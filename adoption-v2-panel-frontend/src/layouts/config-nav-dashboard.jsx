// src/config-nav-dashboard.js

import React, { useState, useEffect } from 'react';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/config-global';
import { useTranslate } from 'src/locales';
import { usePermissions } from 'src/apis/usePermissions';

import { SvgColor } from 'src/components/svg-color';

const icon = (name) => <SvgColor src={`${CONFIG.site.basePath}/assets/icons/navbar/${name}.svg`} />;

// Direkt olarak burada localStorage'a erişmek yerine component içinde yapacağız
// const user = JSON.parse(localStorage.getItem('user'));

const ICONS = {
  home: icon('ic-home'),
  user: icon('ic-users'),
  analytics: icon('ic-analytics'),
  ideation: icon('ic-ideation'),
  aitools: icon('ic-aitools'),
  toolkit: icon('ic-toolkit'),
  translations: icon('ic-translate'),
  permissions: icon('ic-permissions'),
  admins: icon('ic-admins'),
  companies: icon('ic-users'), // Using users icon for companies
  platformSettings: icon('ic-settings'),
  emailTemplates: icon('ic-mail'), // Email templates için icon
};

const NavData = () => {
  const { t } = useTranslate();
  const { getPermissions } = usePermissions();
  const [permissions, setPermissions] = useState({});
  const [items, setItems] = useState([]);
  const [user, setUser] = useState(null);

  // Component mount olduğunda user bilgisini localStorage'dan al
  useEffect(() => {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        setUser(JSON.parse(userData));
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }, []);

  useEffect(() => {
    if (user?.role) {
      getPermissions(user.role, user.company)
        .then((res) => {
          if (res?.perms?.[0]?.nav) {
            setPermissions(res.perms[0].nav);
            console.log('permissions:', res.perms[0].nav);
          }
        })
        .catch((err) => {
          console.error('Error fetching permissions:', err);
        });
    }
  }, [getPermissions, user]);

  useEffect(() => {
    const newItems = [
      {
        items: [
          permissions?.home && {
            title: t('navData.Home'),
            path: paths.dashboard.root,
            icon: ICONS.home,
            disabled: permissions?.home === false,
          },
          permissions?.ideation && {
            title: t('navData.Ideation'),
            path: paths.dashboard.ideation,
            icon: ICONS.ideation,
            disabled: permissions?.ideation === false,
          },
          permissions?.reporting && {
            title: t('navData.Reporting'),
            path: paths.dashboard.reporting,
            icon: ICONS.analytics,
            disabled: permissions?.reporting === false,
          },
          permissions?.users && {
            title: t('navData.Users'),
            path: paths.dashboard.users,
            icon: ICONS.user,
            disabled: permissions?.users === false,
          },

          permissions?.aitools && {
            title: t('navData.AI TOOLS'),
            path: paths.dashboard.aitools,
            icon: ICONS.aitools,
            disabled: permissions?.aitools === false,
          },
          permissions?.aitoolkit && {
            title: t('navData.AIToolkit'),
            path: paths.dashboard.aitoolkit,
            icon: ICONS.toolkit,
            disabled: permissions?.aitoolkit === false,
          },
          // Email Templates - only show if permissions exist
          permissions?.emailTemplates && {
            title: 'Email Templates',
            path: paths.dashboard.emailTemplates.root,
            icon: ICONS.emailTemplates,
            disabled: permissions?.emailTemplates === false,
          },
        ].filter(Boolean),
      },
    ];

    if (user && user?.admin === true) {
      const adminItems = [];
      if (permissions?.translations) {
        adminItems.push({
          title: t('navData.Translations'),
          path: paths.dashboard.translations,
          icon: ICONS.translations,
          disabled: permissions?.translations === false,
        });
      }
      if (permissions.permissions) {
        adminItems.push({
          title: t('navData.Permissions'),
          path: paths.dashboard.permissions,
          icon: ICONS.permissions,
          disabled: permissions?.permissions === false,
        });
      }
      if (permissions.admins) {
        adminItems.push({
          title: t('navData.Admins'),
          path: paths.dashboard.admins,
          icon: ICONS.admins,
          disabled: permissions?.admins === false,
        });
      }
      if (permissions.companies) {
        adminItems.push({
          title: t('navData.Companies'),
          path: paths.dashboard.companies,
          icon: ICONS.companies,
          disabled: permissions?.companies === false,
        });
      }
      if (permissions.platformSettings) {
        adminItems.push({
          title: t('navData.Platform Settings'),
          path: paths.dashboard.platformSettings,
          icon: ICONS.platformSettings,
          disabled: permissions?.platformSettings === false,
        });
      }

      if (adminItems.length > 0) {
        newItems.push({
          subheader: t('navData.Admin Settings'),
          items: adminItems,
        });
      }
    }

    setItems(newItems);
  }, [permissions, t, user]);

  return items;
};

export default NavData;
