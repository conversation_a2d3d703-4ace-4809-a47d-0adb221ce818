import { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Box,
  Card,
  Grid,
  Stack,
  Button,
  Switch,
  Divider,
  Typography,
  IconButton,
  Chip,
  TextField,
  FormControl,
  FormLabel,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

const EmailTemplateSchema = Yup.object().shape({
  name: Yup.string()
    .required('Template name is required')
    .min(3, 'Template name must be at least 3 characters')
    .max(100, 'Template name must be at most 100 characters'),
  subject: Yup.string()
    .required('Subject is required')
    .min(1, 'Subject must be at least 1 character')
    .max(200, 'Subject must be at most 200 characters'),
  htmlContent: Yup.string().required('HTML content is required'),
  textContent: Yup.string(),
  variables: Yup.array().of(
    Yup.object().shape({
      name: Yup.string().required('Variable name is required'),
      defaultValue: Yup.string(),
      description: Yup.string(),
    })
  ),
  tags: Yup.array().of(Yup.string()),
  isActive: Yup.boolean(),
});

export default function EmailTemplateImprovedForm({ currentTemplate, onSubmit }) {
  const [newTag, setNewTag] = useState('');
  const lastProcessedContent = useRef('');
  const isProcessingVariables = useRef(false);

  const defaultValues = useMemo(
    () => ({
      name: currentTemplate?.name || '',
      subject: currentTemplate?.subject || '',
      htmlContent: currentTemplate?.htmlContent || '',
      textContent: currentTemplate?.textContent || '',
      variables: currentTemplate?.variables || [],
      tags: currentTemplate?.tags || [],
      isActive: currentTemplate?.isActive !== undefined ? currentTemplate.isActive : true,
    }),
    [currentTemplate]
  );

  const methods = useForm({
    resolver: yupResolver(EmailTemplateSchema),
    defaultValues,
  });

  const {
    reset,
    watch,
    control,
    setValue,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const values = watch();

  const {
    fields: variableFields,
    append: appendVariable,
    remove: removeVariable,
  } = useFieldArray({
    control,
    name: 'variables',
  });

  // Helper function to generate default values based on variable name
  const generateDefaultValue = useCallback((variableName) => {
    const lowerName = variableName.toLowerCase();

    if (lowerName.includes('name') || lowerName.includes('user')) {
      return 'User';
    }
    if (lowerName.includes('email')) {
      return '<EMAIL>';
    }
    if (lowerName.includes('company')) {
      return 'Company Name';
    }
    if (lowerName.includes('date') || lowerName.includes('time')) {
      return new Date().toLocaleDateString();
    }
    if (lowerName.includes('url') || lowerName.includes('link')) {
      return 'https://example.com';
    }
    if (lowerName.includes('otp') || lowerName.includes('code')) {
      return '123456';
    }
    if (lowerName.includes('year')) {
      return new Date().getFullYear().toString();
    }
    return 'Default Value';
  }, []);

  // Helper function to generate descriptions based on variable name
  const generateDescription = useCallback((variableName) => {
    const lowerName = variableName.toLowerCase();

    if (lowerName.includes('name') || lowerName.includes('user')) {
      return "User's display name";
    }
    if (lowerName.includes('email')) {
      return "User's email address";
    }
    if (lowerName.includes('company')) {
      return 'Company or organization name';
    }
    if (lowerName.includes('date') || lowerName.includes('time')) {
      return 'Date or timestamp';
    }
    if (lowerName.includes('url') || lowerName.includes('link')) {
      return 'URL or web link';
    }
    if (lowerName.includes('otp') || lowerName.includes('code')) {
      return 'One-time password or verification code';
    }
    if (lowerName.includes('year')) {
      return 'Current year';
    }
    return `Dynamic value for ${variableName}`;
  }, []);

  // Improved variable detection with debouncing and duplicate prevention
  const processVariables = useCallback((htmlContent) => {
    if (isProcessingVariables.current || htmlContent === lastProcessedContent.current) {
      return;
    }

    isProcessingVariables.current = true;
    lastProcessedContent.current = htmlContent;

    // Extract all {{variableName}} patterns from HTML content
    const variablePattern = /\{\{([a-zA-Z_][a-zA-Z0-9_]*)\}\}/g;
    const foundVariables = [];
    let match;

    // eslint-disable-next-line no-cond-assign
    while ((match = variablePattern.exec(htmlContent)) !== null) {
      const variableName = match[1];
      if (!foundVariables.includes(variableName)) {
        foundVariables.push(variableName);
      }
    }

    // Get current variable names
    const currentVariables = values.variables || [];
    const currentVariableNames = currentVariables.map((v) => v.name);

    // Find new variables that don't exist in current variables
    const newVariables = foundVariables.filter(
      (varName) => !currentVariableNames.includes(varName) && varName.trim() !== ''
    );

    // Find variables that are in the list but no longer in HTML content
    const removedVariables = currentVariableNames.filter(
      (varName) => !foundVariables.includes(varName) && varName.trim() !== ''
    );

    let hasChanges = false;

    // Add new variables automatically
    if (newVariables.length > 0) {
      newVariables.forEach((varName) => {
        const defaultValue = generateDefaultValue(varName);
        const description = generateDescription(varName);

        appendVariable({
          name: varName,
          defaultValue,
          description,
        });
      });
      hasChanges = true;
    }

    // Remove variables that are no longer in HTML content
    if (removedVariables.length > 0) {
      const updatedVariables = currentVariables.filter(
        (variable) => !removedVariables.includes(variable.name)
      );
      setValue('variables', updatedVariables);
      hasChanges = true;
    }

    // Reset processing flag after a short delay
    setTimeout(() => {
      isProcessingVariables.current = false;
    }, 100);

  }, [values.variables, appendVariable, setValue, generateDefaultValue, generateDescription]);

  // Debounced effect for variable detection
  useEffect(() => {
    const htmlContent = values.htmlContent || '';

    // Only process if content actually changed
    if (htmlContent !== lastProcessedContent.current) {
      const timeoutId = setTimeout(() => {
        processVariables(htmlContent);
      }, 500); // 500ms debounce

      return () => clearTimeout(timeoutId);
    }

    // ESLint consistent-return fix
    return () => {};
  }, [values.htmlContent, processVariables]);

  const handleAddTag = () => {
    if (newTag && !values.tags.includes(newTag)) {
      setValue('tags', [...values.tags, newTag]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setValue(
      'tags',
      values.tags.filter((tag) => tag !== tagToRemove)
    );
  };

  const handleAddVariable = () => {
    appendVariable({ name: '', defaultValue: '', description: '' });
  };

  const handleRemoveVariable = (index) => {
    removeVariable(index);
  };

  const onSubmitForm = async (data) => {
    try {
      await onSubmit(data);
      reset();
    } catch (error) {
      console.error('Submit error:', error);
    }
  };

  const insertVariable = (variableName) => {
    const currentContent = values.htmlContent;
    setValue('htmlContent', `${currentContent}{{${variableName}}}`);
  };

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmitForm)}>
      <Grid container spacing={5} sx={{ mt: 2, px: 2 }}>
        <Grid item xs={12} md={8} lg={9} sx={{ pr: { md: 3 } }}>
          <Stack spacing={3}>
            <Card sx={{ p: 3, mr: { md: 2 } }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Template Information
              </Typography>

              <Stack spacing={3}>
                <Field.Text name="name" label="Template Name" required />

                <Field.Text name="subject" label="Email Subject" required />

                <Box>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    HTML Content *
                  </Typography>
                  <Field.Editor
                    name="htmlContent"
                    sx={{
                      '& .ql-editor': {
                        minHeight: 200,
                      },
                    }}
                  />
                </Box>

                <Field.Text
                  name="textContent"
                  label="Text Content (Optional)"
                  multiline
                  rows={4}
                  helperText="For email clients that do not support HTML"
                />
              </Stack>
            </Card>

            <Card sx={{ p: 3, mr: { md: 2 }, mb: 5 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Variables
              </Typography>

              <Stack spacing={2}>
                {variableFields.map((field, index) => (
                  <Box key={field.id}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3}>
                        <Field.Text
                          name={`variables.${index}.name`}
                          label="Variable Name"
                          placeholder="userName"
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <Field.Text
                          name={`variables.${index}.defaultValue`}
                          label="Default Value"
                          placeholder="User"
                        />
                      </Grid>
                      <Grid item xs={12} sm={5}>
                        <Field.Text
                          name={`variables.${index}.description`}
                          label="Description"
                          placeholder="User's name"
                        />
                      </Grid>
                      <Grid item xs={12} sm={1}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton
                            size="small"
                            color="info"
                            onClick={() => insertVariable(values.variables[index]?.name)}
                            disabled={!values.variables[index]?.name}
                          >
                            <Iconify icon="eva:plus-circle-outline" />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleRemoveVariable(index)}
                          >
                            <Iconify icon="eva:trash-2-outline" />
                          </IconButton>
                        </Box>
                      </Grid>
                    </Grid>
                    {index < variableFields.length - 1 && <Divider sx={{ mt: 2 }} />}
                  </Box>
                ))}

                <Button
                  size="small"
                  color="primary"
                  startIcon={<Iconify icon="mingcute:add-line" />}
                  onClick={handleAddVariable}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Add Variable
                </Button>

                {variableFields.length > 0 && (
                  <Box sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      💡 Use the + button next to the variables to add them to the HTML content.
                      <br />
                      <strong>Variable Format:</strong> All variables must use double curly braces:{' '}
                      {'{{variableName}}'}
                      <br />
                      <strong>Example:</strong> Hello {'{{userName}}'}, welcome to{' '}
                      {'{{companyName}}'}!
                      <br />
                      <strong>🚀 Auto-Detection:</strong> Variables are automatically detected from
                      your HTML content and added to this list with smart default values and
                      descriptions! (Improved with duplicate prevention)
                    </Typography>
                  </Box>
                )}
              </Stack>
            </Card>
          </Stack>
        </Grid>

        <Grid item xs={12} md={4} lg={3} sx={{ pl: { md: 3 } }}>
          <Stack spacing={3}>
            <Card sx={{ p: 3, ml: { md: 2 } }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Settings
              </Typography>

              <Stack spacing={3}>
                <Box>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Status
                  </Typography>
                  <Field.Switch name="isActive" label="Template Active" />
                </Box>

                <Divider />

                <Box>
                  <Typography variant="subtitle2" sx={{ mb: 2 }}>
                    Tags
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {values.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        size="small"
                        onDelete={() => handleRemoveTag(tag)}
                      />
                    ))}
                  </Box>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      size="small"
                      placeholder="Add tag"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddTag();
                        }
                      }}
                    />
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={handleAddTag}
                      disabled={!newTag}
                    >
                      Add
                    </Button>
                  </Box>
                </Box>
              </Stack>
            </Card>

            <Card sx={{ p: 3, ml: { md: 2 } }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Preview
              </Typography>

              <Stack spacing={2}>
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Subject:
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 0.5 }}>
                    {values.subject || 'Subject not entered'}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="caption" color="text.secondary">
                    HTML Content:
                  </Typography>
                  <Box
                    sx={{
                      mt: 0.5,
                      p: 2,
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      bgcolor: 'background.paper',
                      maxHeight: 200,
                      overflow: 'auto',
                    }}
                    dangerouslySetInnerHTML={{
                      __html: values.htmlContent || '<em>Content not entered</em>',
                    }}
                  />
                </Box>
              </Stack>
            </Card>

            <LoadingButton
              type="submit"
              variant="contained"
              size="large"
              loading={isSubmitting}
              startIcon={<Iconify icon="eva:save-fill" />}
            >
              {currentTemplate ? 'Update' : 'Create'}
            </LoadingButton>
          </Stack>
        </Grid>
      </Grid>
    </Form>
  );
}
