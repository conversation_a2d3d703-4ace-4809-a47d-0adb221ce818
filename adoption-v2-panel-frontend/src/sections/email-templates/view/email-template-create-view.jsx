import { useState, useCallback } from 'react';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { Container } from '@mui/material';
import { useSettingsContext } from 'src/components/settings';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { useEmailTemplates } from 'src/apis/useEmailTemplates';
import EmailTemplateNewEditForm from '../email-template-new-edit-form';

// ----------------------------------------------------------------------

export default function EmailTemplateCreateView() {
  const settings = useSettingsContext();
  const router = useRouter();
  const { createTemplate } = useEmailTemplates();

  const handleCreateTemplate = useCallback(
    async (templateData) => {
      const newTemplate = await createTemplate(templateData);
      if (newTemplate) {
        router.push(paths.dashboard.emailTemplates.view(newTemplate._id));
      }
    },
    [createTemplate, router]
  );

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="New Email Template"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Email Templates', href: paths.dashboard.emailTemplates.list },
          { name: 'New Template' },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />

      <EmailTemplateNewEditForm onSubmit={handleCreateTemplate} />
    </Container>
  );
}
