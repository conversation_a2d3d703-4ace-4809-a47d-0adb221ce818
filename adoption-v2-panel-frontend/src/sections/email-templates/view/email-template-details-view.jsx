import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';

import {
  Box,
  Button,
  Card,
  Chip,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';

import { useParams, useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

import { useEmailTemplates } from 'src/apis/useEmailTemplates';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';
import { useSettingsContext } from 'src/components/settings';

import EmailTemplateScheduleForm from '../email-template-schedule-form';

// ----------------------------------------------------------------------

export default function EmailTemplateDetailsView() {
  const params = useParams();
  const settings = useSettingsContext();
  const router = useRouter();

  const { id } = params;

  const {
    template,
    loading,
    fetchTemplate,
    deleteTemplate,
    sendTestEmail,
    stopScheduledJob,
    restartScheduledJob,
    deleteScheduledJob,
  } = useEmailTemplates();

  const [testEmailDialog, setTestEmailDialog] = useState({ open: false });
  const [testEmail, setTestEmail] = useState('');
  const [scheduleDialog, setScheduleDialog] = useState({ open: false });
  const [editScheduleDialog, setEditScheduleDialog] = useState({ open: false, job: null });
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false });
  const [deleteJobConfirm, setDeleteJobConfirm] = useState({
    open: false,
    jobId: null,
    jobName: '',
  });

  useEffect(() => {
    if (id) {
      fetchTemplate(id);
    }
  }, [fetchTemplate, id]);

  const handleEdit = useCallback(() => {
    router.push(paths.dashboard.emailTemplates.edit(id));
  }, [router, id]);

  const handleDelete = useCallback(async () => {
    const success = await deleteTemplate(id);
    if (success) {
      router.push(paths.dashboard.emailTemplates.list);
      toast.success('Template deleted successfully');
    }
    setDeleteConfirm({ open: false });
  }, [deleteTemplate, id, router]);

  const handleSendTestEmail = useCallback(async () => {
    if (testEmail) {
      await sendTestEmail(id, testEmail).then(() => {
        toast.success('Test email sent successfully');
        setTestEmailDialog({ open: false });
        setTestEmail('');
      });
    }
  }, [sendTestEmail, id, testEmail]);

  const handleStopScheduledJob = useCallback(
    async (jobId) => {
      await stopScheduledJob(id, jobId);
    },
    [stopScheduledJob, id]
  );

  const handleRestartScheduledJob = useCallback(
    async (jobId) => {
      await restartScheduledJob(id, jobId);
    },
    [restartScheduledJob, id]
  );

  const handleDeleteScheduledJob = useCallback(async (jobId, jobName) => {
    setDeleteJobConfirm({ open: true, jobId, jobName });
  }, []);

  const confirmDeleteScheduledJob = useCallback(async () => {
    const { jobId } = deleteJobConfirm;
    if (jobId) {
      await deleteScheduledJob(id, jobId);
    }
    setDeleteJobConfirm({ open: false, jobId: null, jobName: '' });
  }, [deleteScheduledJob, id, deleteJobConfirm]);

  const handleEditScheduledJob = useCallback((job) => {
    setEditScheduleDialog({ open: true, job });
  }, []);

  if (loading) {
    return <LoadingScreen />;
  }

  if (!template) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Typography variant="h6" color="error">
          Template not found
        </Typography>
      </Container>
    );
  }

  const renderBasicInfo = (
    <Card sx={{ p: 3, bgcolor: 'background.paper' }}>
      <Stack spacing={3}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h4">{template.name}</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Chip
              label={template.isActive ? 'Active' : 'Inactive'}
              color={template.isActive ? 'success' : 'default'}
              variant="soft"
            />
          </Box>
        </Box>

        <Divider />

        <Grid container spacing={4}>
          <Grid xs={12} md={6}>
            <Box sx={{ ml: 4, mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Email Subject
              </Typography>
              <Typography variant="body1">{template.subject}</Typography>
            </Box>
          </Grid>

          <Grid xs={12} md={6}>
            <Box sx={{ ml: 4, mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Created Date
              </Typography>
              <Typography variant="body1">
                {new Date(template.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Typography>
            </Box>
          </Grid>

          {template.tags && template.tags.length > 0 && (
            <Grid xs={12}>
              <Box sx={{ ml: 4, mt: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Tags
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {template.tags.map((tag, index) => (
                    <Chip key={index} label={tag} size="small" variant="outlined" />
                  ))}
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>
      </Stack>
    </Card>
  );

  const renderContent = (
    <Card sx={{ p: 3, bgcolor: 'background.paper' }}>
      <Typography variant="h6" gutterBottom>
        Email Content
      </Typography>

      <Stack spacing={3}>
        <Box>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            HTML Content
          </Typography>
          <Box
            sx={{
              p: 2,
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              bgcolor: 'background.paper',
              maxHeight: 400,
              overflow: 'auto',
            }}
            dangerouslySetInnerHTML={{ __html: template.htmlContent }}
          />
        </Box>

        {template.textContent && (
          <Box>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Text Content
            </Typography>
            <Typography
              variant="body2"
              sx={{
                p: 2,
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                bgcolor: 'background.neutral',
                whiteSpace: 'pre-wrap',
              }}
            >
              {template.textContent}
            </Typography>
          </Box>
        )}
      </Stack>
    </Card>
  );

  const renderVariables = (
    <Card sx={{ p: 3, bgcolor: 'background.paper' }}>
      <Typography variant="h6" gutterBottom>
        Variables
      </Typography>

      {template.variables && template.variables.length > 0 ? (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Variable Name</TableCell>
                <TableCell>Default Value</TableCell>
                <TableCell>Description</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {template.variables.map((variable, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {`{{${variable.name}}}`}
                    </Typography>
                  </TableCell>
                  <TableCell>{variable.defaultValue || '-'}</TableCell>
                  <TableCell>{variable.description || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Typography variant="body2" color="text.secondary">
          No variables defined for this template.
        </Typography>
      )}
    </Card>
  );

  const renderScheduledJobs = (
    <Card sx={{ p: 3, mb: 5, bgcolor: 'background.paper' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Scheduled Jobs</Typography>
        <Button
          variant="outlined"
          startIcon={<Iconify icon="mingcute:add-line" />}
          onClick={() => setScheduleDialog({ open: true })}
        >
          Add New Task
        </Button>
      </Box>

      {template.scheduledJobs && template.scheduledJobs.length > 0 ? (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Task Name</TableCell>
                <TableCell>Trigger Type</TableCell>
                <TableCell>Schedule/Condition</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Run</TableCell>
                <TableCell>Target Count</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {template.scheduledJobs.map((job, index) => {
                const getTriggerLabel = (triggerType) => {
                  switch (triggerType) {
                    case 'cron':
                      return 'Scheduled';
                    case 'onboarding_complete':
                      return 'Onboarding Complete';
                    case 'onboarding_started':
                      return 'Onboarding Started';
                    case 'role_change':
                      return 'Role Change';
                    case 'login_reminder':
                      return 'Login Reminder';
                    case 'password_reset_requested':
                      return 'Password Reset Requested';
                    case 'password_changed':
                      return 'Password Changed';
                    case 'account_created':
                      return 'Account Created';
                    case 'account_activated':
                      return 'Account Activated';
                    case 'inactivity_warning':
                      return 'Inactivity Warning';
                    case 'subscription_expired':
                      return 'Subscription Expired';
                    case 'form_submitted':
                      return 'Form Submitted';
                    case 'support_ticket_created':
                      return 'Support Ticket Created';
                    case 'course_completed':
                      return 'Course Completed';
                    case 'certificate_earned':
                      return 'Certificate Earned';
                    default:
                      return triggerType || 'Scheduled';
                  }
                };

                const getScheduleInfo = (jobItem) => {
                  switch (jobItem.triggerType) {
                    case 'cron':
                      return (
                        <Typography variant="body2" fontFamily="monospace">
                          {jobItem.cronExpression}
                        </Typography>
                      );
                    case 'login_reminder':
                      return (
                        <Typography variant="body2">
                          After {jobItem.triggerConditions?.reminderDays || 7} days
                        </Typography>
                      );
                    case 'inactivity_warning':
                      return (
                        <Typography variant="body2">
                          After {jobItem.triggerConditions?.reminderDays || 30} days
                        </Typography>
                      );
                    case 'role_change': {
                      const fromRole = jobItem.triggerConditions?.fromRole;
                      const toRole = jobItem.triggerConditions?.toRole;
                      if (fromRole || toRole) {
                        return (
                          <Typography variant="body2">
                            {fromRole ? `From: ${fromRole}` : 'Any role'} →{' '}
                            {toRole ? `To: ${toRole}` : 'Any role'}
                          </Typography>
                        );
                      }
                      return <Typography variant="body2">Any role change</Typography>;
                    }
                    case 'form_submitted': {
                      const formType = jobItem.triggerConditions?.formType;
                      return (
                        <Typography variant="body2">
                          {formType ? `Form: ${formType}` : 'Any form submission'}
                        </Typography>
                      );
                    }
                    case 'course_completed': {
                      const courseId = jobItem.triggerConditions?.courseId;
                      return (
                        <Typography variant="body2">
                          {courseId ? `Course: ${courseId}` : 'Any course completion'}
                        </Typography>
                      );
                    }
                    case 'certificate_earned': {
                      const certificateType = jobItem.triggerConditions?.certificateType;
                      return (
                        <Typography variant="body2">
                          {certificateType ? `Type: ${certificateType}` : 'Any certificate earned'}
                        </Typography>
                      );
                    }
                    case 'onboarding_complete':
                      return <Typography variant="body2">On completion</Typography>;
                    case 'onboarding_started':
                      return <Typography variant="body2">On start</Typography>;
                    case 'password_reset_requested':
                      return <Typography variant="body2">On request</Typography>;
                    case 'password_changed':
                      return <Typography variant="body2">On change</Typography>;
                    case 'account_created':
                      return <Typography variant="body2">On creation</Typography>;
                    case 'account_activated':
                      return <Typography variant="body2">On activation</Typography>;
                    case 'subscription_expired':
                      return <Typography variant="body2">On expiration</Typography>;
                    case 'support_ticket_created':
                      return <Typography variant="body2">On creation</Typography>;
                    default:
                      return <Typography variant="body2">-</Typography>;
                  }
                };

                const getTargetCount = (jobItem) => {
                  if (jobItem.targetConditions?.includeAllUsers) {
                    return 'All Users';
                  }
                  return `${(jobItem.targetUsers?.length || 0) + (jobItem.targetEmails?.length || 0)} targets`;
                };

                return (
                  <TableRow key={index}>
                    <TableCell>{job.name}</TableCell>
                    <TableCell>
                      <Chip
                        label={getTriggerLabel(job.triggerType)}
                        size="small"
                        variant="outlined"
                        color={job.triggerType === 'cron' ? 'primary' : 'secondary'}
                      />
                    </TableCell>
                    <TableCell>{getScheduleInfo(job)}</TableCell>
                    <TableCell>
                      <Chip
                        label={job.isActive ? 'Active' : 'Stopped'}
                        color={job.isActive ? 'success' : 'default'}
                        size="small"
                        variant="soft"
                      />
                    </TableCell>
                    <TableCell>
                      {job.lastRun
                        ? new Date(job.lastRun).toLocaleDateString('en-US')
                        : 'Not run yet'}
                    </TableCell>
                    <TableCell>{getTargetCount(job)}</TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEditScheduledJob(job)}
                        >
                          <Iconify icon="solar:pen-bold" />
                        </IconButton>
                        {job.isActive ? (
                          <IconButton
                            size="small"
                            color="warning"
                            onClick={() => handleStopScheduledJob(job._id)}
                            title="Stop Job"
                          >
                            <Iconify icon="solar:stop-circle-bold" />
                          </IconButton>
                        ) : (
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleRestartScheduledJob(job._id)}
                            title="Start Job"
                          >
                            <Iconify icon="solar:play-circle-bold" />
                          </IconButton>
                        )}
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteScheduledJob(job._id, job.name)}
                          title="Delete Job Permanently"
                        >
                          <Iconify icon="solar:trash-bin-trash-bold" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Typography variant="body2" color="text.secondary">
          No scheduled tasks found for this template.
        </Typography>
      )}
    </Card>
  );

  const renderActions = (
    <Card sx={{ p: 3, ml: { md: 1 }, height: '94%', bgcolor: 'background.paper' }}>
      <Typography variant="h6" gutterBottom>
        Actions
      </Typography>

      <Stack spacing={2}>
        <Button
          variant="contained"
          startIcon={<Iconify icon="solar:pen-bold" />}
          onClick={handleEdit}
        >
          Edit Template
        </Button>

        <Button
          variant="outlined"
          startIcon={<Iconify icon="solar:letter-bold" />}
          onClick={() => setTestEmailDialog({ open: true })}
        >
          Send Test Email
        </Button>

        <Button
          variant="outlined"
          color="error"
          startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
          onClick={() => setDeleteConfirm({ open: true })}
        >
          Delete Template
        </Button>
      </Stack>
    </Card>
  );

  return (
    <>
      <Container maxWidth={settings.themeStretch ? false : 'xl'}>
        <CustomBreadcrumbs
          heading="Email Template Details"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Email Templates', href: paths.dashboard.emailTemplates.list },
            { name: template.name },
          ]}
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />
        <Grid container spacing={4} sx={{ mt: 1 }}>
          <Grid xs={12} md={8} lg={8} sx={{ mb: 2 }}>
            {renderBasicInfo}
          </Grid>
          <Grid xs={12} md={4} lg={4}>
            {renderActions}
          </Grid>
          <Grid xs={12} md={12} lg={12}>
            <Stack spacing={3}>
              {renderContent}
              {renderVariables}
              {renderScheduledJobs}
            </Stack>
          </Grid>
        </Grid>
      </Container>

      {/* Test Email Dialog */}
      <Dialog
        open={testEmailDialog.open}
        onClose={() => setTestEmailDialog({ open: false })}
        sx={{
          '& .MuiDialog-paper': { minWidth: '400px', width: '400px', maxWidth: '400px' },
        }}
      >
        <DialogTitle>Send Test Email</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestEmailDialog({ open: false })}>Cancel</Button>
          <Button onClick={handleSendTestEmail} variant="contained">
            Send
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schedule Dialog */}
      <Dialog
        open={scheduleDialog.open}
        onClose={() => setScheduleDialog({ open: false })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>New Scheduled Task</DialogTitle>
        <DialogContent>
          <EmailTemplateScheduleForm
            templateId={id}
            onSuccess={() => {
              setScheduleDialog({ open: false });
              fetchTemplate(id); // Refresh template data
            }}
            onCancel={() => setScheduleDialog({ open: false })}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Schedule Dialog */}
      <Dialog
        open={editScheduleDialog.open}
        onClose={() => setEditScheduleDialog({ open: false, job: null })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Edit Scheduled Task</DialogTitle>
        <DialogContent>
          <EmailTemplateScheduleForm
            templateId={id}
            currentJob={editScheduleDialog.job}
            onSuccess={() => {
              setEditScheduleDialog({ open: false, job: null });
              fetchTemplate(id); // Refresh template data
            }}
            onCancel={() => setEditScheduleDialog({ open: false, job: null })}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false })}
        title="Delete Template"
        content={
          <>
            Are you sure you want to delete this template?
            <br />
            <strong>{template.name}</strong>
            <br />
            This action cannot be undone and all scheduled tasks will be stopped.
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={handleDelete}>
            Delete
          </Button>
        }
      />

      {/* Delete Job Confirmation */}
      <ConfirmDialog
        open={deleteJobConfirm.open}
        onClose={() => setDeleteJobConfirm({ open: false, jobId: null, jobName: '' })}
        title="Delete Scheduled Task"
        content={
          <>
            Are you sure you want to delete this scheduled task?
            <br />
            <strong>{deleteJobConfirm.jobName}</strong>
            <br />
            This action cannot be undone and the task will be stopped.
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={confirmDeleteScheduledJob}>
            Delete
          </Button>
        }
      />
    </>
  );
}
