import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  Table,
  Button,
  Container,
  TableBody,
  TableCell,
  TableRow,
  TableContainer,
  Typography,
  Chip,
  IconButton,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Checkbox,
} from '@mui/material';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useSettingsContext } from 'src/components/settings';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import {
  useTable,
  emptyRows,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';
import { CustomPopover, usePopover } from 'src/components/custom-popover';
import { useEmailTemplates } from 'src/apis/useEmailTemplates';
import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'name', label: 'Template Name' },
  { id: 'subject', label: 'Subject' },
  { id: 'tags', label: 'Tags' },
  { id: 'isActive', label: 'Status' },
  { id: 'createdAt', label: 'Created At' },
  { id: 'scheduledJobs', label: 'Scheduled Jobs' },
  { id: '', width: 88 },
];

const defaultFilters = {
  name: '',
  isActive: 'all',
};

// ----------------------------------------------------------------------

export default function EmailTemplateListView() {
  const table = useTable();
  const settings = useSettingsContext();
  const router = useRouter();
  const popover = usePopover();

  const { templates, loading, fetchTemplates, deleteTemplate, sendTestEmail } = useEmailTemplates();

  const [filters, setFilters] = useState(defaultFilters);
  const [testEmailDialog, setTestEmailDialog] = useState({ open: false, template: null });
  const [testEmail, setTestEmail] = useState('');

  const dataFiltered = applyFilter({
    inputData: templates,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const dataInPage = dataFiltered.slice(
    table.page * table.rowsPerPage,
    table.page * table.rowsPerPage + table.rowsPerPage
  );

  const denseHeight = table.dense ? 52 : 72;
  const canReset = !!filters.name || filters.isActive !== 'all';
  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  const handleFilters = useCallback(
    (name, value) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleDeleteRow = useCallback(
    async (id) => {
      await deleteTemplate(id);
      table.onUpdatePageDeleteRow(dataInPage.length);
    },
    [deleteTemplate, dataInPage.length, table]
  );

  const handleEditRow = useCallback(
    (id) => {
      router.push(paths.dashboard.emailTemplates.edit(id));
    },
    [router]
  );

  const handleViewRow = useCallback(
    (id) => {
      router.push(paths.dashboard.emailTemplates.view(id));
    },
    [router]
  );

  const handleTestEmail = useCallback(
    (template) => {
      setTestEmailDialog({ open: true, template });
      popover.onClose();
    },
    [popover]
  );

  const handleSendTestEmail = useCallback(async () => {
    if (testEmailDialog.template && testEmail) {
      await sendTestEmail(testEmailDialog.template._id, testEmail);
      setTestEmailDialog({ open: false, template: null });
      setTestEmail('');
    }
  }, [testEmailDialog.template, testEmail, sendTestEmail]);

  const renderFilters = (
    <Box
      sx={{
        p: 2.5,
        pr: { xs: 2.5, md: 1 },
      }}
    >
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: 'repeat(1, 1fr)',
            md: 'repeat(2, 1fr)',
          },
          gap: 2,
        }}
      >
        <TextField
          fullWidth
          value={filters.name}
          onChange={(event) => handleFilters('name', event.target.value)}
          placeholder="Template name..."
          InputProps={{
            startAdornment: <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />,
          }}
        />

        <TextField
          fullWidth
          select
          value={filters.isActive}
          onChange={(event) => handleFilters('isActive', event.target.value)}
          InputProps={{
            startAdornment: <Iconify icon="eva:filter-fill" sx={{ color: 'text.disabled' }} />,
          }}
        >
          <MenuItem value="all">All Statuses</MenuItem>
          <MenuItem value="true">Active</MenuItem>
          <MenuItem value="false">Inactive</MenuItem>
        </TextField>
      </Box>
    </Box>
  );

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <>
      <Container maxWidth={settings.themeStretch ? false : 'xl'}>
        <CustomBreadcrumbs
          heading="Email Templates"
          links={[{ name: 'Dashboard', href: paths.dashboard.root }, { name: 'Email Templates' }]}
          action={
            <Button
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
              onClick={() => router.push(paths.dashboard.emailTemplates.new)}
            >
              New Template
            </Button>
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />

        <Card sx={{ bgcolor: 'background.paper' }}>
          {renderFilters}

          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <TableSelectedAction
              dense={table.dense}
              numSelected={table.selected.length}
              rowCount={dataFiltered.length}
              onSelectAllRows={(checked) =>
                table.onSelectAllRows(
                  checked,
                  dataFiltered.map((row) => row._id)
                )
              }
              action={
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    color="error"
                    variant="contained"
                    startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
                    onClick={() => {
                      // Bulk delete implementation
                    }}
                  >
                    Delete
                  </Button>
                </Box>
              }
            />

            <Scrollbar>
              <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headLabel={TABLE_HEAD}
                  rowCount={dataFiltered.length}
                  numSelected={table.selected.length}
                  onSort={table.onSort}
                  onSelectAllRows={(checked) =>
                    table.onSelectAllRows(
                      checked,
                      dataFiltered.map((row) => row._id)
                    )
                  }
                />

                <TableBody>
                  {dataFiltered
                    .slice(
                      table.page * table.rowsPerPage,
                      table.page * table.rowsPerPage + table.rowsPerPage
                    )
                    .map((row) => (
                      <EmailTemplateTableRow
                        key={row._id}
                        row={row}
                        selected={table.selected.includes(row._id)}
                        onSelectRow={() => table.onSelectRow(row._id)}
                        onDeleteRow={() => handleDeleteRow(row._id)}
                        onEditRow={() => handleEditRow(row._id)}
                        onViewRow={() => handleViewRow(row._id)}
                        onTestEmail={() => handleTestEmail(row)}
                      />
                    ))}

                  <TableEmptyRows
                    height={denseHeight}
                    emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)}
                  />

                  <TableNoData notFound={notFound} />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            count={dataFiltered.length}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage}
            dense={table.dense}
            onChangeDense={table.onChangeDense}
          />
        </Card>
      </Container>

      <Dialog
        open={testEmailDialog.open}
        onClose={() => setTestEmailDialog({ open: false, template: null })}
      >
        <DialogTitle>Send Test Email</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestEmailDialog({ open: false, template: null })}>
            Cancel
          </Button>
          <Button onClick={handleSendTestEmail} variant="contained">
            Send
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

// ----------------------------------------------------------------------

function EmailTemplateTableRow({
  row,
  selected,
  onSelectRow,
  onViewRow,
  onEditRow,
  onDeleteRow,
  onTestEmail,
}) {
  const [openConfirm, setOpenConfirm] = useState(false);
  const popover = usePopover();

  const handleOpenConfirm = useCallback(() => {
    setOpenConfirm(true);
    popover.onClose();
  }, [popover]);

  const handleCloseConfirm = useCallback(() => {
    setOpenConfirm(false);
  }, []);

  return (
    <>
      <TableRow hover selected={selected} sx={{ cursor: 'pointer' }}>
        <TableCell padding="checkbox">
          <Checkbox checked={selected} onChange={onSelectRow} />
        </TableCell>

        <TableCell onClick={onViewRow}>
          <Typography variant="subtitle2" noWrap>
            {row.name}
          </Typography>
        </TableCell>

        <TableCell onClick={onViewRow}>
          <Typography variant="body2" noWrap>
            {row.subject}
          </Typography>
        </TableCell>

        <TableCell onClick={onViewRow}>
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {row.tags?.slice(0, 2).map((tag, index) => (
              <Chip key={index} label={tag} size="small" variant="soft" />
            ))}
            {row.tags?.length > 2 && (
              <Chip label={`+${row.tags.length - 2}`} size="small" variant="outlined" />
            )}
          </Box>
        </TableCell>

        <TableCell onClick={onViewRow}>
          <Chip
            label={row.isActive ? 'Active' : 'Inactive'}
            variant="soft"
            color={row.isActive ? 'success' : 'default'}
          />
        </TableCell>

        <TableCell onClick={onViewRow}>
          <Typography variant="body2">
            {new Date(row.createdAt).toLocaleDateString('en-US')}
          </Typography>
        </TableCell>

        <TableCell onClick={onViewRow}>
          <Chip
            label={`${row.scheduledJobs?.filter((job) => job.isActive).length || 0} Active`}
            size="small"
            variant="soft"
            color="info"
          />
        </TableCell>

        <TableCell align="right">
          <IconButton color={popover.open ? 'inherit' : 'default'} onClick={popover.onOpen}>
            <Iconify icon="eva:more-vertical-fill" />
          </IconButton>
        </TableCell>
      </TableRow>

      <CustomPopover
        open={popover.open}
        anchorEl={popover.anchorEl}
        onClose={popover.onClose}
        slotProps={{
          arrow: { placement: 'right-top' },
        }}
        sx={{ width: 160 }}
      >
        <MenuItem
          onClick={() => {
            onViewRow();
            popover.onClose();
          }}
        >
          <Iconify icon="solar:eye-bold" />
          View
        </MenuItem>

        <MenuItem
          onClick={() => {
            onEditRow();
            popover.onClose();
          }}
        >
          <Iconify icon="solar:pen-bold" />
          Edit
        </MenuItem>

        <MenuItem
          onClick={() => {
            onTestEmail();
            popover.onClose();
          }}
        >
          <Iconify icon="solar:letter-bold" />
          Test Email
        </MenuItem>

        <MenuItem onClick={handleOpenConfirm} sx={{ color: 'error.main' }}>
          <Iconify icon="solar:trash-bin-trash-bold" />
          Delete
        </MenuItem>
      </CustomPopover>

      <ConfirmDialog
        open={openConfirm}
        onClose={handleCloseConfirm}
        title="Delete Template"
        content="Are you sure you want to delete this template?"
        action={
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              onDeleteRow();
              handleCloseConfirm();
            }}
          >
            Delete
          </Button>
        }
      />
    </>
  );
}

// ----------------------------------------------------------------------

function applyFilter({ inputData, comparator, filters }) {
  const { name, isActive } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index]);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (template) => template.name.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  if (isActive !== 'all') {
    inputData = inputData.filter((template) => template.isActive.toString() === isActive);
  }

  return inputData;
}
