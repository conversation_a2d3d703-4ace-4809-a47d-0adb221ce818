import { useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'src/routes/hooks';

import { paths } from 'src/routes/paths';
import { Container, Typography } from '@mui/material';
import { useSettingsContext } from 'src/components/settings';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { LoadingScreen } from 'src/components/loading-screen';
import { useEmailTemplates } from 'src/apis/useEmailTemplates';
import EmailTemplateNewEditForm from '../email-template-new-edit-form';

// ----------------------------------------------------------------------

export default function EmailTemplateEditView() {
  const params = useParams();
  const settings = useSettingsContext();
  const router = useRouter();

  const { id } = params;

  const { template, loading, fetchTemplate, updateTemplate } = useEmailTemplates();

  useEffect(() => {
    if (id) {
      fetchTemplate(id);
    }
  }, [fetchTemplate, id]);

  const handleUpdateTemplate = useCallback(
    async (templateData) => {
      const updatedTemplate = await updateTemplate(id, templateData);
      if (updatedTemplate) {
        router.push(paths.dashboard.emailTemplates.view(id));
      }
    },
    [updateTemplate, id, router]
  );

  if (loading) {
    return <LoadingScreen />;
  }

  if (!template) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Typography variant="h6" color="error">
          Template not found
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Edit Email Template"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Email Templates', href: paths.dashboard.emailTemplates.list },
          { name: template.name, href: paths.dashboard.emailTemplates.view(id) },
          { name: 'Edit' },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />

      <EmailTemplateNewEditForm currentTemplate={template} onSubmit={handleUpdateTemplate} />
    </Container>
  );
}
