import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Box,
  Stack,
  Button,
  TextField,
  Typography,
  MenuItem,
  Chip,
  Autocomplete,
  DialogActions,
  FormControlLabel,
  Switch,
  Alert,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { Form, Field } from 'src/components/hook-form';
import { useEmailTemplates } from 'src/apis/useEmailTemplates';
import { useUser } from 'src/apis/useUser';

// ----------------------------------------------------------------------

const ScheduleSchema = Yup.object().shape({
  name: Yup.string().required('Task name is required'),
  triggerType: Yup.string().required('Trigger type is required'),
  cronExpression: Yup.string().when('triggerType', {
    is: 'cron',
    then: (schema) => schema.required('Cron expression is required for cron trigger'),
    otherwise: (schema) => schema.notRequired(),
  }),
  reminderDays: Yup.number().when('triggerType', {
    is: 'login_reminder',
    then: (schema) =>
      schema.min(1, 'Reminder days must be at least 1').required('Reminder days is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  targetEmails: Yup.array().of(Yup.string().email('Please enter a valid email address')),
  targetUsers: Yup.array(),
});

const cronPresets = [
  { label: 'Daily at 09:00', value: '0 9 * * *' },
  { label: 'Every Monday at 09:00', value: '0 9 * * 1' },
  { label: 'First day of every month at 09:00', value: '0 9 1 * *' },
  { label: 'Every hour', value: '0 * * * *' },
  { label: 'Every 30 minutes', value: '*/30 * * * *' },
  { label: 'Weekdays at 18:00', value: '0 18 * * 1-5' },
];

const triggerTypes = [
  {
    value: 'cron',
    label: 'Scheduled (Cron)',
    description: 'Send emails based on time schedule',
  },
  {
    value: 'onboarding_complete',
    label: 'Onboarding Complete',
    description: 'Send email when user completes onboarding',
  },
  {
    value: 'onboarding_started',
    label: 'Onboarding Started',
    description: 'Send email when user starts onboarding process',
  },
  {
    value: 'role_change',
    label: 'Role Change',
    description: 'Send email when user role changes',
  },
  {
    value: 'login_reminder',
    label: 'Login Reminder',
    description: 'Send reminder after period of inactivity',
  },
  {
    value: 'password_reset_requested',
    label: 'Password Reset Requested',
    description: 'Send email when user requests password reset',
  },
  {
    value: 'password_changed',
    label: 'Password Changed',
    description: 'Send confirmation when password is changed',
  },
  {
    value: 'account_created',
    label: 'Account Created',
    description: 'Send welcome email when new account is created',
  },
  {
    value: 'account_activated',
    label: 'Account Activated',
    description: 'Send confirmation when account is activated',
  },
  {
    value: 'inactivity_warning',
    label: 'Inactivity Warning',
    description: 'Send warning before account deactivation due to inactivity',
  },
  {
    value: 'subscription_expired',
    label: 'Subscription Expired',
    description: 'Send notification when subscription expires',
  },
  {
    value: 'form_submitted',
    label: 'Form Submitted',
    description: 'Send confirmation when user submits a form',
  },
  {
    value: 'support_ticket_created',
    label: 'Support Ticket Created',
    description: 'Send notification when support ticket is created',
  },
  {
    value: 'course_completed',
    label: 'Course Completed',
    description: 'Send congratulations when user completes a course',
  },
  {
    value: 'certificate_earned',
    label: 'Certificate Earned',
    description: 'Send notification when user earns a certificate',
  },
];

export default function EmailTemplateScheduleForm({
  templateId,
  currentJob = null, // For edit mode
  onSuccess,
  onCancel,
}) {
  const [emailInput, setEmailInput] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [emailList, setEmailList] = useState([]);

  const { addScheduledJob, updateScheduledJob } = useEmailTemplates();
  const { users, fetchUsers } = useUser();

  const isEditMode = Boolean(currentJob);

  const defaultValues = {
    name: currentJob?.name || '',
    triggerType: currentJob?.triggerType || 'cron',
    cronExpression: currentJob?.cronExpression || '',
    reminderDays: currentJob?.triggerConditions?.reminderDays || 7,
    fromRole: currentJob?.triggerConditions?.fromRole || '',
    toRole: currentJob?.triggerConditions?.toRole || '',
    includeAllUsers: currentJob?.targetConditions?.includeAllUsers || false,
    targetEmails: currentJob?.targetEmails || [],
    targetUsers: currentJob?.targetUsers || [],
  };

  const methods = useForm({
    resolver: yupResolver(ScheduleSchema),
    defaultValues,
  });

  const {
    reset,
    setValue,
    watch,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const values = watch();

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Load existing job data when in edit mode
  useEffect(() => {
    if (isEditMode && currentJob) {
      setValue('name', currentJob.name);
      setValue('cronExpression', currentJob.cronExpression);

      // Set target emails
      if (currentJob.targetEmails) {
        setEmailList(currentJob.targetEmails);
        setValue('targetEmails', currentJob.targetEmails);
      }

      // Set target users
      if (currentJob.targetUsers && users) {
        const selectedUserObjects = users.filter((user) =>
          currentJob.targetUsers.some((targetUser) =>
            typeof targetUser === 'string' ? targetUser === user._id : targetUser._id === user._id
          )
        );
        setSelectedUsers(selectedUserObjects);
        setValue(
          'targetUsers',
          selectedUserObjects.map((user) => user._id)
        );
      }
    }
  }, [isEditMode, currentJob, users, setValue]);

  const handleAddEmail = () => {
    if (emailInput && !emailList.includes(emailInput)) {
      const newEmailList = [...emailList, emailInput];
      setEmailList(newEmailList);
      setValue('targetEmails', newEmailList);
      setEmailInput('');
    }
  };

  const handleRemoveEmail = (emailToRemove) => {
    const newEmailList = emailList.filter((email) => email !== emailToRemove);
    setEmailList(newEmailList);
    setValue('targetEmails', newEmailList);
  };

  const handleUserChange = (event, newValue) => {
    setSelectedUsers(newValue);
    setValue(
      'targetUsers',
      newValue.map((user) => user._id)
    );
  };

  const onSubmit = async (data) => {
    try {
      const jobData = {
        name: data.name,
        triggerType: data.triggerType,
        targetUsers: selectedUsers.map((user) => user._id),
        targetEmails: emailList,
        isActive: true,
      };

      // Add trigger-specific data
      if (data.triggerType === 'cron') {
        jobData.cronExpression = data.cronExpression;
      } else if (data.triggerType === 'login_reminder') {
        jobData.triggerConditions = {
          reminderDays: data.reminderDays,
        };
      } else if (data.triggerType === 'role_change') {
        jobData.triggerConditions = {
          fromRole: data.fromRole || undefined,
          toRole: data.toRole || undefined,
        };
      } else if (data.triggerType === 'inactivity_warning') {
        jobData.triggerConditions = {
          reminderDays: data.reminderDays || 30,
        };
      } else if (data.triggerType === 'form_submitted') {
        jobData.triggerConditions = {
          formType: data.formType || undefined,
        };
      } else if (data.triggerType === 'course_completed') {
        jobData.triggerConditions = {
          courseId: data.courseId || undefined,
        };
      } else if (data.triggerType === 'certificate_earned') {
        jobData.triggerConditions = {
          certificateType: data.certificateType || undefined,
        };
      }

      // Add target conditions
      jobData.targetConditions = {
        includeAllUsers: data.includeAllUsers || false,
      };

      if (isEditMode) {
        await updateScheduledJob(templateId, currentJob._id, jobData);
      } else {
        await addScheduledJob(templateId, jobData);
      }

      reset();
      setEmailList([]);
      setSelectedUsers([]);
      onSuccess();
    } catch (error) {
      console.error('Submit error:', error);
    }
  };

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
      <Stack spacing={3} sx={{ pt: 2 }}>
        <Field.Text name="name" label="Task Name" placeholder="Weekly Newsletter" required />

        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            Trigger Type *
          </Typography>
          <Field.Select name="triggerType" label="Select Trigger Type">
            {triggerTypes.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    {type.label}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {type.description}
                  </Typography>
                </Box>
              </MenuItem>
            ))}
          </Field.Select>
        </Box>

        {/* Cron-specific fields */}
        {values.triggerType === 'cron' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Cron Expression *
            </Typography>
            <Field.Select name="cronExpression" label="Select Schedule">
              {cronPresets.map((preset) => (
                <MenuItem key={preset.value} value={preset.value}>
                  {preset.label}
                </MenuItem>
              ))}
            </Field.Select>

            <Field.Text
              name="cronExpression"
              label="Custom Cron Expression"
              placeholder="0 9 * * *"
              helperText="Select one of the options above or enter a custom cron expression"
              sx={{ mt: 2 }}
            />
          </Box>
        )}

        {/* Login reminder specific fields */}
        {values.triggerType === 'login_reminder' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Reminder Settings
            </Typography>
            <Field.Text
              name="reminderDays"
              label="Days After Last Login"
              type="number"
              placeholder="7"
              helperText="Send reminder after this many days of inactivity"
            />
            <Alert severity="info" sx={{ mt: 2 }}>
              This will send a reminder email to users who haven&apos;t logged in for the specified
              number of days.
            </Alert>
          </Box>
        )}

        {/* Role change specific fields */}
        {values.triggerType === 'role_change' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Role Change Settings
            </Typography>
            <Stack spacing={2}>
              <Field.Text
                name="fromRole"
                label="From Role (Optional)"
                placeholder="e.g., user"
                helperText="Leave empty to trigger on any role change"
              />
              <Field.Text
                name="toRole"
                label="To Role (Optional)"
                placeholder="e.g., admin"
                helperText="Leave empty to trigger on any role change"
              />
            </Stack>
            <Alert severity="info" sx={{ mt: 2 }}>
              This will send an email when a user&apos;s role changes. Leave fields empty to trigger
              on any role change.
            </Alert>
          </Box>
        )}

        {/* Onboarding complete info */}
        {values.triggerType === 'onboarding_complete' && (
          <Alert severity="info">
            This will automatically send an email when a user completes their onboarding process.
          </Alert>
        )}

        {/* Onboarding started info */}
        {values.triggerType === 'onboarding_started' && (
          <Alert severity="info">
            This will automatically send an email when a user starts their onboarding process.
          </Alert>
        )}

        {/* Password reset requested info */}
        {values.triggerType === 'password_reset_requested' && (
          <Alert severity="info">
            This will automatically send an email when a user requests a password reset.
          </Alert>
        )}

        {/* Password changed info */}
        {values.triggerType === 'password_changed' && (
          <Alert severity="info">
            This will automatically send a confirmation email when a user&apos;s password is
            successfully changed.
          </Alert>
        )}

        {/* Account created info */}
        {values.triggerType === 'account_created' && (
          <Alert severity="info">
            This will automatically send a welcome email when a new user account is created.
          </Alert>
        )}

        {/* Account activated info */}
        {values.triggerType === 'account_activated' && (
          <Alert severity="info">
            This will automatically send a confirmation email when a user account is activated.
          </Alert>
        )}

        {/* Inactivity warning specific fields */}
        {values.triggerType === 'inactivity_warning' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Inactivity Warning Settings
            </Typography>
            <Field.Text
              name="reminderDays"
              label="Days of Inactivity"
              type="number"
              placeholder="30"
              helperText="Send warning after this many days of inactivity"
            />
            <Alert severity="warning" sx={{ mt: 2 }}>
              This will send a warning email to users who haven&apos;t been active for the specified
              number of days.
            </Alert>
          </Box>
        )}

        {/* Subscription expired info */}
        {values.triggerType === 'subscription_expired' && (
          <Alert severity="info">
            This will automatically send a notification when a user&apos;s subscription expires.
          </Alert>
        )}

        {/* Form submitted info */}
        {values.triggerType === 'form_submitted' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Form Submission Settings
            </Typography>
            <Field.Text
              name="formType"
              label="Form Type (Optional)"
              placeholder="e.g., contact, feedback, support"
              helperText="Specify which form type should trigger this email (leave empty for all forms)"
            />
            <Alert severity="info" sx={{ mt: 2 }}>
              This will send a confirmation email when users submit forms. You can specify a
              particular form type or leave it empty to trigger on all form submissions.
            </Alert>
          </Box>
        )}

        {/* Support ticket created info */}
        {values.triggerType === 'support_ticket_created' && (
          <Alert severity="info">
            This will automatically send a notification when a support ticket is created.
          </Alert>
        )}

        {/* Course completed info */}
        {values.triggerType === 'course_completed' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Course Completion Settings
            </Typography>
            <Field.Text
              name="courseId"
              label="Course ID (Optional)"
              placeholder="e.g., course-101"
              helperText="Specify which course completion should trigger this email (leave empty for all courses)"
            />
            <Alert severity="info" sx={{ mt: 2 }}>
              This will send a congratulations email when users complete courses. You can specify a
              particular course or leave it empty to trigger on all course completions.
            </Alert>
          </Box>
        )}

        {/* Certificate earned info */}
        {values.triggerType === 'certificate_earned' && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Certificate Settings
            </Typography>
            <Field.Text
              name="certificateType"
              label="Certificate Type (Optional)"
              placeholder="e.g., completion, achievement"
              helperText="Specify which certificate type should trigger this email (leave empty for all certificates)"
            />
            <Alert severity="info" sx={{ mt: 2 }}>
              This will send a notification when users earn certificates. You can specify a
              particular certificate type or leave it empty to trigger on all certificate earnings.
            </Alert>
          </Box>
        )}

        {/* Target Settings */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Target Settings
          </Typography>

          {(values.triggerType === 'onboarding_complete' ||
            values.triggerType === 'role_change' ||
            values.triggerType === 'login_reminder') && (
            <FormControlLabel
              control={<Field.Switch name="includeAllUsers" />}
              label="Include All Users"
              sx={{ mb: 2, ml: 1 }}
            />
          )}

          {!values.includeAllUsers && (
            <>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Specific Users
              </Typography>
              <Autocomplete
                multiple
                options={users || []}
                getOptionLabel={(option) => `${option.name} (${option.email})`}
                value={selectedUsers}
                onChange={handleUserChange}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      {...getTagProps({ index })}
                      key={option._id}
                      label={option.name}
                      size="small"
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select users"
                    helperText="You can select registered users from the system"
                  />
                )}
              />
            </>
          )}
        </Box>

        <Box>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Additional Email Addresses
          </Typography>

          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <TextField
              fullWidth
              type="email"
              placeholder="Add email address"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddEmail();
                }
              }}
            />
            <Button variant="outlined" onClick={handleAddEmail} disabled={!emailInput}>
              Add
            </Button>
          </Box>

          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {emailList.map((email, index) => (
              <Chip
                key={index}
                label={email}
                onDelete={() => handleRemoveEmail(email)}
                size="small"
              />
            ))}
          </Box>
        </Box>

        {(selectedUsers.length > 0 || emailList.length > 0 || values.includeAllUsers) && (
          <Box sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Total Targets: </strong>
              {values.includeAllUsers
                ? 'All Users'
                : `${selectedUsers.length + emailList.length} recipients`}
            </Typography>
          </Box>
        )}
      </Stack>

      <DialogActions sx={{ px: 0, pt: 3 }}>
        <Button onClick={onCancel}>Cancel</Button>
        <LoadingButton
          type="submit"
          variant="contained"
          loading={isSubmitting}
          disabled={!values.includeAllUsers && selectedUsers.length === 0 && emailList.length === 0}
        >
          {isEditMode ? 'Update Scheduled Task' : 'Create Scheduled Task'}
        </LoadingButton>
      </DialogActions>
    </Form>
  );
}
