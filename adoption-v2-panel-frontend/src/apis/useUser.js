/* eslint-disable consistent-return */
import axios from 'axios';
import { useState, useCallback } from 'react';

import { CONFIG } from 'src/config-global';

export const useUser = () => {
  const [userData, setUserData] = useState(null);
  const [users, setUsers] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const authToken = localStorage.getItem('accessToken');

  const updateAccount = useCallback(
    async (name, surname, email) => {
      setLoading(true);
      try {
        const response = await axios.post(
          `${CONFIG.site.serverUrl}/api/users/updateAccount`,
          { name, surname, email },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setUserData(response.data);
        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        setUserData(null);
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const getUsers = useCallback(
    async (companyId = null) => {
      setLoading(true);
      try {
        let url = `${CONFIG.site.serverUrl}/api/users`;

        // Add company parameter to the request if provided
        if (companyId) {
          url = `${url}?company=${companyId}`;
        }

        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        const responseData = response.data.data || response.data;
        setUsers(responseData);
        setError(null);
        return responseData;
      } catch (err) {
        console.error('Error fetching users:', err);
        setError(err);
        setUsers(null);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const getUserById = useCallback(
    async (userId) => {
      setLoading(true);
      try {
        const response = await axios.get(`${CONFIG.site.serverUrl}/api/users/${userId}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setError(null);
        return response.data.data || response.data;
      } catch (err) {
        setError(err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const updateUser = useCallback(
    async (userId, userDataToUpdate) => {
      setLoading(true);
      try {
        const response = await axios.patch(
          `${CONFIG.site.serverUrl}/api/users/${userId}`,
          userDataToUpdate,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        setError(null);
        return response.data;
      } catch (err) {
        setError(err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const deleteUser = useCallback(
    async (userId) => {
      setLoading(true);
      try {
        const response = await axios.delete(`${CONFIG.site.serverUrl}/api/users/${userId}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        setError(null);
        return response.data;
      } catch (err) {
        console.error('Error deleting user:', err);
        setError(err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [authToken]
  );

  const updateUsersRole = useCallback(
    async (userIds, role) => {
      setLoading(true);
      try {
        // Create an array to store results
        const results = [];
        let success = true;
        let errorMessage = '';

        // Process all users using Promise.all with map
        const updatePromises = userIds.map(async (userId) => {
          try {
            // Get user data first
            const userInfo = await getUserById(userId);

            if (userInfo) {
              // Update only the role field
              const updatedData = { ...userInfo, role };

              // Send update request
              const updateResponse = await updateUser(userId, updatedData);

              if (updateResponse) {
                return { userId, success: true };
              }
              return { userId, success: false, error: 'Update failed' };
            }
            return { userId, success: false, error: 'User not found' };
          } catch (userError) {
            console.error(`Error updating user ${userId}:`, userError);
            return { userId, success: false, error: userError.message || 'Unknown error' };
          }
        });

        // Wait for all updates to complete
        const updateResults = await Promise.all(updatePromises);

        // Add results and check if any failed
        updateResults.forEach((result) => {
          results.push(result);
          if (!result.success) {
            success = false;
            if (result.error === 'Update failed') {
              errorMessage = 'Some updates failed';
            } else if (result.error === 'User not found') {
              errorMessage = 'Some users not found';
            } else {
              errorMessage = 'Error processing some users';
            }
          }
        });

        setError(null);
        return {
          success,
          status: success ? 'Success' : 'Error',
          message: success
            ? `Successfully updated ${results.filter((r) => r.success).length} users`
            : errorMessage,
          results,
        };
      } catch (err) {
        console.error('Error in updateUsersRole:', err);
        setError(err);
        return {
          success: false,
          status: 'Error',
          message: err.message || 'Unknown error occurred',
          results: [],
        };
      } finally {
        setLoading(false);
      }
    },
    [getUserById, updateUser]
  );

  return {
    updateAccount,
    getUsers,
    fetchUsers: getUsers,
    getUserById,
    updateUser,
    deleteUser,
    updateUsersRole,
    userData,
    users,
    loading,
    error,
  };
};
