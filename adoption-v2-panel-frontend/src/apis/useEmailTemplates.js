import { useState, useCallback } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { CONFIG } from '../config-global';

const API_BASE_URL = CONFIG.site.serverUrl;

export const useEmailTemplates = () => {
  const [templates, setTemplates] = useState([]);
  const [template, setTemplate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // API Header yapılandırması
  const getHeaders = () => ({
    'Content-Type': 'application/json',
    'x-api-key': import.meta.env.VITE_API_KEY || '',
  });

  // Tüm template'leri getir
  const fetchTemplates = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${API_BASE_URL}/api/email-templates`, {
        headers: getHeaders(),
      });

      if (response.data.success) {
        setTemplates(response.data.data);
      } else {
        throw new Error(response.data.message || 'Templates could not be retrieved');
      }
    } catch (err) {
      setError(err.message);
      toast.error(`Error fetching templates: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Template detayını getir
  const fetchTemplate = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${API_BASE_URL}/api/email-templates/${id}`, {
        headers: getHeaders(),
      });

      if (response.data.success) {
        setTemplate(response.data.data);
        return response.data.data;
      }
      throw new Error(response.data.message || 'Template could not be retrieved');
    } catch (err) {
      setError(err.message);
      toast.error(`Error fetching template: ${err.message}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Yeni template oluştur
  const createTemplate = useCallback(
    async (templateData) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.post(`${API_BASE_URL}/api/email-templates`, templateData, {
          headers: getHeaders(),
        });

        if (response.data.success) {
          toast.success('Template created successfully');
          await fetchTemplates(); // Template listesini yenile
          return response.data.data;
        }
        throw new Error(response.data.message || 'Template could not be created');
      } catch (err) {
        setError(err.message);
        toast.error(`Error creating template: ${err.message}`);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplates]
  );

  // Template güncelle
  const updateTemplate = useCallback(
    async (id, templateData) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.put(
          `${API_BASE_URL}/api/email-templates/${id}`,
          templateData,
          { headers: getHeaders() }
        );

        if (response.data.success) {
          toast.success('Template updated successfully');
          await fetchTemplates(); // Template listesini yenile
          return response.data.data;
        }
        throw new Error(response.data.message || 'Template could not be updated');
      } catch (err) {
        setError(err.message);
        toast.error(`Error updating template: ${err.message}`);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplates]
  );

  // Template sil
  const deleteTemplate = useCallback(
    async (id) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.delete(`${API_BASE_URL}/api/email-templates/${id}`, {
          headers: getHeaders(),
        });

        if (response.data.success) {
          toast.success('Template deleted successfully');
          await fetchTemplates(); // Template listesini yenile
          return true;
        }
        throw new Error(response.data.message || 'Template could not be deleted');
      } catch (err) {
        setError(err.message);
        toast.error(`Error deleting template: ${err.message}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplates]
  );

  // Test email gönder
  const sendTestEmail = useCallback(async (id, testEmail, variables = {}) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/email-templates/${id}/test`,
        { testEmail, variables },
        { headers: getHeaders() }
      );

      if (response.data.success) {
        toast.success('Test email sent successfully');
        return true;
      }
      throw new Error(response.data.message || 'Test email could not be sent');
    } catch (err) {
      setError(err.message);
      toast.error(`Error sending test email: ${err.message}`);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Scheduled job ekle
  const addScheduledJob = useCallback(
    async (id, jobData) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/email-templates/${id}/schedule`,
          jobData,
          { headers: getHeaders() }
        );

        if (response.data.success) {
          toast.success('Scheduled job added successfully');
          await fetchTemplate(id); // Template detayını yenile
          return response.data.data;
        }
        throw new Error(response.data.message || 'Scheduled job could not be added');
      } catch (err) {
        setError(err.message);
        toast.error(`Error adding scheduled job: ${err.message}`);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplate]
  );

  // Scheduled job güncelle
  const updateScheduledJob = useCallback(
    async (id, jobId, jobData) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.put(
          `${API_BASE_URL}/api/email-templates/${id}/schedule/${jobId}`,
          jobData,
          { headers: getHeaders() }
        );

        if (response.data.success) {
          toast.success('Scheduled job updated successfully');
          await fetchTemplate(id); // Template detayını yenile
          return response.data.data;
        }
        throw new Error(response.data.message || 'Scheduled job could not be updated');
      } catch (err) {
        setError(err.message);
        toast.error(`Error updating scheduled job: ${err.message}`);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplate]
  );

  // Scheduled job durdur
  const stopScheduledJob = useCallback(
    async (id, jobId) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.delete(
          `${API_BASE_URL}/api/email-templates/${id}/schedule/${jobId}`,
          { headers: getHeaders() }
        );

        if (response.data.success) {
          toast.success('Scheduled job stopped successfully');
          await fetchTemplate(id); // Template detayını yenile
          return true;
        }
        throw new Error(response.data.message || 'Scheduled job could not be stopped');
      } catch (err) {
        setError(err.message);
        toast.error(`Error stopping scheduled job: ${err.message}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplate]
  );

  // Scheduled job yeniden başlat
  const restartScheduledJob = useCallback(
    async (id, jobId) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/email-templates/${id}/schedule/${jobId}/restart`,
          {},
          { headers: getHeaders() }
        );

        if (response.data.success) {
          toast.success('Scheduled job restarted successfully');
          await fetchTemplate(id); // Template detayını yenile
          return true;
        }
        throw new Error(response.data.message || 'Scheduled job could not be restarted');
      } catch (err) {
        setError(err.message);
        toast.error(`Error restarting scheduled job: ${err.message}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplate]
  );

  // Scheduled job sil
  const deleteScheduledJob = useCallback(
    async (id, jobId) => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.delete(
          `${API_BASE_URL}/api/email-templates/${id}/schedule/${jobId}/remove`,
          { headers: getHeaders() }
        );

        if (response.data.success) {
          toast.success('Scheduled job deleted successfully');
          await fetchTemplate(id); // Template detayını yenile
          return true;
        }
        throw new Error(response.data.message || 'Scheduled job could not be deleted');
      } catch (err) {
        setError(err.message);
        toast.error(`Error deleting scheduled job: ${err.message}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [fetchTemplate]
  );

  return {
    // State
    templates,
    template,
    loading,
    error,

    // Actions
    fetchTemplates,
    fetchTemplate,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    sendTestEmail,
    addScheduledJob,
    updateScheduledJob,
    stopScheduledJob,
    restartScheduledJob,
    deleteScheduledJob,

    // Helpers
    setTemplate,
    setError,
  };
};
