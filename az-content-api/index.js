require("dotenv").config();
const express = require("express");
const http = require("http");
const cors = require("cors");
const mongoose = require("mongoose");
const ENUM = require("./src/utils/enum");
const apiKeyMiddleware = require("./src/middlewares/apiKeyMiddleware");
const config = require("./config");
const swagger = require("./src/docs/swagger");

const app = express();

// Compile regex patterns only once (performance optimization)
const aibsPattern = /^https:\/\/[a-zA-Z0-9\-]+\.aibusinessschool\.com$/;

const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      "http://localhost:5173",
      "http://localhost:4173",
      "http://localhost:5174",
      "http://127.0.0.1:5500",
      "http://127.0.0.1:4173",
      "https://coursera.org",
      "https://*.cloudfront.net",
    ];

    // Allow if no origin (same-origin requests) or origin is in allowed list
    if (
      !origin ||
      allowedOrigins.includes(origin) ||
      aibsPattern.test(origin)
    ) {
      callback(null, true);
    } else {
      callback(new Error("CORS policy violation"));
    }
  },
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "x-api-key"],
  credentials: true,
};

app.use(cors(corsOptions));

// MIDDLEWARES
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb" }));

app.use(apiKeyMiddleware);

// Swagger UI
app.use("/api/v1/docs", swagger.serve, swagger.setup);

// API versioning
const v1Routes = require("./src/routes/v1");

// Define API routes
app.use("/api/v1", v1Routes);

// Default version redirect
app.get("/api", (req, res) => {
  res.redirect("/api/v1");
});

// Unauthorized access control
app.get("/api", (req, res) => {
  res
    .status(ENUM.HTTP_CODES.FORBIDDEN)
    .json({ message: "Unauthorized access" });
});

const httpServer = http.createServer(app);

// MongoDB connection
mongoose.connect(config.MONGODB_URI).catch((error) => {
  console.log("MongoDB connection error:", error);
});

// Start HTTP servers
app.listen(config.PORT, () => {
  console.log(`API v1 server started on port: ${config.PORT}`);
});

httpServer.listen(9981, () => {
  console.log("HTTP Server running on port 9981");
});

// For future v2 API implementation:
/*
const v2Routes = require("./src/routes/v2");
app.use("/api/v2", v2Routes);
app.use("/api/v2/docs", swaggerUi.serve, swaggerUi.setup(swaggerSpecsV2));
*/
