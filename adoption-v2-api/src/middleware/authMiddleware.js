const jwt = require("jsonwebtoken");
const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");
const config = require("../../config");
const UserModel = require("../models/UserModel");
const sessionStore = require("../utils/sessionStore");

const AuthMiddleware = (requireAdmin = false, skipSessionUpdate = false) => {
  return (req, res, next) => {
    if (req.isApiKeyAuthenticated) {
      return next();
    }

    try {
      const authHeader = req.headers["authorization"];

      if (!authHeader) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.UNAUTHORIZED,
          "error",
          "Access Denied: No Token Provided"
        );
      }

      const token = authHeader.split(" ")[1];

      jwt.verify(token, config.JWT_SECRET, async (err, decoded) => {
        if (err) {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.UNAUTHORIZED,
            "error",
            "Invalid Token",
            err.message
          );
        }

        req.user = decoded;

        const findUserID = await UserModel.findOne({ email: req.user.email });

        if (!findUserID) {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.UNAUTHORIZED,
            "error",
            "User not found",
            null
          );
        }

        console.log("AuthMiddleware - User found:", {
          id: findUserID._id,
          email: findUserID.email,
        });

        // Add session check (but don't update if skipSessionUpdate is true)
        if (skipSessionUpdate) {
          // Just check if session exists without timeout check for testing
          const session = sessionStore.getSession(findUserID._id);
          if (!session || !session.isActive) {
            return httpResponse(
              res,
              ENUM.HTTP_CODES.UNAUTHORIZED,
              "error",
              "No active session found",
              "SESSION_EXPIRED"
            );
          }
        } else {
          // Normal session check with timeout and update
          const isSessionActive = sessionStore.isSessionActive(findUserID._id);
          if (!isSessionActive) {
            return httpResponse(
              res,
              ENUM.HTTP_CODES.UNAUTHORIZED,
              "error",
              "Session expired or user logged out from another location",
              "SESSION_EXPIRED"
            );
          }

          // Update session (last activity time)
          sessionStore.updateSession(findUserID._id);
        }

        // Check if admin privileges are required
        if (requireAdmin) {
          const isAdmin =
            findUserID.role === "admin" ||
            findUserID.role === "Administrator" ||
            findUserID.role === "SUPER_USER";

          if (!isAdmin) {
            return httpResponse(
              res,
              ENUM.HTTP_CODES.FORBIDDEN,
              "error",
              "This action requires admin privileges",
              null
            );
          }
        }

        // Add user information to req (for logout and other operations)
        req.user.userId = findUserID._id;
        req.user.userRole = findUserID.role;

        next();
      });
    } catch (error) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.INT_SERVER_ERROR,
        "error",
        "Authentication error",
        error.message
      );
    }
  };
};

module.exports = AuthMiddleware;
