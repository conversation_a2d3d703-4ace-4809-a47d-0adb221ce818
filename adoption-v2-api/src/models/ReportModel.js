const mongoose = require("mongoose");

const ReportSchema = mongoose.Schema(
  {
    company: { type: mongoose.Schema.Types.ObjectId, ref: "Company" },
    companyName: { type: String, required: true },
    reportType: {
      type: String,
      enum: [
        "training",
        "nextgen",
        "ideation",
        "creator",
        "application",
        "ai-value",
        "ms-training",
      ],
      required: true,
    },
    reportData: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
    },
    downloadLink: { type: String, required: false },
    createdAt: { type: Date, default: Date.now, required: true },
  },
  { versionKey: false }
);

module.exports = mongoose.model("Report", ReportSchema);
