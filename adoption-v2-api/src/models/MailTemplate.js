const mongoose = require("mongoose");

const MailTemplateSchema = mongoose.Schema(
  {
    name: { type: String, required: true, unique: true },
    subject: { type: String, required: true },
    htmlContent: { type: String, required: true },
    textContent: { type: String },
    variables: [
      {
        name: String,
        defaultValue: String,
        description: String,
      },
    ],
    isActive: { type: Boolean, default: true },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "Users" },
    tags: [String],
    scheduledJobs: [
      {
        name: String,
        // Trigger type: 'cron', 'onboarding_complete', 'role_change', 'login_reminder'
        triggerType: {
          type: String,
          enum: [
            "cron",
            "onboarding_complete",
            "onboarding_started",
            "role_change",
            "login_reminder",
            "password_reset_requested",
            "password_changed",
            "account_created",
            "account_activated",
            "inactivity_warning",
            "subscription_expired",
            "form_submitted",
            "support_ticket_created",
            "course_completed",
            "certificate_earned",
          ],
          default: "cron",
        },
        // For cron-based scheduling
        cronExpression: String,
        // For event-based scheduling
        triggerConditions: {
          // For role_change trigger
          fromRole: String, // Previous role
          toRole: String, // New role
          // For login_reminder trigger
          reminderDays: { type: Number, default: 7 }, // Days after last login
          // For onboarding_complete trigger
          onboardingSteps: [String], // Specific onboarding steps completed
        },
        isActive: { type: Boolean, default: true },
        targetUsers: [{ type: mongoose.Schema.Types.ObjectId, ref: "Users" }],
        targetEmails: [String],
        // For dynamic targeting based on conditions
        targetConditions: {
          includeAllUsers: { type: Boolean, default: false },
          filterByRole: [String], // Target users with specific roles
          filterByCompany: [
            { type: mongoose.Schema.Types.ObjectId, ref: "companies" },
          ],
          excludeUsers: [
            { type: mongoose.Schema.Types.ObjectId, ref: "Users" },
          ],
        },
        lastRun: Date,
        nextRun: Date,
        // Track which users have been processed for event-based triggers
        processedUsers: [
          {
            userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users" },
            processedAt: { type: Date, default: Date.now },
            triggerEvent: String, // What triggered the email
          },
        ],
      },
    ],
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

module.exports = mongoose.model("MailTemplate", MailTemplateSchema);
