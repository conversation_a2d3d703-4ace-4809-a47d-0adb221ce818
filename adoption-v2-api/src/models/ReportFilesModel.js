const mongoose = require("mongoose");

const ReportFilesSchema = new mongoose.Schema({
  reportId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Report",
    required: true,
  },
  status: {
    type: String,
    enum: ["pending", "processing", "ready", "failed"],
    default: "pending",
    required: true,
  },
  fileUrl: {
    type: String,
  },
  fileName: {
    type: String,
  },
  error: {
    type: String,
  },
  requestedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Users",
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: {
    type: Date,
  },
});

const ReportFilesModel = mongoose.model("ReportFiles", ReportFilesSchema);

module.exports = ReportFilesModel;
