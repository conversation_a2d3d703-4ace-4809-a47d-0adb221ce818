const mongoose = require("mongoose");

const TranslationSchema = mongoose.Schema(
  {
    en: String,
    de: String,
    fr: String,
  },
  { _id: false }
);

const OnboardingFieldSchema = mongoose.Schema(
  {
    slug: String,
    translations: TranslationSchema,
    _id: String,
    order: Number,
    description: String,
  },
  { _id: false }
);

const OnboardingSchema = mongoose.Schema({
  function_label: {
    type: mongoose.Schema.Types.Mixed,
    set: function (value) {
      if (typeof value === "string") {
        return value;
      }
      return value;
    },
    default: null,
  },
  job_role_label: {
    type: mongoose.Schema.Types.Mixed,
    set: function (value) {
      if (typeof value === "string") {
        return value;
      }
      return value;
    },
    default: null,
  },
  management_role_label: {
    type: mongoose.Schema.Types.Mixed,
    set: function (value) {
      if (typeof value === "string") {
        return value === "yes";
      } else if (typeof value === "boolean") {
        return value;
      }
      return value;
    },
    default: null,
  },
  technical_background_label: {
    type: mongoose.Schema.Types.Mixed,
    set: function (value) {
      if (typeof value === "string") {
        return value;
      }
      return value;
    },
    default: null,
  },
  ai_knowledge_label: {
    type: mongoose.Schema.Types.Mixed,
    set: function (value) {
      if (typeof value === "string") {
        return value;
      }
      return value;
    },
    default: null,
  },
  industry_label: {
    type: mongoose.Schema.Types.Mixed,
    set: function (value) {
      if (typeof value === "string") {
        return value;
      }
      return value;
    },
    default: null,
  },
  language: {
    type: String,
    default: "en",
  },
  create_date: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

OnboardingSchema.pre("save", function (next) {
  if (this.isNew) {
    this.create_date = new Date();
  }
  if (this.isModified()) {
    this.updated_at = new Date();
  }
  next();
});

const JourneyLevelSchema = mongoose.Schema({
  name: String, //slug
  translations: {
    en: String,
    de: String,
  },
  // skipped journey will be an array of levels
  skippedJourney: {
    type: [String],
    default: [],
    validate: {
      validator: function (array) {
        const allowedValues = ["beginner", "expert", "master"];
        return array.every((item) => allowedValues.includes(item));
      },
      message: "skippedJourney can only contain: beginner, expert, master",
    },
  },
  allJourneyCompleted: {
    type: Boolean,
    default: false,
  },
  create_date: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
});

JourneyLevelSchema.pre("save", function (next) {
  if (this.isNew) {
    this.create_date = new Date();
  }
  if (this.isModified()) {
    this.updated_at = new Date();
  }
  next();
});

const UserSchema = mongoose.Schema(
  {
    name: String,
    surname: String,
    email: String,
    username: String,
    password: String,
    role: String,
    previous_role: String,
    company: { type: mongoose.Schema.Types.ObjectId, ref: "companies" },
    platformName: String,
    platformUrl: String,
    status: String,
    token: String,
    language: {
      type: String,
      default: "en",
    },
    create_date: String,
    lastLogin: { type: Date },
    admin: Boolean,
    onboarding: OnboardingSchema,
    sso_login: Boolean,
    journeyLevel: JourneyLevelSchema,
  },
  { versionKey: false }
);

module.exports = mongoose.model("Users", UserSchema);
