const express = require("express");
const router = express.Router();
const { body } = require("express-validator");
const EmailTemplateController = require("../controllers/EmailTemplateController");

// Validation middlewares
const createTemplateValidation = [
  body("name")
    .notEmpty()
    .withMessage("Template name is required")
    .isLength({ min: 3, max: 100 })
    .withMessage("Template name must be between 3 and 100 characters"),
  body("subject")
    .notEmpty()
    .withMessage("Subject is required")
    .isLength({ min: 1, max: 200 })
    .withMessage("Subject must be between 1 and 200 characters"),
  body("htmlContent").notEmpty().withMessage("HTML content is required"),
];

const testEmailValidation = [
  body("testEmail").isEmail().withMessage("Valid email address is required"),
];

const scheduledJobValidation = [
  body("name").notEmpty().withMessage("Job name is required"),
  body("triggerType").notEmpty().withMessage("Trigger type is required"),
];

// Routes

// POST /api/email-templates/migrate - Migrate existing templates (development only)
router.post("/migrate", EmailTemplateController.migrateExistingTemplates);

// POST /api/email-templates/fix-variables - Manual fix for template variables
router.post("/fix-variables", EmailTemplateController.fixTemplateVariables);

// GET /api/email-templates - Get all templates
router.get("/", EmailTemplateController.getAllTemplates);

// GET /api/email-templates/:id - Get template details
router.get("/:id", EmailTemplateController.getTemplateById);

// POST /api/email-templates - Create new template
router.post(
  "/",
  createTemplateValidation,
  EmailTemplateController.createTemplate
);

// PUT /api/email-templates/:id - Update template
router.put("/:id", EmailTemplateController.updateTemplate);

// DELETE /api/email-templates/:id - Delete template
router.delete("/:id", EmailTemplateController.deleteTemplate);

// POST /api/email-templates/:id/test - Send test email
router.post(
  "/:id/test",
  testEmailValidation,
  EmailTemplateController.sendTestEmail
);

// POST /api/email-templates/:id/schedule - Add scheduled job
router.post(
  "/:id/schedule",
  scheduledJobValidation,
  EmailTemplateController.addScheduledJob
);

// PUT /api/email-templates/:id/schedule/:jobId - Update scheduled job
router.put(
  "/:id/schedule/:jobId",
  scheduledJobValidation,
  EmailTemplateController.updateScheduledJob
);

// DELETE /api/email-templates/:id/schedule/:jobId - Stop scheduled job
router.delete("/:id/schedule/:jobId", EmailTemplateController.stopScheduledJob);

// POST /api/email-templates/:id/schedule/:jobId/restart - Restart scheduled job
router.post(
  "/:id/schedule/:jobId/restart",
  EmailTemplateController.restartScheduledJob
);

// DELETE /api/email-templates/:id/schedule/:jobId/remove - Delete scheduled job completely
router.delete(
  "/:id/schedule/:jobId/remove",
  EmailTemplateController.deleteScheduledJob
);

module.exports = router;
