const express = require("express");
const router = express.Router();
const ReportFilesController = require("../controllers/ReportFilesController");
const { body } = require("express-validator");
const authMiddleware = require("../middleware/authMiddleware");

// Generate Excel file from report
router.post(
  "/generate",
  [
    authMiddleware(),
    body("reportId")
      .notEmpty()
      .withMessage("Report ID is required")
      .isMongoId()
      .withMessage("Invalid report ID format"),
  ],
  ReportFilesController.generateExcel
);

// Get status of Excel file generation
router.get("/status/:id", authMiddleware(), ReportFilesController.getStatus);

// List user's Excel file requests
router.get("/", authMiddleware(), ReportFilesController.list);

// Download Excel file
router.get("/download/:id", authMiddleware(), ReportFilesController.download);

module.exports = router;
