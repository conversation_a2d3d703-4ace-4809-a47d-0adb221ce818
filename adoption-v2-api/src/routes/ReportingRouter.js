const express = require("express");
const router = express.Router();
const reportingController = require("../controllers/ReportingController");
const authMiddleware = require("../middleware/authMiddleware");
const validatePagination = require("../middleware/validatePagination");

/**
 * POST Report Routes - Generate new reports
 */
router.post(
  "/generate/:type",
  authMiddleware(),
  reportingController.createReport
);

// Specific report generation endpoints
router.post("/generate/training", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/nextgen", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/application", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/ai-value", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/ideation", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/creator", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/ms-training", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.get(
  "/list/:reportType/:companyId",
  authMiddleware(),
  (req, res, next) => {
    return reportingController.getReportFromModel(req, res, next);
  }
);
/** get all reports */
router.get("/list/:reportType/", authMiddleware(), (req, res, next) => {
  return reportingController.getAllReportsByType(req, res, next);
});

module.exports = router;
