const express = require("express");
const {
  createUser,
  login,
  loginWithToken,
  verifyAccount,
  forgotPassword,
  forgotPasswordChange,
  checkOTP,
  changePassword,
  definePassword,
  me,
  resetPassword,
  logout,
  adminLogoutUser,
  getActiveSessions,
  getMySession,
  getMySessionNoUpdate,
} = require("../controllers/AuthController");
const AuthMiddleware = require("../middleware/authMiddleware");

const router = express.Router();

router.route("/sign-up").post(createUser);
router.route("/sign-in").post(login);
router.route("/me").get(me);
router.route("/login-with-token").post(loginWithToken);
router.route("/verifyAccount").post(verifyAccount);
router.route("/forgotPassword").post(forgotPassword);
router.route("/forgotPasswordChange").post(forgotPasswordChange);
router.route("/checkOTP").post(checkOTP);
router.route("/changePassword").post(changePassword);
router.route("/definePassword").post(definePassword);
router.route("/resetPassword").post(resetPassword);

// Session Management
router.route("/logout").post(AuthMiddleware(), logout);
router.route("/my-session").get(AuthMiddleware(), getMySession);
router
  .route("/my-session-no-update")
  .get(AuthMiddleware(false, true), getMySessionNoUpdate);
router
  .route("/admin/logout/:userId")
  .post(AuthMiddleware(true), adminLogoutUser);
router.route("/admin/sessions").get(AuthMiddleware(true), getActiveSessions);

module.exports = router;
