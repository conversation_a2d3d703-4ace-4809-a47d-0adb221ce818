const User = require("../models/UserModel");
const { httpResponse, sendEmail } = require("../utils/helpers");
const jwt = require("jsonwebtoken");
require("dotenv").config();
const fs = require("fs");
const path = require("path");

const OTPModel = require("../models/OTPModel");
const md5 = require("md5");
const sha256 = require("sha256");
const MailTemplate = require("../models/MailTemplate");
const emailContent = require("../utils/emailTemplates/emailContent");
const AppTrackingModel = require("../models/AppTrackingModel");
const CertificateModel = require("../models/CertificateModel");
const CourseTrackingModel = require("../models/CourseTrackingModel");
const FormResponseModel = require("../models/FormResponse");
const JourneyTrackingModel =
  require("../models/TrackingModel").JourneyTrackingModel;

const QuizProgressModel = require("../models/QuizProgress");
const ShortcutModel = require("../models/ShortcutsModel");
const WorkflowModel = require("../models/WorkflowModel");
const UsecaseModel = require("../models/SimpleAppModel");
const {
  handleRoleChange,
  handleOnboardingComplete,
  sendEmailByTemplate,
} = require("./EmailTemplateController");

exports.allUsers = async (req, res, next) => {
  try {
    const { company } = req.query;
    if (company) {
      const response = await User.find({ company });
      return httpResponse(res, 200, "Success", "All Users", response);
    } else {
      const response = await User.find();
      return httpResponse(res, 200, "Success", "All Users", response);
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.getUserbyEmail = async (req, res, next) => {
  const { email } = req.query;

  try {
    const checkUser = await User.findOne({ email });

    if (checkUser !== undefined) {
      return httpResponse(res, 200, "Success", "User Information", checkUser);
    } else {
      return httpResponse(res, 404, "error", "User not found");
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.deleteUser = async (req, res, next) => {
  const { id } = req.params;
  try {
    const deletedUser = await User.findByIdAndDelete(id);
    const deleteUserOnboardingData = await User.deleteMany(
      { _id: id },
      { onboarding: null }
    );
    const deleteJourneyLevelData = await User.deleteMany(
      { _id: id },
      {
        journeyLevel: {
          name: "beginner",
          translations: {
            en: "Beginner",
            de: "Anfänger",
          },
          skippedJourney: [],
          allJourneyCompleted: false,
        },
      }
    );
    const deleteUserAppTrackingData = await AppTrackingModel.deleteMany({
      userId: id,
    });
    const deleteUserCertificate = await CertificateModel.deleteMany({
      userId: id,
    });
    const deleteUserCourseTrackingData = await CourseTrackingModel.deleteMany({
      user: id,
    });
    const deleteUserFormResponse = await FormResponseModel.deleteMany({
      submittedBy: id,
    });

    const deleteJourneyTrackingData = await JourneyTrackingModel.deleteMany({
      _id: id,
    });

    const deleteUserQuizProgressData = await QuizProgressModel.deleteMany({
      userId: id,
    });
    const deleteUserShortcutsData = await ShortcutModel.deleteMany({
      userId: id,
    });
    const deleteUserUsecasesData = await UsecaseModel.deleteMany({
      userId: id,
    });

    const deleteUserWorkflowData = await WorkflowModel.deleteMany({
      userId: id,
    });

    return httpResponse(res, 200, "success", "User Deleted", {
      deletedUser,
      deleteUserOnboardingData,
      deleteJourneyLevelData,
      deleteUserAppTrackingData,
      deleteUserCertificate,
      deleteUserCourseTrackingData,
      deleteUserFormResponse,
      deleteJourneyTrackingData,
      deleteUserQuizProgressData,
      deleteUserShortcutsData,
      deleteUserUsecasesData,
      deleteUserWorkflowData,
    });
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};
exports.getUserById = async (req, res, next) => {
  const { id } = req.params;
  try {
    const user = await User.findById({ _id: id });
    return httpResponse(res, 200, "Success", "User Information", user);
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.updateUserById = async (req, res, next) => {
  const { id } = req.params;
  const {
    name,
    surname,
    email,
    language,
    status,
    admin,
    role,
    company,
    platformName,
    platformUrl,
    onboarding,
    journeyLevel,
  } = req.body;

  try {
    // Get current user to check if role is changing
    const currentUser = await User.findById(id);
    if (!currentUser) {
      return httpResponse(res, 404, "error", "User not found");
    }

    const updateData = {
      name,
      surname,
      email,
      language,
      status,
      admin,
      role,
      company,
      platformName,
      platformUrl,
      onboarding,
      journeyLevel,
    };

    let roleChanged = false;
    let fromRole = null;
    let toRole = null;

    // If role is changing, store the previous role and trigger role change event
    if (role && role !== currentUser.role) {
      updateData.previous_role = currentUser.role;
      roleChanged = true;
      fromRole = currentUser.role;
      toRole = role;
    }

    // Check if onboarding is being completed
    let onboardingCompleted = false;
    if (
      onboarding &&
      !currentUser.onboarding?.allJourneyCompleted &&
      onboarding.allJourneyCompleted === true
    ) {
      onboardingCompleted = true;
    }

    // Remove undefined fields
    Object.keys(updateData).forEach(
      (key) => updateData[key] === undefined && delete updateData[key]
    );

    const updatedUser = await User.findByIdAndUpdate({ _id: id }, updateData, {
      new: true,
    });

    // Trigger email events asynchronously
    if (roleChanged) {
      try {
        await handleRoleChange(id, fromRole, toRole);
      } catch (emailError) {
        console.error("Error triggering role change email:", emailError);
        // Don't fail the update if email fails
      }
    }

    if (onboardingCompleted) {
      try {
        await handleOnboardingComplete(id);
      } catch (emailError) {
        console.error(
          "Error triggering onboarding complete email:",
          emailError
        );
        // Don't fail the update if email fails
      }
    }

    return httpResponse(res, 200, "Success", "User Information", updatedUser);
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.inviteUsers = async (req, res, next) => {
  const { name, surname, email, company, platformName, platformUrl, mailLang } =
    req.body;

  try {
    const userExist = await User.findOne({ email });

    var token = jwt.sign({ email: email }, process.env.JWT_SECRET, {
      expiresIn: "24h",
    });

    const randomPassword = Math.random().toString(36).slice(-8);
    const hashedPassword = md5(md5(randomPassword) + sha256(randomPassword));

    if (userExist !== null) {
      return httpResponse(res, 200, "Success", "User already exists");
    } else {
      const createUser = await User.create({
        name: name,
        surname: surname,
        email: email,
        password: hashedPassword,
        company: company,
        role: "Administrator",
        platformName: platformName,
        platformUrl: platformUrl,
        status: "Sending",
        token: token,
      });

      var tokenn = jwt.sign({ email: email }, process.env.JWT_SECRET, {
        expiresIn: "24h",
      });

      const verificationLink = `https://${platformUrl}/auth/jwt/sign-in?token=${tokenn}`;

      const selectedLanguage = mailLang === "de" ? "de" : "en";
      const content = emailContent[selectedLanguage];

      // Use template system for invitation email
      try {
        await sendEmailByTemplate("User Invitation", email, {
          greeting: content.greeting(name),
          welcomeMessage: content.welcomeMessage,
          activationMessage: content.activationMessage,
          buttonText: content.buttonText,
          linkValidityMessage: content.linkValidityMessage,
          supportMessage: content.supportMessage,
          verificationLink: verificationLink,
          name: name,
        });

        // OTP oluşturma
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        await OTPModel.create({ email: email, otp });

        return httpResponse(
          res,
          201,
          "Success",
          "User Invited",
          createUser?.email
        );
      } catch (emailError) {
        console.error("Error sending invitation email:", emailError);

        // Fallback to old method if template system fails
        const mailTemplatePath = path.join(
          __dirname,
          "../utils/emailTemplates/inviteMail.html"
        );
        const mailTemplate = fs.readFileSync(mailTemplatePath, "utf8");

        const personalizedMail = mailTemplate
          .replace("{{greeting}}", content.greeting(name))
          .replace("{{welcomeMessage}}", content.welcomeMessage)
          .replace("{{activationMessage}}", content.activationMessage)
          .replace("{{buttonText}}", content.buttonText)
          .replace("{{linkValidityMessage}}", content.linkValidityMessage)
          .replace("{{supportMessage}}", content.supportMessage)
          .replace("{{verificationLink}}", verificationLink);

        // OTP oluşturma
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        await OTPModel.create({ email: email, otp });

        // E-posta gönderme
        await sendEmail({
          from: "<EMAIL>",
          to: email,
          subject: content.subject,
          html: personalizedMail,
        });

        return httpResponse(
          res,
          201,
          "Success",
          "User Invited",
          createUser?.email
        );
      }
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.getMailTemplate = async (req, res, next) => {
  const { lang } = req.body;

  try {
    const findTemplate = await MailTemplate.findOne({ lang });

    return httpResponse(res, 201, "Success", "Mail Template", findTemplate);
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.updateAccount = async (req, res, next) => {
  const { name, surname, email } = req.body;

  try {
    const findUser = await User.findOneAndUpdate(
      { email },
      { name: name, surname: surname },
      { new: true }
    );

    return httpResponse(res, 200, "success", "User Account Updated", findUser);
  } catch (err) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      err.message
    );
  }
};
exports.getJourneyLevelById = async (req, res, next) => {
  const userId = req.params.id;
  try {
    const findUser = await User.findById(userId);
    return httpResponse(
      res,
      200,
      "success",
      "Journey Level Data",
      findUser.journeyLevel
    );
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};
exports.updateJourneyLevel = async (req, res, next) => {
  const { journeyLevel } = req.body;

  const userId = req.params.id;

  try {
    const findUser = await User.findByIdAndUpdate(
      userId,
      { journeyLevel: journeyLevel },
      { new: true }
    );
    return httpResponse(res, 200, "success", "Journey Level Updated", findUser);
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};
exports.updateAccountById = async (req, res, next) => {
  const { name, surname, email } = req.body;

  const userId = req.params.id;

  try {
    const findUser = await User.findByIdAndUpdate(
      userId,
      { name: name, surname: surname, email: email },
      { new: true }
    );

    if (!findUser) {
      return httpResponse(res, 404, "error", "User not found");
    }

    return httpResponse(res, 200, "success", "User Account Updated", findUser);
  } catch (err) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      err.message
    );
  }
};
exports.deleteAllDatasbyUser = async (req, res, next) => {
  const { id } = req.params;
  try {
    // const deletedUser = await User.findByIdAndDelete(id);
    const deleteUserOnboardingData = await User.findByIdAndUpdate(
      { _id: id },
      { onboarding: null }
    );
    const deleteJourneyLevelData = await User.findByIdAndUpdate(
      { _id: id },
      { journeyLevel: null }
    );
    const deleteUserAppTrackingData = await AppTrackingModel.deleteMany({
      userId: id,
    });
    const deleteUserCertificate = await CertificateModel.deleteMany({
      userId: id,
    });
    const deleteUserCourseTrackingData = await CourseTrackingModel.deleteMany({
      user: id,
    });
    const deleteUserFormResponse = await FormResponseModel.deleteMany({
      submittedBy: id,
    });

    const deleteJourneyTrackingData = await JourneyTrackingModel.deleteMany({
      _id: id,
    });

    const deleteUserQuizProgressData = await QuizProgressModel.deleteMany({
      userId: id,
    });
    const deleteUserShortcutsData = await ShortcutModel.deleteMany({
      userId: id,
    });
    const deleteUserUsecasesData = await UsecaseModel.deleteMany({
      userId: id,
    });

    const deleteUserWorkflowData = await WorkflowModel.deleteMany({
      userId: id,
    });

    return httpResponse(res, 200, "success", "User Deleted", {
      // deletedUser,
      deleteUserOnboardingData,
      deleteJourneyLevelData,
      deleteUserAppTrackingData,
      deleteUserCertificate,
      deleteUserCourseTrackingData,
      deleteUserFormResponse,
      deleteJourneyTrackingData,
      deleteUserQuizProgressData,
      deleteUserShortcutsData,
      deleteUserUsecasesData,
      deleteUserWorkflowData,
    });
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};

// New function to send feedback confirmation
exports.sendFeedbackConfirmation = async (req, res, next) => {
  const { email, name } = req.body;

  try {
    await sendEmailByTemplate("Feedback Mail Confirmation", email, {
      userName: name || "User",
    });

    return httpResponse(res, 200, "Success", "Feedback confirmation sent");
  } catch (error) {
    console.error("Error sending feedback confirmation:", error);
    return httpResponse(
      res,
      500,
      "error",
      "Failed to send feedback confirmation",
      error.message
    );
  }
};

// New function to send support request notification
exports.sendSupportRequestNotification = async (req, res, next) => {
  const {
    userName,
    userEmail,
    formType,
    formTitle,
    formDescription,
    formResponses,
    notificationEmail = "<EMAIL>",
  } = req.body;

  try {
    // Format form responses for email
    let formattedResponses = "";
    if (formResponses && Array.isArray(formResponses)) {
      formattedResponses = formResponses
        .map(
          (response) => `
        <div class="response-item">
          <div class="response-label">${response.question}</div>
          <div class="response-value">${response.answer}</div>
        </div>
      `
        )
        .join("");
    } else {
      formattedResponses = "<p>No responses provided</p>";
    }

    await sendEmailByTemplate(
      "Support Request Notification",
      notificationEmail,
      {
        userName: userName || "Unknown User",
        userEmail: userEmail || "<EMAIL>",
        timestamp: new Date().toLocaleString(),
        formType: formType || "Support Request",
        formTitle: formTitle || "Support Request",
        formDescription: formDescription || "No description provided",
        formResponses: formattedResponses,
      }
    );

    return httpResponse(
      res,
      200,
      "Success",
      "Support request notification sent"
    );
  } catch (error) {
    console.error("Error sending support request notification:", error);
    return httpResponse(
      res,
      500,
      "error",
      "Failed to send support request notification",
      error.message
    );
  }
};
