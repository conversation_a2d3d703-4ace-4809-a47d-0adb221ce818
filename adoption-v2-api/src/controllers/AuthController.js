require("dotenv").config();

const { isEmail } = require("validator");
const User = require("../models/UserModel");
const { httpResponse, generateUserId, sendEmail } = require("../utils/helpers");
const md5 = require("md5");
const sha256 = require("sha256");
const jwt = require("jsonwebtoken");
const config = require("../../config");
const OTPModel = require("../models/OTPModel");
const fs = require("fs");
const path = require("path");
const APIRequestLimitModel = require("../models/APIRequestLimitModel");
const { sendEmailByTemplate } = require("./EmailTemplateController");
const sessionStore = require("../utils/sessionStore");

function generateAccessToken(email) {
  return jwt.sign(email, config.JWT_SECRET, { expiresIn: "7d" }); // 7 gün
}

exports.createUser = async (req, res, next) => {
  const {
    name,
    surname,
    email,
    username,
    role,
    company,
    department,
    admin,
    onboarding,
  } = req.body;
  let { password } = req.body;

  try {
    const check = await User.find({ email });

    if (check.length !== 0) {
      return httpResponse(
        res,
        200,
        "error",
        "This email address is already registered."
      );
    }
    if (!email || !isEmail(email)) {
      return httpResponse(
        res,
        200,
        "error",
        "Please log in with a valid email address"
      );
    } else if (!name && !surname) {
      return httpResponse(
        res,
        200,
        "error",
        "Please enter your Name and Surname"
      );
    } else if (!password) {
      return httpResponse(res, 200, "error", "Please enter a valid password");
    }
    let createUserId = generateUserId(name);
    while (User.find({ userId: createUserId }).length === 0) {
      createUserId = generateUserId(name);
    }

    password = md5(md5(password) + sha256(password));

    if (check.length === 0) {
      var token = jwt.sign({ email: email }, config.JWT_SECRET, {
        expiresIn: "24h",
      });
      const user = await User.create({
        name: name,
        surname: surname,
        email: email,
        username: username,
        password: password,
        role: role,
        company: company,
        department: department,
        onboarding: onboarding,
        status: "Not verified",
        token: token,
        create_date: new Date(),
        admin: admin,
      });

      // Create default API request limits for user
      try {
        // Create limit for standard textGeneration endpoint
        await APIRequestLimitModel.create({
          userId: user._id,
          endpoint: "textGeneration",
        });
        await APIRequestLimitModel.create({
          userId: user._id,
          endpoint: "imageGeneration",
        });
        await APIRequestLimitModel.create({
          userId: user._id,
          endpoint: "videoGeneration",
        });
      } catch (limitError) {
        console.error("Error creating API request limits:", limitError.message);
        // Limit creation error should not prevent user creation
      }

      return httpResponse(
        res,
        200,
        "success",
        "Account created successfuly",
        user
      );
    }
  } catch (error) {
    return httpResponse(res, 200, "error", "Something went wrong", error);
  }
};
exports.login = async (req, res, next) => {
  const { email, password, ssoLogin, name, surname } = req.body;

  try {
    const response = await User.find({ email });

    if (response.length === 1) {
      // User found
      let userInfo = await User.findOne({ email });

      // If logging in with SSO, continue without password check
      if (ssoLogin === true) {
        // Add name and surname information from SSO to user
        if (name || surname) {
          const updateData = {};
          if (name) updateData.name = name;
          if (surname) updateData.surname = surname;

          await User.findOneAndUpdate({ email }, updateData);
          userInfo = await User.findOne({ email });
        }

        // Update last login
        await User.findOneAndUpdate({ email }, { lastLogin: new Date() });

        const token = generateAccessToken({ email });
        userInfo.token = token;

        // Create session
        sessionStore.createSession(userInfo._id, token, email);

        const { password, ...userWithoutPassword } = userInfo.toObject();

        return httpResponse(
          res,
          200,
          "success",
          "Successfully logged in with SSO.",
          userWithoutPassword
        );
      } else {
        // Normal login process (with password)
        const hash = md5(md5(password) + sha256(password));

        if (userInfo.password === hash) {
          // Update last login
          await User.findOneAndUpdate({ email }, { lastLogin: new Date() });

          const token = generateAccessToken({ email });
          userInfo.token = token;

          // Create session
          sessionStore.createSession(userInfo._id, token, email);

          const { password, ...userWithoutPassword } = userInfo.toObject();

          return httpResponse(
            res,
            200,
            "success",
            "Successfully logged in.",
            userWithoutPassword
          );
        } else {
          return httpResponse(
            res,
            404,
            "error",
            "error",
            "Invalid credentials"
          );
        }
      }
    } else {
      // User not found

      // If logging in with SSO and auto registration is enabled
      // Check config.AUTO_PROVISION_SSO_USERS variable, default to true
      if (ssoLogin === true && config.AUTO_PROVISION_SSO_USERS !== false) {
        // Create new user
        const randomPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = md5(
          md5(randomPassword) + sha256(randomPassword)
        );

        const token = generateAccessToken({ email });

        // Use name and surname information from SSO
        const newUser = await User.create({
          email: email,
          password: hashedPassword,
          name: name || "",
          surname: surname || "",
          role: "user",
          status: "Not verified",
          token: token,
          create_date: new Date(),
          admin: false,
          onboarding: null, // Indicate that onboarding is required
          sso_login: true,
        });

        // Create session
        sessionStore.createSession(newUser._id, token, email);

        // Create default API request limits for user
        try {
          await APIRequestLimitModel.create({
            userId: newUser._id,
            endpoint: "textGeneration",
            // Default values will come from model schema
          });

          console.log("API request limits created for the new SSO user");
        } catch (limitError) {
          console.error(
            "Error creating API request limits for SSO user:",
            limitError.message
          );
          // Limit creation error should not prevent user creation
        }

        const { password, ...userWithoutPassword } = newUser.toObject();

        return httpResponse(
          res,
          200,
          "success",
          "User created and logged in with SSO.",
          userWithoutPassword
        );
      } else {
        return httpResponse(
          res,
          200,
          "error",
          "This email is not registered to any account"
        );
      }
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.me = async (req, res, next) => {
  const { email } = req.query;
  var token = req.headers.authorization?.split(" ")[1];
  try {
    const user = await User.findOne({ email });

    if (!token) return httpResponse(res, 401, "error", "No token provided.'");
    // jwt.verify(token, process.env.TOKEN_SECRET, function (err, decoded) {
    //   if (err)
    //     return httpResponse(
    //       res,
    //       401,
    //       "error",
    //       "Unauthorized Access",
    //       "Unauthorized Access"
    //     );

    const userData = {
      user: {
        id: user._id,
        displayName: user.name + " " + user.surname,
        email: user.email,
      },
    };
    return httpResponse(res, 200, "Success", "User", userData);
    // });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.loginWithToken = async (req, res, next) => {
  const { token } = req.body;
  const secret = process.env.JWT_SECRET || process.env.TOKEN_SECRET;

  try {
    // Token validation
    if (!token) {
      return httpResponse(res, 400, "error", "Token not found", null);
    }

    // Validate token
    let decodedToken;
    try {
      decodedToken = jwt.verify(token, secret);
    } catch (error) {
      return httpResponse(res, 401, "error", "Invalid token", null);
    }

    // Get email information from token content
    const email = decodedToken.email;

    if (!email) {
      return httpResponse(res, 400, "error", "Invalid token content", null);
    }

    // Find user in database
    const user = await User.findOne({ email });

    if (!user) {
      return httpResponse(res, 404, "error", "User not found", null);
    }

    // Check if user has admin privileges
    if (user.role !== "admin" && user.role !== "Administrator") {
      return httpResponse(
        res,
        403,
        "error",
        "You do not have access to the panel",
        null
      );
    }

    // Create new token for panel
    const panelToken = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        role: user.role,
        isPanel: true, // Special flag for panel
      },
      secret,
      { expiresIn: "8h" }
    );

    // Successful response
    return httpResponse(res, 200, "success", "Panel access granted", {
      accessToken: panelToken,
      user: {
        _id: user._id,
        name: user.name,
        surname: user.surname,
        email: user.email,
        role: user.role,
        status: user.status,
      },
    });
  } catch (error) {
    console.error("loginWithToken error:", error);
    return httpResponse(res, 500, "error", "Server error", error.message);
  }
};
exports.changePassword = async (req, res, next) => {
  const { currentPassword, newPassword, email } = req.body;

  try {
    const user = await User.findOne({ email: email });

    if (!user) {
      return httpResponse(res, 404, "error", "error", "User not found.");
    }
    const isCurrentPasswordValid =
      user.password === md5(md5(currentPassword) + sha256(currentPassword));
    if (!isCurrentPasswordValid) {
      return httpResponse(
        res,
        400,
        "error",
        "error",
        "Invalid current password."
      );
    }
    const hashedPassword = md5(md5(newPassword) + sha256(newPassword));

    const updatedUser = await User.findOneAndUpdate(
      { email: email },
      { password: hashedPassword },
      { new: true }
    );

    if (!updatedUser) {
      return httpResponse(res, 404, "error", "error", "User not found.");
    }

    // Use template system for password change confirmation
    try {
      await sendEmailByTemplate("Password Change Confirmation", email, {
        userName: updatedUser.name || "User",
      });

      return httpResponse(
        res,
        200,
        "Success",
        "Password successfully changed",
        updatedUser
      );
    } catch (emailError) {
      console.error("Error sending password change confirmation:", emailError);
      // Fallback to old method if template system fails
      let emailTemplate = fs.readFileSync(
        path.join(
          __dirname,
          "../utils/emailTemplates/forgotPasswordChange.html"
        ),
        "utf8"
      );

      emailTemplate = emailTemplate
        .replace(/\{\{userName\}\}/g, updatedUser?.name || "User")
        .replace(/\{\{year\}\}/g, new Date().getFullYear());

      await sendEmail({
        from: "<EMAIL>",
        to: email,
        subject: "Password Change",
        html: emailTemplate,
      });

      return httpResponse(
        res,
        200,
        "Success",
        "Password successfully changed",
        updatedUser
      );
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.sendPasswordResetOTPMail = async (req, res, next) => {
  const { email } = req.body;
  var token = req.headers.authorization?.split(" ")[1];
  try {
    const existingUser = await User.findOne({ email });
    if (!existingUser) {
      throw Error("There is no account for the provided email.");
    }
    if (!existingUser.verified) {
      throw Error("Email hasn't been verified. Check your inbox");
    }

    const otpDetails = {
      email,
      subject: "Password Reset",
      message: "Enter the code below to reset your password.",
      duration: 1,
    };
    const createOTP = await sendOTP(otpDetails);
    return createOTP;
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.forgotPassword = async (req, res, next) => {
  const { email } = req.body;
  try {
    const response = await User.find({ email });

    if (response.length > 0) {
      // Önce varolan OTP'yi sil
      await OTPModel.findOneAndDelete({ email });

      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Yeni OTP oluştur
      await OTPModel.create({
        email,
        otp,
      });

      // Use template system for forgot password email
      try {
        await sendEmailByTemplate("Forgot Password OTP", email, {
          userName: response[0]?.name || "User",
          otp: otp,
        });

        return httpResponse(res, 200, "success", "Mail sent successfully", {
          email,
        });
      } catch (emailError) {
        console.error("Error sending forgot password email:", emailError);

        // Fallback to old method if template system fails
        let emailTemplate = fs.readFileSync(
          path.join(__dirname, "../utils/emailTemplates/forgotPassword.html"),
          "utf8"
        );

        emailTemplate = emailTemplate
          .replace(/\{\{userName\}\}/g, response[0]?.name || "User")
          .replace(/\{\{otp\}\}/g, otp);

        await sendEmail({
          from: "<EMAIL>",
          to: email,
          subject: "Forgot Password",
          html: emailTemplate,
        });

        return httpResponse(res, 200, "success", "Mail sent successfully", {
          email,
        });
      }
    } else {
      return httpResponse(res, 404, "error", "error", "Email not found");
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.forgotPasswordChange = async (req, res, next) => {
  const { email, password } = req.body;
  try {
    const hashedPassword = md5(md5(password) + sha256(password));
    const updatedUser = await User.findOneAndUpdate(
      { email: email },
      { password: hashedPassword },
      { new: true }
    );

    // Use template system for password change confirmation
    try {
      await sendEmailByTemplate("Password Change Confirmation", email, {
        userName: updatedUser?.name || "User",
      });

      return httpResponse(
        res,
        200,
        "Success",
        "Password successfully changed",
        updatedUser
      );
    } catch (emailError) {
      console.error("Error sending password change confirmation:", emailError);
      // Fallback to old method if template system fails
      let emailTemplate = fs.readFileSync(
        path.join(
          __dirname,
          "../utils/emailTemplates/forgotPasswordChange.html"
        ),
        "utf8"
      );

      emailTemplate = emailTemplate
        .replace(/\{\{userName\}\}/g, updatedUser?.name || "User")
        .replace(/\{\{year\}\}/g, new Date().getFullYear());

      await sendEmail({
        from: "<EMAIL>",
        to: email,
        subject: "Password Change",
        html: emailTemplate,
      });

      return httpResponse(
        res,
        200,
        "Success",
        "Password successfully changed",
        updatedUser
      );
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.checkOTP = async (req, res, next) => {
  const { email, otp } = req.body;
  try {
    const checkOTP = await OTPModel.findOne({ email, otp });

    if (checkOTP) {
      const responseData = [{ email: checkOTP.email, otp: checkOTP.otp }];
      await OTPModel.findOneAndDelete({ email });
      return httpResponse(res, 200, "success", "OTP matched", responseData);
    } else {
      return httpResponse(res, 404, "error", "OTP not found or expired");
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.verifyAccount = async (req, res, next) => {
  const { email, otp } = req.body;

  try {
    const checkOTP = await OTPModel.findOne({ email, otp });
    if (checkOTP) {
      const updatedUser = await User.findOneAndUpdate(
        { email },
        { status: "Verified" },
        { new: true }
      );
      await OTPModel.findOneAndDelete({ email: email });
      return httpResponse(res, 200, "success", "Account verified", updatedUser);
    } else {
      return httpResponse(res, 404, "error", "OTP not found for this account!");
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.definePassword = async (req, res, next) => {
  const { newPassword, email } = req.body;
  try {
    const hashedPassword = md5(md5(newPassword) + sha256(newPassword));

    const checkUser = await User.findOneAndUpdate(
      { email: email },
      { password: hashedPassword, status: "Verified" },
      { new: true }
    );

    if (checkUser !== undefined) {
      return httpResponse(res, 200, "success", "Password changed successfully");
    }
  } catch (error) {
    console.error("error:", error.message);
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.resetPassword = async (req, res, next) => {
  const { email, password, otp } = req.body;
  try {
    const checkOTP = await OTPModel.findOne({ email, otp });

    if (checkOTP) {
      const hashedPassword = md5(md5(password) + sha256(password));
      const updatedUser = await User.findOneAndUpdate(
        { email: email },
        { password: hashedPassword },
        { new: true }
      );

      // Use template system for password change confirmation
      try {
        await sendEmailByTemplate("Password Change Confirmation", email, {
          userName: updatedUser?.name || "User",
        });

        await OTPModel.findOneAndDelete({ email });
        return httpResponse(
          res,
          200,
          "Success",
          "Password successfully changed",
          updatedUser
        );
      } catch (emailError) {
        console.error(
          "Error sending password change confirmation:",
          emailError
        );

        // Fallback to old method if template system fails
        let emailTemplate = fs.readFileSync(
          path.join(
            __dirname,
            "../utils/emailTemplates/forgotPasswordChange.html"
          ),
          "utf8"
        );

        emailTemplate = emailTemplate
          .replace(/\{\{userName\}\}/g, updatedUser?.name || "User")
          .replace(/\{\{year\}\}/g, new Date().getFullYear());

        await sendEmail({
          from: "<EMAIL>",
          to: email,
          subject: "Password Change",
          html: emailTemplate,
        });

        await OTPModel.findOneAndDelete({ email });
        return httpResponse(
          res,
          200,
          "Success",
          "Password successfully changed",
          updatedUser
        );
      }
    } else {
      return httpResponse(res, 404, "error", "OTP not found or expired");
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Add logout endpoint
exports.logout = async (req, res, next) => {
  try {
    console.log("Logout request received");
    console.log("req.user:", req.user);

    const { email } = req.user; // User information from AuthMiddleware
    const user = await User.findOne({ email });

    console.log(
      "Found user for logout:",
      user ? { id: user._id, email: user.email } : "Not found"
    );

    if (user) {
      // End session
      console.log("Attempting to destroy session for user ID:", user._id);
      const sessionDestroyed = sessionStore.destroySession(user._id);

      console.log("Session destroyed result:", sessionDestroyed);

      if (sessionDestroyed) {
        return httpResponse(
          res,
          200,
          "success",
          "Successfully logged out",
          null
        );
      } else {
        return httpResponse(res, 404, "error", "No active session found", null);
      }
    } else {
      return httpResponse(res, 404, "error", "User not found", null);
    }
  } catch (error) {
    console.error("Logout error:", error);
    return httpResponse(
      res,
      500,
      "error",
      "Logout error occurred",
      error.message
    );
  }
};

// Admin endpoint for logging out user
exports.adminLogoutUser = async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Admin privilege check (done in AuthMiddleware but additional check)
    const adminUser = await User.findOne({ email: req.user.email });
    if (
      !adminUser ||
      (adminUser.role !== "admin" &&
        adminUser.role !== "Administrator" &&
        adminUser.role !== "SUPER_USER")
    ) {
      return httpResponse(res, 403, "error", "Admin privileges required", null);
    }

    // Find target user
    const targetUser = await User.findById(userId);
    if (!targetUser) {
      return httpResponse(res, 404, "error", "Target user not found", null);
    }

    // End session
    const sessionDestroyed = sessionStore.destroySession(userId);

    return httpResponse(
      res,
      200,
      "success",
      `User ${targetUser.email} has been logged out`,
      {
        targetUser: {
          id: targetUser._id,
          email: targetUser.email,
          name: targetUser.name,
          surname: targetUser.surname,
        },
        sessionDestroyed,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "Admin logout error occurred",
      error.message
    );
  }
};

// List active sessions (for admin)
exports.getActiveSessions = async (req, res, next) => {
  try {
    // Admin privilege check
    const adminUser = await User.findOne({ email: req.user.email });
    if (
      !adminUser ||
      (adminUser.role !== "admin" &&
        adminUser.role !== "Administrator" &&
        adminUser.role !== "SUPER_USER")
    ) {
      return httpResponse(res, 403, "error", "Admin privileges required", null);
    }

    const activeSessions = sessionStore.getAllActiveSessions();

    return httpResponse(res, 200, "success", "Active sessions retrieved", {
      count: activeSessions.length,
      sessions: activeSessions,
    });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "Error retrieving active sessions",
      error.message
    );
  }
};

// Get current user's session info
exports.getMySession = async (req, res, next) => {
  try {
    const { email } = req.user;
    const user = await User.findOne({ email });

    if (!user) {
      return httpResponse(res, 404, "error", "User not found", null);
    }

    const sessionDetails = sessionStore.getSessionWithDetails(user._id);

    if (!sessionDetails) {
      return httpResponse(res, 404, "error", "No active session found", null);
    }

    return httpResponse(res, 200, "success", "Session details retrieved", {
      userId: user._id,
      email: user.email,
      userName: `${user.name} ${user.surname}`,
      loginTime: sessionDetails.loginTime,
      lastActivity: sessionDetails.lastActivity,
      totalSessionDuration: {
        minutes: sessionDetails.details.totalSessionDurationMinutes,
        hours: sessionDetails.details.totalSessionDurationHours,
        readable: `${sessionDetails.details.totalSessionDurationHours} hours ${
          sessionDetails.details.totalSessionDurationMinutes % 60
        } minutes`,
      },
      inactivityDuration: {
        minutes: sessionDetails.details.timeSinceLastActivityMinutes,
        seconds: sessionDetails.details.timeSinceLastActivitySeconds,
        readable:
          sessionDetails.details.timeSinceLastActivitySeconds < 60
            ? `${sessionDetails.details.timeSinceLastActivitySeconds} seconds`
            : `${sessionDetails.details.timeSinceLastActivityMinutes} minutes`,
      },
      remainingTime: {
        minutes: sessionDetails.details.remainingTimeMinutes,
        hours: sessionDetails.details.remainingTimeHours,
        readable: `${sessionDetails.details.remainingTimeHours} hours ${
          sessionDetails.details.remainingTimeMinutes % 60
        } minutes`,
      },
      sessionStatus: {
        activityStatus: sessionDetails.details.activityStatus,
        isRecentlyActive: sessionDetails.details.isRecentlyActive,
        timeoutInMinutes: 720,
        timeoutInHours: 12,
        willExpireAt: new Date(
          sessionDetails.lastActivity.getTime() + 720 * 60 * 1000
        ),
      },
    });
  } catch (error) {
    return httpResponse(res, 500, "error", "Session info error", error.message);
  }
};

// Get session info WITHOUT updating lastActivity (for testing purposes)
exports.getMySessionNoUpdate = async (req, res, next) => {
  try {
    const { email } = req.user;
    const user = await User.findOne({ email });

    if (!user) {
      return httpResponse(res, 404, "error", "User not found", null);
    }

    // Get session WITHOUT updating it
    const session = sessionStore.getSession(user._id);
    if (!session || !session.isActive) {
      return httpResponse(res, 404, "error", "No active session found", null);
    }

    const details = sessionStore.getSessionDetails(session);
    if (details.isExpired) {
      return httpResponse(res, 404, "error", "Session has expired", null);
    }

    return httpResponse(
      res,
      200,
      "success",
      "Session details retrieved (no update)",
      {
        userId: user._id,
        email: user.email,
        userName: `${user.name} ${user.surname}`,
        loginTime: session.loginTime,
        lastActivity: session.lastActivity,
        currentTime: new Date(),
        totalSessionDuration: {
          minutes: details.totalSessionDurationMinutes,
          hours: details.totalSessionDurationHours,
          readable: `${details.totalSessionDurationHours} hours ${
            details.totalSessionDurationMinutes % 60
          } minutes`,
        },
        inactivityDuration: {
          minutes: details.timeSinceLastActivityMinutes,
          seconds: details.timeSinceLastActivitySeconds,
          readable:
            details.timeSinceLastActivitySeconds < 60
              ? `${details.timeSinceLastActivitySeconds} seconds`
              : `${details.timeSinceLastActivityMinutes} minutes`,
        },
        remainingTime: {
          minutes: details.remainingTimeMinutes,
          hours: details.remainingTimeHours,
          readable: `${details.remainingTimeHours} hours ${
            details.remainingTimeMinutes % 60
          } minutes`,
        },
        sessionStatus: {
          activityStatus: details.activityStatus,
          isRecentlyActive: details.isRecentlyActive,
          timeoutInMinutes: 720,
          timeoutInHours: 12,
          willExpireAt: new Date(
            session.lastActivity.getTime() + 720 * 60 * 1000
          ),
        },
        note: "This endpoint does not refresh the session - it only shows the current status",
      }
    );
  } catch (error) {
    return httpResponse(res, 500, "error", "Session info error", error.message);
  }
};

// Check current sessions for debug
exports.debugSessions = async (req, res, next) => {
  try {
    console.log("Debug sessions endpoint called");
    console.log("All sessions in store:", sessionStore.getAllActiveSessions());

    const activeSessions = sessionStore.getAllActiveSessions();

    return httpResponse(res, 200, "success", "Debug sessions retrieved", {
      count: activeSessions.length,
      sessions: activeSessions,
      allSessionKeys: Array.from(sessionStore.sessions().keys()),
    });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "Debug sessions error",
      error.message
    );
  }
};
