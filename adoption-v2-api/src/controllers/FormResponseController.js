const FormResponse = require("../models/FormResponse");
const UserModel = require("../models/UserModel");
const { validationResult } = require("express-validator");
const { httpResponse, sendEmail } = require("../utils/helpers");
const { HTTP_CODES } = require("../utils/enum");
const mongoose = require("mongoose");
const path = require("path");
const fs = require("fs");
const multer = require("multer");
const { BlobServiceClient } = require("@azure/storage-blob");

// Multer configuration
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
});

// Azure Storage configuration
const azureStorageConnectionString =
  process.env.AZURE_STORAGE_CONNECTION_STRING;
const containerName =
  process.env.AZURE_STORAGE_CONTAINER_NAME || "form-uploads";

// File upload helper function
const uploadFileToAzure = async (file) => {
  try {
    if (!azureStorageConnectionString) {
      throw new Error("Azure Storage connection string not configured");
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(
      azureStorageConnectionString
    );
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Container yoksa oluştur
    await containerClient.createIfNotExists({
      access: "blob",
    });

    const blobName = `${Date.now()}-${file.originalname}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    await blockBlobClient.upload(file.buffer, file.buffer.length, {
      blobHTTPHeaders: {
        blobContentType: file.mimetype,
      },
    });

    return {
      url: blockBlobClient.url,
      name: blobName,
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
    };
  } catch (error) {
    console.error("Error uploading file to Azure:", error);
    throw error;
  }
};

// Create a custom email function and use Azure API directly
const sendFeedbackEmail = async (to, userName) => {
  try {
    const templatePath = path.join(
      __dirname,
      "../utils/emailTemplates/feedbackMail.html"
    );
    let emailTemplate = fs.readFileSync(templatePath, "utf8");

    emailTemplate = emailTemplate.replace(
      "${response[0]?.name}",
      userName || "User"
    );

    // Azure API'ye doğrudan erişim
    const { EmailClient } = require("@azure/communication-email");
    const connectionString = process.env.AZURE_EMAIL_CONNECTION_STRING;
    const emailClient = new EmailClient(connectionString);

    const emailMessage = {
      senderAddress: "<EMAIL>",
      content: {
        subject: "Your Support Request Has Been Received",
        html: emailTemplate,
      },
      recipients: {
        to: [
          {
            address: to,
          },
        ],
      },
    };

    const poller = await emailClient.beginSend(emailMessage);
  } catch (error) {
    console.error("Error sending feedback email:", error);
  }
};

// Support request notification email to admin
const sendSupportRequestNotification = async (user, formData) => {
  try {
    // Try to use the new template system first
    const { sendEmailByTemplate } = require("./EmailTemplateController");

    // Format form responses for display
    const formatResponses = (responses) => {
      return responses
        .map((response) => {
          return `
          <div class="response-item">
            <div class="response-label">${response.name} (${
            response.type
          })</div>
            <div class="response-value">${
              response.value || "No response provided"
            }</div>
          </div>
        `;
        })
        .join("");
    };

    const templateVariables = {
      userName: user.name || "Unknown User",
      userEmail: user.email || "Unknown Email",
      timestamp: new Date().toLocaleString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        timeZoneName: "short",
      }),
      formType: formData.formType || "Unknown",
      formTitle: formData.title || "No title provided",
      formDescription: formData.description || "No description provided",
      formResponses: formatResponses(formData.responses || []),
    };

    try {
      await sendEmailByTemplate(
        "Support Request Notification",
        "<EMAIL>",
        templateVariables
      );
      console.log("Support request notification sent via template system");
      return;
    } catch (templateError) {
      console.log(
        "Template system failed, falling back to file method:",
        templateError.message
      );
    }

    // Fallback to reading template file directly
    const templatePath = path.join(
      __dirname,
      "../utils/emailTemplates/supportRequestNotification.html"
    );
    let emailTemplate = fs.readFileSync(templatePath, "utf8");

    // Replace template variables using new {{variable}} format
    emailTemplate = emailTemplate
      .replace(/\{\{userName\}\}/g, templateVariables.userName)
      .replace(/\{\{userEmail\}\}/g, templateVariables.userEmail)
      .replace(/\{\{timestamp\}\}/g, templateVariables.timestamp)
      .replace(/\{\{formType\}\}/g, templateVariables.formType)
      .replace(/\{\{formTitle\}\}/g, templateVariables.formTitle)
      .replace(/\{\{formDescription\}\}/g, templateVariables.formDescription)
      .replace(/\{\{formResponses\}\}/g, templateVariables.formResponses);

    // Send email using the existing sendEmail helper
    await sendEmail({
      from: "<EMAIL>",
      to: "<EMAIL>",
      subject: `New Support Request from ${user.email}`,
      html: emailTemplate,
    });
  } catch (error) {
    console.error("Error sending support request notification:", error);
  }
};

// File upload endpoint
exports.uploadFile = async (req, res) => {
  upload.single("file")(req, res, async (err) => {
    try {
      if (err instanceof multer.MulterError) {
        if (err.code === "LIMIT_FILE_SIZE") {
          return httpResponse(
            res,
            HTTP_CODES.REQUEST_ENTITY_TOO_LARGE,
            "error",
            "File size too large",
            "Maximum file size allowed is 50MB"
          );
        }
        return httpResponse(
          res,
          HTTP_CODES.BAD_REQUEST,
          "error",
          "File upload error",
          err.message
        );
      } else if (err) {
        return httpResponse(
          res,
          HTTP_CODES.INT_SERVER_ERROR,
          "error",
          "Unexpected error occurred",
          err.message
        );
      }

      if (!req.file) {
        return httpResponse(
          res,
          HTTP_CODES.BAD_REQUEST,
          "error",
          "Please upload a file"
        );
      }

      if (!req.user || !req.user.email) {
        return httpResponse(
          res,
          HTTP_CODES.UNAUTHORIZED,
          "error",
          "User not authenticated"
        );
      }

      const fileData = await uploadFileToAzure(req.file);

      return httpResponse(
        res,
        HTTP_CODES.OK,
        "success",
        "File uploaded successfully",
        fileData
      );
    } catch (error) {
      console.error("Error in file upload:", error);
      return httpResponse(
        res,
        HTTP_CODES.INT_SERVER_ERROR,
        "error",
        "An internal server error occurred",
        error.message
      );
    }
  });
};

exports.create = async (req, res) => {
  try {
    if (!req.user || !req.user.email) {
      return httpResponse(
        res,
        HTTP_CODES.UNAUTHORIZED,
        "error",
        "User not authenticated"
      );
    }

    const user = await UserModel.findOne({ email: req.user.email });

    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Validation error",
        errors.array()
      );
    }

    const { formId, formType, title, description, responses } = req.body;

    if (formType === "help") {
      try {
        await sendFeedbackEmail(user.email, user.name);
        await sendSupportRequestNotification(user, req.body);
      } catch (error) {
        console.error("Error sending email:", error);
      }
    }

    const formResponse = new FormResponse({
      formId,
      formType,
      title,
      description,
      responses,
      submittedBy: user._id,
      status: "draft",
    });

    await formResponse.save();

    return httpResponse(
      res,
      HTTP_CODES.CREATED,
      "success",
      "Form response created successfully",
      formResponse
    );
  } catch (error) {
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// List all form responses
exports.list = async (req, res) => {
  try {
    const {
      formId,
      status,
      formType,
      submittedBy,
      page = 1,
      limit = 10,
    } = req.query;

    const query = {};
    if (formId) query.formId = formId;
    if (status) query.status = status;
    if (formType) query.formType = formType;
    // Get user by email
    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    // If is not admin, only show the user's form responses
    if (user.admin !== true) {
      query.submittedBy = user._id;
    } else if (submittedBy) {
      // If is admin and submittedBy is not null, show the submittedBy's form responses
      query.submittedBy = submittedBy;
    }

    let result;
    if (limit === -1) {
      // If limit is -1, get all records
      const docs = await FormResponse.find(query)
        .sort({ createdAt: -1 })
        .populate("submittedBy", "name email role");

      result = {
        docs,
        totalDocs: docs.length,
        limit: -1,
        totalPages: 1,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      };
    } else {
      // Manuel pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const [docs, totalDocs] = await Promise.all([
        FormResponse.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .populate("submittedBy", "name email role"),
        FormResponse.countDocuments(query),
      ]);

      const totalPages = Math.ceil(totalDocs / parseInt(limit));

      result = {
        docs,
        totalDocs,
        limit: parseInt(limit),
        totalPages,
        page: parseInt(page),
        pagingCounter: skip + 1,
        hasPrevPage: parseInt(page) > 1,
        hasNextPage: parseInt(page) < totalPages,
        prevPage: parseInt(page) > 1 ? parseInt(page) - 1 : null,
        nextPage: parseInt(page) < totalPages ? parseInt(page) + 1 : null,
      };
    }

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form responses retrieved successfully",
      result
    );
  } catch (error) {
    console.error("Error in list form responses:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Display a specific form response
exports.get = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId kontrolü
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid form response ID format"
      );
    }

    const formResponse = await FormResponse.findById(id).populate(
      "submittedBy",
      "name email"
    );

    if (!formResponse) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Form response not found"
      );
    }

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form response retrieved successfully",
      formResponse
    );
  } catch (error) {
    console.error("Error in get form response:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Update form response
exports.update = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId kontrolü
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid form response ID format"
      );
    }

    const { responses, status } = req.body;

    // Email'e göre user'ı bulalım
    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const formResponse = await FormResponse.findById(id);

    if (!formResponse) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Form response not found"
      );
    }

    // Sadece yanıtı oluşturan kullanıcı güncelleyebilir
    if (formResponse.submittedBy.toString() !== user._id.toString()) {
      return httpResponse(
        res,
        HTTP_CODES.FORBIDDEN,
        "error",
        "You don't have permission to update this form response"
      );
    }

    if (responses) formResponse.responses = responses;
    if (status) formResponse.status = status;

    await formResponse.save();

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form response updated successfully",
      formResponse
    );
  } catch (error) {
    console.error("Error in update form response:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Delete form response
exports.delete = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId control
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid form response ID format"
      );
    }

    // Find user by email
    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const formResponse = await FormResponse.findById(id);

    if (!formResponse) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Form response not found"
      );
    }

    // Only the user who created the response can delete it
    if (formResponse.submittedBy.toString() !== user._id.toString()) {
      return httpResponse(
        res,
        HTTP_CODES.FORBIDDEN,
        "error",
        "You don't have permission to delete this form response"
      );
    }

    await FormResponse.deleteOne({ _id: id });

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form response deleted successfully"
    );
  } catch (error) {
    console.error("Error in delete form response:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
