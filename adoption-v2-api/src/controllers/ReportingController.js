const mongoose = require("mongoose");
const { httpResponse } = require("../utils/helpers");
const CompanyModel = require("../models/CompanyModel");
const {
  generateTrainingProgressReport,
} = require("./reporting/TrainingProgressController");
const {
  generateMSTrainingReport,
} = require("./reporting/MSTrainingController");
const {
  generateNextgenTalentReport,
} = require("./reporting/NextgenTalentController");
const {
  generateApplicationUsageReport,
} = require("./reporting/ApplicationUsageController");
const { generateAIValueReport } = require("./reporting/AIValueController");
const { generateIdeationReport } = require("./reporting/IdeationController");
const {
  generateCreatorToolsReport,
} = require("./reporting/CreatorToolsController");
const ReportModel = require("../models/ReportModel");
const REPORT_TYPES = {
  TRAINING: "training",
  NEXTGEN: "nextgen",
  APPLICATION: "application",
  AI_VALUE: "ai-value",
  IDEATION: "ideation",
  CREATOR: "creator",
  MS_TRAINING: "ms-training",
};
exports.createReport = async (req, res, next) => {
  try {
    const {
      reportType = req.params.type,
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    } = req.body;
    const normalizedReportType = reportType.toLowerCase().replace(/_/g, "-");
    if (!Object.values(REPORT_TYPES).includes(normalizedReportType)) {
      return res.status(400).json({
        status: "error",
        message: `Invalid report type. Must be one of: ${Object.values(
          REPORT_TYPES
        ).join(", ")}`,
      });
    }

    if (!mongoose.Types.ObjectId.isValid(companyId) && CompanyModel) {
      return httpResponse(res, 400, "error", "Invalid companyId format", null);
    }

    const company = await CompanyModel.findById(companyId);
    if (!company) {
      return httpResponse(res, 400, "error", "Company not found", null);
    }

    let report = {
      type: normalizedReportType,
      companyId,
      generatedAt: new Date(),
      data: null,
    };

    switch (normalizedReportType) {
      case REPORT_TYPES.TRAINING:
        report.data = await generateTrainingProgressReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.NEXTGEN:
        report.data = await generateNextgenTalentReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.APPLICATION:
        report.data = await generateApplicationUsageReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.AI_VALUE:
        report.data = await generateAIValueReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.IDEATION:
        report.data = await generateIdeationReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.CREATOR:
        report.data = await generateCreatorToolsReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.MS_TRAINING:
        report.data = await generateMSTrainingReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      default:
        return httpResponse(res, 400, "error", "Invalid report type", null);
    }

    return httpResponse(
      res,
      200,
      "success",
      "Report created successfully",
      report
    );
  } catch (error) {
    next(error);
  }
};

exports.getReportFromModel = async (req, res, next) => {
  const { reportType, companyId } = req.params;
  if (!reportType || !companyId) {
    return httpResponse(
      res,
      400,
      "error",
      "Report type and companyId are required",
      null
    );
  }
  const report = await ReportModel.find({
    reportType,
    company: companyId,
  });

  return httpResponse(
    res,
    200,
    "success",
    "Reports fetched successfully",
    report
  );
};

exports.getAllReportsByType = async (req, res, next) => {
  try {
    const { reportType } = req.params;
    if (!reportType) {
      return httpResponse(res, 400, "error", "Report type is required", null);
    }
    // sort by createdAt in descending order
    const report = await ReportModel.find({
      reportType,
    }).sort({ createdAt: -1 });

    return httpResponse(
      res,
      200,
      "success",
      "Reports fetched successfully",
      report
    );
  } catch (error) {
    next(error);
  }
};
