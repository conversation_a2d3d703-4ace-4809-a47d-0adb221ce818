const MailTemplate = require("../models/MailTemplate");
const User = require("../models/UserModel");
const { sendEmail } = require("../utils/helpers");
const cron = require("node-cron");
const { validationResult } = require("express-validator");

// Map to track active cron jobs
const activeCronJobs = new Map();

// Function to wrap HTML content in beautiful template format
const wrapHtmlContent = (htmlContent, subject) => {
  return `
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { 
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
      line-height: 1.6; 
      color: #333; 
      margin: 0; 
      padding: 0;
      background-color: #f9f9f9;
    }
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }
    .email-header {
      background-color: #f5f5f5;
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #eeeeee;
    }
    .email-logo {
      max-width: 180px;
      margin-bottom: 10px;
    }
    .email-body {
      padding: 30px;
      color: #444444;
    }
    .email-greeting {
      font-size: 22px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 20px;
    }
    .email-message {
      margin-bottom: 25px;
    }
    .email-button {
      display: inline-block;
      background-color: #3a9c6d;
      color: white;
      text-decoration: none;
      padding: 12px 30px;
      border-radius: 4px;
      font-weight: 600;
      margin: 20px 0;
      text-align: center;
    }
    .email-footer {
      background-color: #f5f5f5;
      padding: 20px;
      text-align: center;
      color: #777777;
      font-size: 14px;
      border-top: 1px solid #eeeeee;
    }
    .help-text {
      font-size: 14px;
      color: #777777;
      margin-top: 25px;
      padding-top: 20px;
      border-top: 1px solid #eeeeee;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <img src="https://aitrainer.aibusinessschool.com/resources/uploads/2024/01/saas-88a1c009-0c9a-4d94-a2f3-fa017cba07d6-aibs-logo-1.png" alt="AI Business School" class="email-logo">
    </div>
    
    <div class="email-body">
      <div class="email-greeting">
        ${subject}
      </div>
      
      <div class="email-message">
        ${htmlContent}
      </div>
    </div>
    
    <div class="email-footer">
      <p>&copy; 2025 AI Business School. All rights reserved.</p>
      <div class="help-text">
        <p>If you have any questions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>
    </div>
  </div>
</body>
</html>`;
};

// Function to replace template variables
const replaceVariables = (content, variables, template) => {
  let result = content;

  // If template is provided, use its variables with default values
  if (template && template.variables) {
    template.variables.forEach((templateVar) => {
      const regex = new RegExp(`{{${templateVar.name}}}`, "g");
      const value =
        variables[templateVar.name] || templateVar.defaultValue || "";
      result = result.replace(regex, value);
    });
  }

  // Also handle any additional variables passed that might not be in template
  Object.keys(variables).forEach((key) => {
    const regex = new RegExp(`{{${key}}}`, "g");
    result = result.replace(regex, variables[key] || "");
  });

  return result;
};

// Get all templates
exports.getAllTemplates = async (req, res) => {
  try {
    const templates = await MailTemplate.find()
      .populate("createdBy", "name email")
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error("Get all templates error:", error);
    res.status(500).json({
      success: false,
      message: "Templates could not be retrieved",
      error: error.message,
    });
  }
};

// Get template details
exports.getTemplateById = async (req, res) => {
  try {
    const { id } = req.params;
    const template = await MailTemplate.findById(id)
      .populate("createdBy", "name email")
      .populate("scheduledJobs.targetUsers", "name email");

    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    res.status(200).json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error("Get template by ID error:", error);
    res.status(500).json({
      success: false,
      message: "Template could not be retrieved",
      error: error.message,
    });
  }
};

// Create new template
exports.createTemplate = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: errors.array(),
      });
    }

    const {
      name,
      subject,
      htmlContent,
      textContent,
      variables,
      tags,
      isActive,
    } = req.body;

    // Wrap HTML content in beautiful template format only if not already wrapped
    let wrappedHtmlContent = htmlContent;
    if (!htmlContent.includes("email-container")) {
      wrappedHtmlContent = wrapHtmlContent(htmlContent, subject);
    }

    const template = new MailTemplate({
      name,
      subject,
      htmlContent: wrappedHtmlContent,
      textContent,
      variables: variables || [],
      tags: tags || [],
      isActive: isActive !== undefined ? isActive : true,
      createdBy: req.user?.id || null,
    });

    await template.save();

    res.status(201).json({
      success: true,
      message: "Template created successfully",
      data: template,
    });
  } catch (error) {
    console.error("Create template error:", error);
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Template name already exists",
      });
    }
    res.status(500).json({
      success: false,
      message: "Template could not be created",
      error: error.message,
    });
  }
};

// Update template
exports.updateTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // If htmlContent is being updated, wrap it only if not already wrapped
    if (updateData.htmlContent) {
      if (!updateData.htmlContent.includes("email-container")) {
        updateData.htmlContent = wrapHtmlContent(
          updateData.htmlContent,
          updateData.subject || "Email Template"
        );
      }
    }

    const template = await MailTemplate.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Template updated successfully",
      data: template,
    });
  } catch (error) {
    console.error("Update template error:", error);
    res.status(500).json({
      success: false,
      message: "Template could not be updated",
      error: error.message,
    });
  }
};

// Delete template
exports.deleteTemplate = async (req, res) => {
  try {
    const { id } = req.params;

    // First stop scheduled jobs
    const template = await MailTemplate.findById(id);
    if (template && template.scheduledJobs) {
      template.scheduledJobs.forEach((job) => {
        const jobKey = `${id}_${job._id}`;
        if (activeCronJobs.has(jobKey)) {
          activeCronJobs.get(jobKey).destroy();
          activeCronJobs.delete(jobKey);
        }
      });
    }

    const deletedTemplate = await MailTemplate.findByIdAndDelete(id);

    if (!deletedTemplate) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Template deleted successfully",
    });
  } catch (error) {
    console.error("Delete template error:", error);
    res.status(500).json({
      success: false,
      message: "Template could not be deleted",
      error: error.message,
    });
  }
};

// Send test email
exports.sendTestEmail = async (req, res) => {
  try {
    const { id } = req.params;
    const { testEmail, variables = {} } = req.body;

    const template = await MailTemplate.findById(id);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    const subject = replaceVariables(template.subject, variables, template);
    let htmlContent = replaceVariables(
      template.htmlContent,
      variables,
      template
    );

    // Check if content is already wrapped (contains email-container class)
    if (!htmlContent.includes("email-container")) {
      htmlContent = wrapHtmlContent(htmlContent, subject);
    }

    await sendEmail({
      to: testEmail,
      subject: subject,
      html: htmlContent,
      from: "<EMAIL>",
    });

    res.status(200).json({
      success: true,
      message: "Test email sent successfully",
    });
  } catch (error) {
    console.error("Send test email error:", error);
    res.status(500).json({
      success: false,
      message: "Test email could not be sent",
      error: error.message,
    });
  }
};

// Add/update scheduled job
exports.addScheduledJob = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      triggerType = "cron",
      cronExpression,
      triggerConditions = {},
      targetUsers = [],
      targetEmails = [],
      targetConditions = {},
      isActive = true,
    } = req.body;

    // Validate based on trigger type
    if (triggerType === "cron") {
      if (!cronExpression || !cron.validate(cronExpression)) {
        return res.status(400).json({
          success: false,
          message: "Invalid or missing cron expression for cron trigger",
        });
      }
    } else if (triggerType === "login_reminder") {
      if (
        !triggerConditions.reminderDays ||
        triggerConditions.reminderDays < 1
      ) {
        return res.status(400).json({
          success: false,
          message:
            "Reminder days must be at least 1 for login reminder trigger",
        });
      }
    }

    const template = await MailTemplate.findById(id);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    const newJob = {
      name,
      triggerType,
      cronExpression: triggerType === "cron" ? cronExpression : undefined,
      triggerConditions,
      targetUsers,
      targetEmails,
      targetConditions,
      isActive,
      nextRun: triggerType === "cron" ? new Date() : undefined,
      processedUsers: [],
    };

    template.scheduledJobs.push(newJob);
    await template.save();

    // Start cron job only for cron-based triggers
    if (isActive && triggerType === "cron") {
      const jobId =
        template.scheduledJobs[template.scheduledJobs.length - 1]._id;
      await startCronJob(template._id, jobId, cronExpression, template);
    }

    res.status(200).json({
      success: true,
      message: "Scheduled job added successfully",
      data: template,
    });
  } catch (error) {
    console.error("Add scheduled job error:", error);
    res.status(500).json({
      success: false,
      message: "Scheduled job could not be added",
      error: error.message,
    });
  }
};

// Start cron job
const startCronJob = async (templateId, jobId, cronExpression, template) => {
  const jobKey = `${templateId}_${jobId}`;

  // Stop existing job if exists
  if (activeCronJobs.has(jobKey)) {
    activeCronJobs.get(jobKey).destroy();
  }

  const job = cron.schedule(
    cronExpression,
    async () => {
      try {
        console.log(`Running scheduled email job: ${jobKey}`);

        const currentTemplate = await MailTemplate.findById(
          templateId
        ).populate("scheduledJobs.targetUsers", "email name");

        if (!currentTemplate) return;

        const scheduledJob = currentTemplate.scheduledJobs.id(jobId);
        if (!scheduledJob || !scheduledJob.isActive) return;

        const recipients = [];

        // Get emails from target users
        if (scheduledJob.targetUsers) {
          scheduledJob.targetUsers.forEach((user) => {
            if (user.email) recipients.push(user.email);
          });
        }

        // Add target emails
        if (scheduledJob.targetEmails) {
          recipients.push(...scheduledJob.targetEmails);
        }

        // Send emails
        for (const email of recipients) {
          try {
            let htmlContent = currentTemplate.htmlContent;

            // Check if content is already wrapped (contains email-container class)
            if (!htmlContent.includes("email-container")) {
              htmlContent = wrapHtmlContent(
                htmlContent,
                currentTemplate.subject
              );
            }

            await sendEmail({
              to: email,
              subject: currentTemplate.subject,
              html: htmlContent,
              from: "<EMAIL>",
            });
          } catch (emailError) {
            console.error(`Failed to send email to ${email}:`, emailError);
          }
        }

        // Update last run time
        scheduledJob.lastRun = new Date();
        await currentTemplate.save();

        console.log(`Scheduled email job completed: ${jobKey}`);
      } catch (error) {
        console.error(`Error in scheduled job ${jobKey}:`, error);
      }
    },
    {
      scheduled: false,
    }
  );

  job.start();
  activeCronJobs.set(jobKey, job);
  console.log(`Started cron job: ${jobKey} with expression: ${cronExpression}`);
};

// Initialize all active scheduled jobs (when application starts)
exports.initializeScheduledJobs = async () => {
  try {
    const templates = await MailTemplate.find({ isActive: true });

    for (const template of templates) {
      if (template.scheduledJobs && template.scheduledJobs.length > 0) {
        for (const job of template.scheduledJobs) {
          if (job.isActive) {
            if (job.triggerType === "cron" && job.cronExpression) {
              await startCronJob(
                template._id,
                job._id,
                job.cronExpression,
                template
              );
            }
            // Event-based triggers are handled by their respective events
          }
        }
      }
    }

    console.log("Scheduled email jobs initialized");

    // Start periodic check for event-based triggers
    startEventBasedChecks();
  } catch (error) {
    console.error("Error initializing scheduled jobs:", error);
  }
};

// Start periodic checks for event-based triggers
const startEventBasedChecks = () => {
  // Check for login reminders every hour
  setInterval(async () => {
    await checkLoginReminders();
  }, 60 * 60 * 1000); // Every hour

  console.log("Event-based trigger checks started");
};

// Check for users who need login reminders
const checkLoginReminders = async () => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "login_reminder",
      "scheduledJobs.isActive": true,
    });

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "login_reminder" && job.isActive) {
          const reminderDays = job.triggerConditions?.reminderDays || 7;
          const reminderDate = new Date();
          reminderDate.setDate(reminderDate.getDate() - reminderDays);

          // Find users who haven't logged in for the specified days
          const query = {
            $or: [
              { lastLogin: { $lt: reminderDate } },
              { lastLogin: { $exists: false } },
            ],
          };

          // Apply target conditions
          if (job.targetConditions?.filterByRole?.length > 0) {
            query.role = { $in: job.targetConditions.filterByRole };
          }
          if (job.targetConditions?.filterByCompany?.length > 0) {
            query.company = { $in: job.targetConditions.filterByCompany };
          }
          if (job.targetConditions?.excludeUsers?.length > 0) {
            query._id = { $nin: job.targetConditions.excludeUsers };
          }

          const usersToRemind = await User.find(query);

          for (const user of usersToRemind) {
            // Check if user was already processed recently
            const alreadyProcessed = job.processedUsers?.some(
              (processed) =>
                processed.userId.toString() === user._id.toString() &&
                processed.processedAt > reminderDate
            );

            if (!alreadyProcessed) {
              await sendEventBasedEmail(template, job, user, "login_reminder");

              // Add to processed users
              if (!job.processedUsers) job.processedUsers = [];
              job.processedUsers.push({
                userId: user._id,
                processedAt: new Date(),
                triggerEvent: "login_reminder",
              });
            }
          }

          // Update last run
          job.lastRun = new Date();
          await template.save();
        }
      }
    }
  } catch (error) {
    console.error("Error checking login reminders:", error);
  }
};

// Handle onboarding completion trigger
exports.handleOnboardingComplete = async (userId) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "onboarding_complete",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "onboarding_complete" && job.isActive) {
          // Check if user matches target conditions
          if (await checkUserMatchesTargetConditions(user, job)) {
            // Check if user was already processed
            const alreadyProcessed = job.processedUsers?.some(
              (processed) => processed.userId.toString() === userId
            );

            if (!alreadyProcessed) {
              await sendEventBasedEmail(
                template,
                job,
                user,
                "onboarding_complete"
              );

              // Add to processed users
              if (!job.processedUsers) job.processedUsers = [];
              job.processedUsers.push({
                userId: user._id,
                processedAt: new Date(),
                triggerEvent: "onboarding_complete",
              });

              job.lastRun = new Date();
              await template.save();
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling onboarding completion:", error);
  }
};

// Handle role change trigger
exports.handleRoleChange = async (userId, fromRole, toRole) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "role_change",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "role_change" && job.isActive) {
          // Check if role change matches trigger conditions
          const conditions = job.triggerConditions;
          const roleMatches =
            (!conditions?.fromRole || conditions.fromRole === fromRole) &&
            (!conditions?.toRole || conditions.toRole === toRole);

          if (
            roleMatches &&
            (await checkUserMatchesTargetConditions(user, job))
          ) {
            await sendEventBasedEmail(template, job, user, "role_change");

            // Add to processed users
            if (!job.processedUsers) job.processedUsers = [];
            job.processedUsers.push({
              userId: user._id,
              processedAt: new Date(),
              triggerEvent: `role_change_${fromRole}_to_${toRole}`,
            });

            job.lastRun = new Date();
            await template.save();
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling role change:", error);
  }
};

// Check if user matches target conditions
const checkUserMatchesTargetConditions = async (user, job) => {
  const conditions = job.targetConditions;

  if (!conditions) return true;

  // Check role filter
  if (conditions.filterByRole?.length > 0) {
    if (!conditions.filterByRole.includes(user.role)) return false;
  }

  // Check company filter
  if (conditions.filterByCompany?.length > 0) {
    if (
      !conditions.filterByCompany.some(
        (companyId) => companyId.toString() === user.company?.toString()
      )
    )
      return false;
  }

  // Check excluded users
  if (conditions.excludeUsers?.length > 0) {
    if (
      conditions.excludeUsers.some(
        (excludedId) => excludedId.toString() === user._id.toString()
      )
    )
      return false;
  }

  return true;
};

// Send event-based email
const sendEventBasedEmail = async (template, job, user, triggerType) => {
  try {
    let htmlContent = template.htmlContent;

    // Replace user-specific variables
    const userVariables = {
      userName: user.name || "User",
      userEmail: user.email,
      userRole: user.role,
      triggerType: triggerType,
    };

    htmlContent = replaceVariables(htmlContent, userVariables, template);
    const subject = replaceVariables(template.subject, userVariables, template);

    // Check if content is already wrapped
    if (!htmlContent.includes("email-container")) {
      htmlContent = wrapHtmlContent(htmlContent, subject);
    }

    await sendEmail({
      to: user.email,
      subject: subject,
      html: htmlContent,
      from: "<EMAIL>",
    });

    console.log(`Event-based email sent: ${triggerType} to ${user.email}`);
  } catch (error) {
    console.error(`Failed to send event-based email to ${user.email}:`, error);
  }
};

// Update scheduled job
exports.updateScheduledJob = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: errors.array(),
      });
    }

    const { id, jobId } = req.params;
    const {
      name,
      triggerType,
      cronExpression,
      triggerConditions = {},
      targetUsers,
      targetEmails,
      targetConditions = {},
      isActive,
    } = req.body;

    // Validate based on trigger type
    if (triggerType === "cron") {
      if (!cronExpression || !cron.validate(cronExpression)) {
        return res.status(400).json({
          success: false,
          message: "Invalid or missing cron expression for cron trigger",
        });
      }
    } else if (triggerType === "login_reminder") {
      if (
        !triggerConditions.reminderDays ||
        triggerConditions.reminderDays < 1
      ) {
        return res.status(400).json({
          success: false,
          message:
            "Reminder days must be at least 1 for login reminder trigger",
        });
      }
    }

    const template = await MailTemplate.findById(id);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    const job = template.scheduledJobs.id(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Scheduled job not found",
      });
    }

    // Stop existing cron job if it was cron-based
    const jobKey = `${id}_${jobId}`;
    if (activeCronJobs.has(jobKey)) {
      activeCronJobs.get(jobKey).destroy();
      activeCronJobs.delete(jobKey);
    }

    // Update job properties
    job.name = name;
    job.triggerType = triggerType;
    job.cronExpression = triggerType === "cron" ? cronExpression : undefined;
    job.triggerConditions = triggerConditions;
    job.targetUsers = targetUsers;
    job.targetEmails = targetEmails;
    job.targetConditions = targetConditions;
    job.isActive = isActive;
    job.nextRun = triggerType === "cron" ? new Date() : undefined;

    // Reset processed users if trigger type changed
    if (triggerType !== "cron") {
      job.processedUsers = [];
    }

    await template.save();

    // Start new cron job if active and cron-based
    if (isActive && triggerType === "cron") {
      await startCronJob(template._id, jobId, cronExpression, template);
    }

    res.status(200).json({
      success: true,
      message: "Scheduled job updated successfully",
      data: template,
    });
  } catch (error) {
    console.error("Update scheduled job error:", error);
    res.status(500).json({
      success: false,
      message: "Scheduled job could not be updated",
      error: error.message,
    });
  }
};

// Stop scheduled job
exports.stopScheduledJob = async (req, res) => {
  try {
    const { id, jobId } = req.params;

    const template = await MailTemplate.findById(id);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    const job = template.scheduledJobs.id(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Scheduled job not found",
      });
    }

    job.isActive = false;
    await template.save();

    // Stop cron job
    const jobKey = `${id}_${jobId}`;
    if (activeCronJobs.has(jobKey)) {
      activeCronJobs.get(jobKey).destroy();
      activeCronJobs.delete(jobKey);
    }

    res.status(200).json({
      success: true,
      message: "Scheduled job stopped successfully",
    });
  } catch (error) {
    console.error("Stop scheduled job error:", error);
    res.status(500).json({
      success: false,
      message: "Scheduled job could not be stopped",
      error: error.message,
    });
  }
};

// Restart scheduled job
exports.restartScheduledJob = async (req, res) => {
  try {
    const { id, jobId } = req.params;

    const template = await MailTemplate.findById(id);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    const job = template.scheduledJobs.id(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Scheduled job not found",
      });
    }

    // Activate the job
    job.isActive = true;
    job.nextRun = job.triggerType === "cron" ? new Date() : undefined;
    await template.save();

    // Start cron job if it's cron-based
    if (job.triggerType === "cron" && job.cronExpression) {
      await startCronJob(template._id, jobId, job.cronExpression, template);
    }

    res.status(200).json({
      success: true,
      message: "Scheduled job restarted successfully",
      data: template,
    });
  } catch (error) {
    console.error("Restart scheduled job error:", error);
    res.status(500).json({
      success: false,
      message: "Scheduled job could not be restarted",
      error: error.message,
    });
  }
};

// Delete scheduled job completely
exports.deleteScheduledJob = async (req, res) => {
  try {
    const { id, jobId } = req.params;

    const template = await MailTemplate.findById(id);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: "Template not found",
      });
    }

    const job = template.scheduledJobs.id(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Scheduled job not found",
      });
    }

    // Stop and remove cron job
    const jobKey = `${id}_${jobId}`;
    if (activeCronJobs.has(jobKey)) {
      activeCronJobs.get(jobKey).destroy();
      activeCronJobs.delete(jobKey);
    }

    // Remove job from template
    template.scheduledJobs.pull(jobId);
    await template.save();

    res.status(200).json({
      success: true,
      message: "Scheduled job deleted successfully",
    });
  } catch (error) {
    console.error("Delete scheduled job error:", error);
    res.status(500).json({
      success: false,
      message: "Scheduled job could not be deleted",
      error: error.message,
    });
  }
};

// Migrate existing templates to the new system
exports.migrateExistingTemplates = async (req, res) => {
  try {
    console.log("Starting email template migration...");

    const fs = require("fs");
    const path = require("path");

    // Define templates to migrate
    const templatesToMigrate = [
      {
        name: "Feedback Mail Confirmation",
        subject: "Your feedback has been received - AI Business School",
        htmlFile: "feedbackMail.html",
        description:
          "Confirmation email sent to users after submitting feedback",
        tags: ["feedback", "confirmation", "support"],
        variables: [
          {
            name: "userName",
            defaultValue: "User",
            description: "Name of the user who submitted feedback",
          },
        ],
      },
      {
        name: "Support Request Notification",
        subject: "New Support Request - AI Business School",
        htmlFile: "supportRequestNotification.html",
        description:
          "Internal notification sent to support team when a new request is submitted",
        tags: ["support", "notification", "internal"],
        variables: [
          {
            name: "userName",
            defaultValue: "User",
            description: "Name of the user who submitted the request",
          },
          {
            name: "userEmail",
            defaultValue: "<EMAIL>",
            description: "Email of the user",
          },
          {
            name: "timestamp",
            defaultValue: "Now",
            description: "When the request was submitted",
          },
          {
            name: "formType",
            defaultValue: "Support",
            description: "Type of the form submitted",
          },
          {
            name: "formTitle",
            defaultValue: "Support Request",
            description: "Title of the form",
          },
          {
            name: "formDescription",
            defaultValue: "Support request",
            description: "Description of the form",
          },
          {
            name: "formResponses",
            defaultValue: "No responses",
            description: "HTML formatted form responses",
          },
        ],
      },
      {
        name: "Forgot Password OTP",
        subject: "Password Reset Code - AI Business School",
        htmlFile: "forgotPassword.html",
        description: "Password reset email with OTP code",
        tags: ["password", "reset", "otp", "security"],
        variables: [
          {
            name: "userName",
            defaultValue: "User",
            description: "Name of the user requesting password reset",
          },
          {
            name: "otp",
            defaultValue: "123456",
            description: "One-time password for reset",
          },
        ],
      },
      {
        name: "Password Change Confirmation",
        subject: "Password Successfully Changed - AI Business School",
        htmlFile: "forgotPasswordChange.html",
        description: "Confirmation email sent after successful password change",
        tags: ["password", "confirmation", "security"],
        variables: [
          {
            name: "userName",
            defaultValue: "User",
            description: "Name of the user whose password was changed",
          },
        ],
      },
      {
        name: "User Invitation",
        subject: "Welcome to AI Business School - Account Activation",
        htmlFile: "inviteMail.html",
        description:
          "Invitation email sent to new users for account activation",
        tags: ["invitation", "welcome", "activation"],
        variables: [
          {
            name: "greeting",
            defaultValue: "Hello",
            description: "Greeting message",
          },
          {
            name: "welcomeMessage",
            defaultValue: "Welcome message",
            description: "Welcome text",
          },
          {
            name: "activationMessage",
            defaultValue: "Activation instructions",
            description: "Activation instructions",
          },
          {
            name: "buttonText",
            defaultValue: "Activate Account",
            description: "Button text",
          },
          {
            name: "linkValidityMessage",
            defaultValue: "Link validity info",
            description: "Link validity message",
          },
          {
            name: "supportMessage",
            defaultValue: "Support information",
            description: "Support contact info",
          },
          {
            name: "verificationLink",
            defaultValue: "#",
            description: "Account activation link",
          },
          {
            name: "name",
            defaultValue: "User",
            description: "User name for personalization",
          },
        ],
      },
    ];

    const templateDir = path.join(__dirname, "../utils/emailTemplates");

    for (const templateConfig of templatesToMigrate) {
      // Check if template already exists
      const existingTemplate = await MailTemplate.findOne({
        name: templateConfig.name,
      });

      if (existingTemplate) {
        console.log(
          `Template "${templateConfig.name}" already exists, skipping...`
        );
        continue;
      }

      // Read HTML file
      const htmlFilePath = path.join(templateDir, templateConfig.htmlFile);
      let htmlContent = "";

      try {
        htmlContent = fs.readFileSync(htmlFilePath, "utf8");

        // Convert old variable format ${variable} to new format {{variable}}
        htmlContent = htmlContent
          .replace(/\$\{response\[0\]\?\.name\}/g, "{{userName}}")
          .replace(/\$\{updatedUser\?\.name\}/g, "{{userName}}")
          .replace(/\$\{otp\}/g, "{{otp}}")
          .replace(/\$\{userName\}/g, "{{userName}}")
          .replace(/\$\{userEmail\}/g, "{{userEmail}}")
          .replace(/\$\{timestamp\}/g, "{{timestamp}}")
          .replace(/\$\{formType\}/g, "{{formType}}")
          .replace(/\$\{formTitle\}/g, "{{formTitle}}")
          .replace(/\$\{formDescription\}/g, "{{formDescription}}")
          .replace(/\$\{formResponses\}/g, "{{formResponses}}")
          .replace(
            /\$\{new Date\(\)\.getFullYear\(\)\}/g,
            new Date().getFullYear()
          );
      } catch (error) {
        console.error(
          `Error reading ${templateConfig.htmlFile}:`,
          error.message
        );
        continue;
      }

      // Create template
      const newTemplate = new MailTemplate({
        name: templateConfig.name,
        subject: templateConfig.subject,
        htmlContent: htmlContent,
        textContent: `Please view this email in HTML format.`,
        variables: templateConfig.variables || [],
        tags: templateConfig.tags || [],
        isActive: true,
        createdBy: null, // System template
      });

      await newTemplate.save();
      console.log(`✅ Migrated template: ${templateConfig.name}`);
    }

    console.log("Email template migration completed successfully!");
    res.status(200).json({
      success: true,
      message: "Email templates migrated successfully",
    });
  } catch (error) {
    console.error("Error during template migration:", error);
    res.status(500).json({
      success: false,
      message: "Migration failed",
      error: error.message,
    });
  }
};

// Helper function to get template by name
exports.getTemplateByName = async (templateName) => {
  try {
    const template = await MailTemplate.findOne({
      name: templateName,
      isActive: true,
    });
    return template;
  } catch (error) {
    console.error("Error getting template by name:", error);
    return null;
  }
};

// Helper function to send email using template by name
exports.sendEmailByTemplate = async (
  templateName,
  recipientEmail,
  variables = {}
) => {
  try {
    const template = await exports.getTemplateByName(templateName);

    if (!template) {
      throw new Error(`Template "${templateName}" not found`);
    }

    // Replace variables in subject and content
    let subject = replaceVariables(template.subject, variables, template);
    let htmlContent = replaceVariables(
      template.htmlContent,
      variables,
      template
    );

    // Send email
    await sendEmail({
      to: recipientEmail,
      subject: subject,
      html: htmlContent,
      from: "<EMAIL>",
    });

    console.log(
      `Email sent using template "${templateName}" to ${recipientEmail}`
    );
    return true;
  } catch (error) {
    console.error(
      `Error sending email with template "${templateName}":`,
      error
    );
    throw error;
  }
};

// Create default scheduled jobs for migrated templates
exports.createDefaultScheduledJobs = async () => {
  try {
    console.log("Creating default scheduled jobs for migrated templates...");

    // Default scheduled jobs configurations
    const defaultScheduledJobs = [
      {
        templateName: "User Invitation",
        jobs: [
          {
            name: "Inactive Users Reminder",
            triggerType: "login_reminder",
            triggerConditions: {
              reminderDays: 7, // 7 gün login yapmamışlar için hatırlatma
            },
            targetConditions: {
              filterByRole: ["Administrator", "user"],
            },
            isActive: true,
          },
        ],
      },
      {
        templateName: "Password Change Confirmation",
        jobs: [
          {
            name: "Security Alert for Suspicious Activity",
            triggerType: "login_reminder",
            triggerConditions: {
              reminderDays: 30, // 30 gün şifre değiştirilmemişler için güvenlik uyarısı
            },
            targetConditions: {
              filterByRole: ["Administrator"],
            },
            isActive: false, // Varsayılan olarak kapalı
          },
        ],
      },
      {
        templateName: "Feedback Mail Confirmation",
        jobs: [
          {
            name: "Monthly Feedback Follow-up",
            triggerType: "cron",
            cronExpression: "0 10 1 * *", // Her ayın 1'inde saat 10:00'da
            targetConditions: {
              filterByRole: ["user", "Administrator"],
            },
            isActive: false, // Varsayılan olarak kapalı
          },
        ],
      },
    ];

    for (const config of defaultScheduledJobs) {
      const template = await MailTemplate.findOne({
        name: config.templateName,
      });

      if (!template) {
        console.log(
          `Template "${config.templateName}" not found, skipping scheduled jobs`
        );
        continue;
      }

      for (const jobConfig of config.jobs) {
        // Check if job already exists
        const existingJob = template.scheduledJobs.find(
          (job) => job.name === jobConfig.name
        );

        if (existingJob) {
          console.log(
            `Scheduled job "${jobConfig.name}" already exists for template "${config.templateName}"`
          );
          continue;
        }

        // Create new scheduled job
        const newJob = {
          name: jobConfig.name,
          triggerType: jobConfig.triggerType,
          cronExpression: jobConfig.cronExpression,
          triggerConditions: jobConfig.triggerConditions || {},
          targetUsers: jobConfig.targetUsers || [],
          targetEmails: jobConfig.targetEmails || [],
          targetConditions: jobConfig.targetConditions || {},
          isActive: jobConfig.isActive,
          nextRun: jobConfig.triggerType === "cron" ? new Date() : undefined,
          processedUsers: [],
        };

        template.scheduledJobs.push(newJob);
        await template.save();

        // Start cron job if active and cron-based
        if (jobConfig.isActive && jobConfig.triggerType === "cron") {
          const jobId =
            template.scheduledJobs[template.scheduledJobs.length - 1]._id;
          await startCronJob(
            template._id,
            jobId,
            jobConfig.cronExpression,
            template
          );
        }

        console.log(
          `✅ Created scheduled job "${jobConfig.name}" for template "${config.templateName}"`
        );
      }
    }

    console.log("Default scheduled jobs creation completed!");
    return true;
  } catch (error) {
    console.error("Error creating default scheduled jobs:", error);
    return false;
  }
};

// Manual fix for templates that weren't properly migrated
exports.fixTemplateVariables = async (req, res) => {
  try {
    console.log("Starting manual template variable fix...");

    // Get all templates
    const templates = await MailTemplate.find();

    let fixedCount = 0;

    for (const template of templates) {
      let htmlContent = template.htmlContent;
      let originalContent = htmlContent;

      // Replace all old variable formats with new ones
      htmlContent = htmlContent
        .replace(/\$\{response\[0\]\?\\.name\}/g, "{{userName}}")
        .replace(/\$\{updatedUser\?\\.name\}/g, "{{userName}}")
        .replace(/\$\{otp\}/g, "{{otp}}")
        .replace(/\$\{userName\}/g, "{{userName}}")
        .replace(/\$\{userEmail\}/g, "{{userEmail}}")
        .replace(/\$\{timestamp\}/g, "{{timestamp}}")
        .replace(/\$\{formType\}/g, "{{formType}}")
        .replace(/\$\{formTitle\}/g, "{{formTitle}}")
        .replace(/\$\{formDescription\}/g, "{{formDescription}}")
        .replace(/\$\{formResponses\}/g, "{{formResponses}}")
        .replace(
          /\$\{new Date\(\)\.getFullYear\(\)\}/g,
          new Date().getFullYear()
        );

      // If content changed, update the template
      if (htmlContent !== originalContent) {
        template.htmlContent = htmlContent;
        await template.save();
        fixedCount++;
        console.log(`✅ Fixed template: ${template.name}`);
      }
    }

    console.log(
      `Manual template fix completed! Fixed ${fixedCount} templates.`
    );

    res.status(200).json({
      success: true,
      message: `Template variables fixed successfully. Updated ${fixedCount} templates.`,
      fixedCount: fixedCount,
    });
  } catch (error) {
    console.error("Error fixing template variables:", error);
    res.status(500).json({
      success: false,
      message: "Template variable fix failed",
      error: error.message,
    });
  }
};

// Handle onboarding started trigger
exports.handleOnboardingStarted = async (userId) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "onboarding_started",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "onboarding_started" && job.isActive) {
          if (await checkUserMatchesTargetConditions(user, job)) {
            const alreadyProcessed = job.processedUsers?.some(
              (processed) => processed.userId.toString() === userId
            );

            if (!alreadyProcessed) {
              await sendEventBasedEmail(
                template,
                job,
                user,
                "onboarding_started"
              );

              if (!job.processedUsers) job.processedUsers = [];
              job.processedUsers.push({
                userId: user._id,
                processedAt: new Date(),
                triggerEvent: "onboarding_started",
              });

              job.lastRun = new Date();
              await template.save();
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling onboarding started:", error);
  }
};

// Handle password reset requested trigger
exports.handlePasswordResetRequested = async (userId) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "password_reset_requested",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "password_reset_requested" && job.isActive) {
          if (await checkUserMatchesTargetConditions(user, job)) {
            await sendEventBasedEmail(
              template,
              job,
              user,
              "password_reset_requested"
            );

            job.lastRun = new Date();
            await template.save();
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling password reset requested:", error);
  }
};

// Handle password changed trigger
exports.handlePasswordChanged = async (userId) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "password_changed",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "password_changed" && job.isActive) {
          if (await checkUserMatchesTargetConditions(user, job)) {
            await sendEventBasedEmail(template, job, user, "password_changed");

            job.lastRun = new Date();
            await template.save();
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling password changed:", error);
  }
};

// Handle account created trigger
exports.handleAccountCreated = async (userId) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "account_created",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "account_created" && job.isActive) {
          if (await checkUserMatchesTargetConditions(user, job)) {
            await sendEventBasedEmail(template, job, user, "account_created");

            if (!job.processedUsers) job.processedUsers = [];
            job.processedUsers.push({
              userId: user._id,
              processedAt: new Date(),
              triggerEvent: "account_created",
            });

            job.lastRun = new Date();
            await template.save();
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling account created:", error);
  }
};

// Handle form submitted trigger
exports.handleFormSubmitted = async (userId, formType) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "form_submitted",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "form_submitted" && job.isActive) {
          // Check if form type matches (if specified)
          const triggerFormType = job.triggerConditions?.formType;
          const formMatches = !triggerFormType || triggerFormType === formType;

          if (
            formMatches &&
            (await checkUserMatchesTargetConditions(user, job))
          ) {
            await sendEventBasedEmail(template, job, user, "form_submitted");

            job.lastRun = new Date();
            await template.save();
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling form submitted:", error);
  }
};

// Handle course completed trigger
exports.handleCourseCompleted = async (userId, courseId) => {
  try {
    const templates = await MailTemplate.find({
      isActive: true,
      "scheduledJobs.triggerType": "course_completed",
      "scheduledJobs.isActive": true,
    });

    const user = await User.findById(userId);
    if (!user) return;

    for (const template of templates) {
      for (const job of template.scheduledJobs) {
        if (job.triggerType === "course_completed" && job.isActive) {
          // Check if course matches (if specified)
          const triggerCourseId = job.triggerConditions?.courseId;
          const courseMatches =
            !triggerCourseId || triggerCourseId === courseId;

          if (
            courseMatches &&
            (await checkUserMatchesTargetConditions(user, job))
          ) {
            const userVariables = {
              userName: user.name || "User",
              userEmail: user.email,
              courseId: courseId,
            };

            // Send email with course-specific variables
            let htmlContent = replaceVariables(
              template.htmlContent,
              userVariables,
              template
            );
            const subject = replaceVariables(
              template.subject,
              userVariables,
              template
            );

            if (!htmlContent.includes("email-container")) {
              htmlContent = wrapHtmlContent(htmlContent, subject);
            }

            await sendEmail({
              to: user.email,
              subject: subject,
              html: htmlContent,
              from: "<EMAIL>",
            });

            job.lastRun = new Date();
            await template.save();
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling course completed:", error);
  }
};
