const ReportFilesModel = require("../models/ReportFilesModel");
const ReportModel = require("../models/ReportModel");
const UserModel = require("../models/UserModel");
const { httpResponse } = require("../utils/helpers");
const { HTTP_CODES } = require("../utils/enum");
const mongoose = require("mongoose");
const ExcelJS = require("exceljs");
const { BlobServiceClient } = require("@azure/storage-blob");

// Azure Storage configuration
const azureStorageConnectionString =
  process.env.AZURE_STORAGE_CONNECTION_STRING;
const containerName =
  process.env.AZURE_STORAGE_CONTAINER_NAME || "excel-reports";

// File upload helper function for Excel files
const uploadExcelToAzure = async (buffer, fileName) => {
  try {
    if (!azureStorageConnectionString) {
      throw new Error("Azure Storage connection string not configured");
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(
      azureStorageConnectionString
    );
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "blob",
    });

    const blobName = `${Date.now()}-${fileName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    await blockBlobClient.upload(buffer, buffer.length, {
      blobHTTPHeaders: {
        blobContentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
    });

    return {
      url: blockBlobClient.url,
      name: blobName,
    };
  } catch (error) {
    console.error("Error uploading Excel file to Azure:", error);
    throw error;
  }
};

// Excel generation functions for different report types
const generateTrainingExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Training_Report");

  // Calculate percentages
  const cohortSize = reportData.cohortSize || 0;
  const loggedInUsers = reportData.loggedInUsers || 0;

  // Get today's date in dd-mm-yyyy format
  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const formattedDate = `${day}-${month}-${year}`;

  // Training metrics based on CSV format
  const worksheetData = [
    [`Training progress ${formattedDate}`, "Participants", "%"],
    ["Cohort size", cohortSize, ""],
    ["Logged in", loggedInUsers, "100%"], // Always 100% since report is based on logged in users
    ["Beginner", "", ""],
    [
      "First certificate achieved (Introduction program)",
      reportData.introductionProgramCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.introductionProgramCompleted || 0) / loggedInUsers) *
              100
          )}%`
        : "0%",
    ],
    [
      "Second certificate achieved (Successfully navigate AI risks program)",
      reportData.riskProgramCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.riskProgramCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "0%",
    ],
    [
      "Third certificate achieved (Journey completed)",
      reportData.beginnerJourneyCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.beginnerJourneyCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "0%",
    ],
    ["Expert", "", ""],
    [
      "Fourth certificate achieved (Job-specific use case training)",
      reportData.expertJourneyJobSpecificCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.expertJourneyJobSpecificCompleted || 0) /
              loggedInUsers) *
              100
          )}%`
        : "0%",
    ],
    [
      "Fifth certificate achieved (Journey completed)",
      reportData.expertJourneyCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.expertJourneyCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "0%",
    ],
    ["Master", "", ""],
    [
      "Sixth certificate achieved",
      reportData.masterJourneyCompleted || 0,
      (reportData.masterJourneyCompleted || 0) > 0
        ? `${Math.round(
            ((reportData.masterJourneyCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "N/A",
    ],
  ];

  // Add data to worksheet
  worksheet.addRows(worksheetData);

  // Set column widths
  worksheet.columns = [
    { width: 60 }, // Description column
    { width: 15 }, // Participants column
    { width: 10 }, // Percentage column
  ];

  // Add styling to cells
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell, colNumber) => {
      // Header row styling (row 1) - #366092
      if (rowNumber === 1) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF366092" }, // Dark blue background
        };
        cell.font = { color: { argb: "FFFFFFFF" }, bold: true }; // White bold text
        cell.alignment = { horizontal: "center", vertical: "middle" };
      }
      // Cohort size and Logged in rows (rows 2-3) - #EBF5FB
      else if (rowNumber === 2 || rowNumber === 3) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFEBF5FB" },
        };
        cell.font = { bold: true };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
      // Group headers (Beginner, Expert, Master) - rows 4, 8, 11 - #7D9CD8
      else if (rowNumber === 4 || rowNumber === 8 || rowNumber === 11) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF7D9CD8" },
        };
        if (colNumber === 1) {
          // Only first column
          cell.font = { bold: true, color: { argb: "FFFFFFFF" } }; // White bold text
          cell.alignment = { horizontal: "left", vertical: "middle" };
        } else {
          cell.alignment = { horizontal: "center", vertical: "middle" };
        }
      }
      // First, Second, Third certificate rows (5-7) - #F2F7FF
      else if (rowNumber === 5 || rowNumber === 6 || rowNumber === 7) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF2F7FF" },
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
      // Fourth and Fifth certificate rows (9-10) - #FEF7F0
      else if (rowNumber === 9 || rowNumber === 10) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFFEF7F0" },
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
      // Sixth certificate row (12) - #FEEAD6
      else if (rowNumber === 12) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFFEEAD6" },
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
    });
  });

  return await workbook.xlsx.writeBuffer();
};

const generateNextgenExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("NextGen_Talent_Radar");

  // Sort users by totalAIScore in descending order for ranking
  const sortedUsers = [...reportData].sort(
    (a, b) => (b.totalAIScore || 0) - (a.totalAIScore || 0)
  );

  // Header row matching CSV format
  const headers = [
    "Ranking",
    "Rating points",
    "Name",
    "Number\nuse cases applied",
    "Number\ndifferent use cases applied",
    "Number ideas\nsubmitted",
    "Number of\ncertificates obtained",
    "Number of\ncreated AI solutions",
    "Completed\ntraining journeys",
  ];

  worksheet.addRow(headers);

  // Add data rows
  sortedUsers.forEach((user, index) => {
    const ranking = index + 1;
    const fullName = `${user.name || ""} ${user.surname || ""}`.trim();

    worksheet.addRow([
      ranking,
      user.totalAIScore || 0,
      fullName,
      user.totalUsecaseUsage || 0,
      user.totalUniqueUsecase || 0,
      user.totalIdeas || 0,
      user.totalCertificates || 0,
      user.totalAISolutions || 0,
      user.totalJourneyCompleted || 0,
    ]);
  });

  // Set column widths for better readability
  worksheet.columns = [
    { width: 10 }, // Ranking
    { width: 15 }, // Rating points
    { width: 20 }, // Name
    { width: 15 }, // Number use cases applied
    { width: 18 }, // Number different use cases applied
    { width: 15 }, // Number ideas submitted
    { width: 18 }, // Number of certificates obtained
    { width: 18 }, // Number of created AI solutions
    { width: 18 }, // Completed training journeys
  ];

  // Column background colors
  const columnColors = [
    "FFFFFFFF", // Ranking - white
    "FFF2F4F4", // Rating points - F2F4F4
    "FFFFFFFF", // Name - white
    "FFEAF2F8", // Number use cases applied - EAF2F8
    "FFD4E6F1", // Number different use cases applied - D4E6F1
    "FFAED6F1", // Number ideas submitted - AED6F1
    "FF7FB3D5", // Number of certificates obtained - 7FB3D5
    "FF85C1E9", // Number of created AI solutions - 85C1E9
    "FF85C1E9", // Completed training journeys - 85C1E9
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.eachCell((cell, colNumber) => {
    // Different styling for first 3 columns vs others
    if (colNumber <= 3) {
      // Ranking, Rating points, Name headers: white background, black text
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFFFFFFF" }, // White background
      };
      cell.font = {
        color: { argb: "FF000000" }, // Black text
        bold: true,
      };
    } else {
      // Other headers: #366092 background, white text
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FF366092" }, // Dark blue background
      };
      cell.font = {
        color: { argb: "FFFFFFFF" }, // White text
        bold: true,
      };
    }

    cell.alignment = {
      horizontal: "center",
      vertical: "middle",
      wrapText: true,
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
  });

  // Style data rows with column-specific colors
  for (let i = 2; i <= worksheet.rowCount; i++) {
    const row = worksheet.getRow(i);
    row.eachCell((cell, colNumber) => {
      // Center align numbers, left align names
      cell.alignment = {
        horizontal: colNumber === 3 ? "left" : "center", // Name column left, others center
        vertical: "middle",
      };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // Apply column-specific background color
      if (colNumber <= columnColors.length) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: columnColors[colNumber - 1] },
        };
      }
    });
  }

  return await workbook.xlsx.writeBuffer();
};

const generateIdeationExcel = (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // Ideation data processing
  const worksheetData = [];

  if (reportData.ideas) {
    worksheetData.push([
      "Idea Title",
      "Description",
      "Submitter",
      "Category",
      "Rating",
      "Status",
    ]);
    reportData.ideas.forEach((idea) => {
      worksheetData.push([
        idea.title || "",
        idea.description || "",
        idea.submitter || "",
        idea.category || "",
        idea.rating || "",
        idea.status || "",
      ]);
    });
  }

  const worksheet = workbook.addWorksheet("Ideation Report");
  worksheet.addRows(worksheetData);

  return workbook.xlsx.writeBuffer();
};

const generateCreatorExcel = (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // Creator data processing
  const worksheetData = [];

  if (reportData.creators) {
    worksheetData.push([
      "Creator Name",
      "Content Type",
      "Views",
      "Engagement",
      "Revenue",
    ]);
    reportData.creators.forEach((creator) => {
      worksheetData.push([
        creator.name || "",
        creator.contentType || "",
        creator.views || "",
        creator.engagement || "",
        creator.revenue || "",
      ]);
    });
  }

  const worksheet = workbook.addWorksheet("Creator Report");
  worksheet.addRows(worksheetData);

  return workbook.xlsx.writeBuffer();
};

const generateApplicationExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Use_Case_Application_Report");

  // Get today's date in dd-mm-yyyy format
  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const formattedDate = `${day}-${month}-${year}`;

  // Extract data
  const totalActiveUser = reportData.totalActiveUser || 0;
  const totalUsecaseUsage = reportData.totalUsecaseUsage || 0;
  const totalUniqueUsecase = reportData.totalUniqueUsecase || 0;

  // Calculate averages
  const avgUsecasePerUser =
    totalActiveUser > 0
      ? (totalUsecaseUsage / totalActiveUser).toFixed(1)
      : "0";
  const avgUniqueUsecasePerUser =
    totalActiveUser > 0
      ? (totalUniqueUsecase / totalActiveUser).toFixed(1)
      : "0";

  // Create data rows matching CSV format
  const reportRows = [
    [`Use case applications (${formattedDate})`],
    [`Total of active users`, totalActiveUser],
    [`Total number of use cases applied`, totalUsecaseUsage],
    [
      `Total number of different types of use cases applied`,
      totalUniqueUsecase,
    ],
    [`Average number of use cases applied per user`, avgUsecasePerUser],
    [
      `Average number of different types of use cases applied per user`,
      avgUniqueUsecasePerUser,
    ],
  ];

  // Add rows to worksheet
  reportRows.forEach((row) => {
    worksheet.addRow(row);
  });

  // Set column widths
  worksheet.columns = [
    { width: 60 }, // Description column
    { width: 20 }, // Value column
  ];

  // Row-specific background colors for descriptions
  const rowColors = {
    2: "FFB4EAF5", // Total of active users
    3: "FF93CFDC", // Total number of use cases applied
    4: "FF75B7C5", // Total number of different types of use cases applied
    5: "FF5299A8", // Average number of use cases applied per user
    6: "FF4B7F8A", // Average number of different types of use cases applied per user
  };

  // Style the rows
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell, colNumber) => {
      if (rowNumber === 1) {
        // Header row styling
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF1D6799" }, // Header background color
        };
        cell.font = {
          color: { argb: "FFFFFFFF" }, // White text
          bold: false,
          size: 12,
        };
        cell.alignment = {
          horizontal: "left",
          vertical: "middle",
        };
      } else {
        // Data rows styling
        if (colNumber === 1) {
          // Description column - specific color for each row
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: rowColors[rowNumber] || "FFFFFFFF" },
          };
          cell.font = { bold: false };
          cell.alignment = {
            horizontal: "left",
            vertical: "middle",
          };
        } else {
          // Value column - consistent color for all numbers
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFFFDDC3" }, // Numbers column background color
          };
          cell.alignment = {
            horizontal: "right",
            vertical: "middle",
          };
          cell.font = { bold: true };
        }
      }

      // No borders - removed border styling
    });
  });

  // Merge the header row across both columns
  worksheet.mergeCells("A1:B1");

  return await workbook.xlsx.writeBuffer();
};

const generateAiValueExcel = (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // AI Value data processing
  const worksheetData = [];

  if (reportData.metrics) {
    worksheetData.push([
      "Metric Name",
      "Value",
      "Improvement",
      "ROI",
      "Impact",
    ]);
    reportData.metrics.forEach((metric) => {
      worksheetData.push([
        metric.name || "",
        metric.value || "",
        metric.improvement || "",
        metric.roi || "",
        metric.impact || "",
      ]);
    });
  }

  const worksheet = workbook.addWorksheet("AI Value Report");
  worksheet.addRows(worksheetData);

  return workbook.xlsx.writeBuffer();
};

const generateMsTrainingExcel = (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // MS Training data processing
  const worksheetData = [];

  if (reportData.trainees) {
    worksheetData.push([
      "Trainee Name",
      "Course",
      "Progress",
      "Certification",
      "Date",
    ]);
    reportData.trainees.forEach((trainee) => {
      worksheetData.push([
        trainee.name || "",
        trainee.course || "",
        trainee.progress || "",
        trainee.certification || "",
        trainee.date || "",
      ]);
    });
  }

  const worksheet = workbook.addWorksheet("MS Training Report");
  worksheet.addRows(worksheetData);

  return workbook.xlsx.writeBuffer();
};

// Excel generation dispatcher
const generateExcelByType = async (reportType, reportData) => {
  switch (reportType) {
    case "training":
      return await generateTrainingExcel(reportData);
    case "nextgen":
      return await generateNextgenExcel(reportData);
    case "ideation":
      return await generateIdeationExcel(reportData);
    case "creator":
      return await generateCreatorExcel(reportData);
    case "application":
      return await generateApplicationExcel(reportData);
    case "ai-value":
      return await generateAiValueExcel(reportData);
    case "ms-training":
      return await generateMsTrainingExcel(reportData);
    default:
      throw new Error(`Unsupported report type: ${reportType}`);
  }
};

// Background job processor (simple queue system)
const processExcelGeneration = async (reportFileId) => {
  try {
    console.log(
      `Starting Excel generation for report file ID: ${reportFileId}`
    );

    const reportFile = await ReportFilesModel.findById(reportFileId);
    if (!reportFile) {
      throw new Error("Report file record not found");
    }

    // Update status
    await ReportFilesModel.findByIdAndUpdate(reportFileId, {
      status: "processing",
    });

    // Fetch report data
    const report = await ReportModel.findById(reportFile.reportId);
    if (!report) {
      throw new Error("Report not found");
    }

    console.log(
      `Generating Excel for company: ${report.companyName}, type: ${report.reportType}`
    );

    // Generate Excel file
    const excelBuffer = await generateExcelByType(
      report.reportType,
      report.reportData
    );

    // Create file name
    const fileName = `${report.companyName}_${report.reportType}_report_${
      new Date().toISOString().split("T")[0]
    }.xlsx`;

    // Upload to Azure
    const uploadResult = await uploadExcelToAzure(excelBuffer, fileName);

    // Update as successful
    await ReportFilesModel.findByIdAndUpdate(reportFileId, {
      status: "ready",
      fileUrl: uploadResult.url,
      fileName: uploadResult.name,
      completedAt: new Date(),
    });

    console.log(
      `Excel file generated successfully for report file ID: ${reportFileId}`
    );
  } catch (error) {
    console.error("Error processing Excel generation:", error);

    // Update error status
    await ReportFilesModel.findByIdAndUpdate(reportFileId, {
      status: "failed",
      error: error.message,
    });
  }
};

// Request Excel file generation
exports.generateExcel = async (req, res) => {
  try {
    const { reportId } = req.body;

    if (!reportId) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Report ID is required"
      );
    }

    // ObjectId validation
    if (!mongoose.Types.ObjectId.isValid(reportId)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid report ID format"
      );
    }

    // User validation
    if (!req.user || !req.user.email) {
      return httpResponse(
        res,
        HTTP_CODES.UNAUTHORIZED,
        "error",
        "User not authenticated"
      );
    }

    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    // Check if report exists
    const report = await ReportModel.findById(reportId);
    if (!report) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Report not found"
      );
    }

    // Check if Excel file has already been generated for this report
    const existingReportFile = await ReportFilesModel.findOne({
      reportId: reportId,
      requestedBy: user._id,
      status: { $in: ["pending", "processing", "ready"] },
    });

    if (existingReportFile) {
      return httpResponse(
        res,
        HTTP_CODES.OK,
        "success",
        "Excel file request already exists",
        existingReportFile
      );
    }

    // Create new report file record
    const reportFile = new ReportFilesModel({
      reportId: reportId,
      requestedBy: user._id,
      status: "pending",
    });

    await reportFile.save();

    // Start background process (in production, use Redis queue)
    setTimeout(() => {
      processExcelGeneration(reportFile._id);
    }, 1000);

    return httpResponse(
      res,
      HTTP_CODES.CREATED,
      "success",
      "Excel file generation request created successfully",
      reportFile
    );
  } catch (error) {
    console.error("Error in generateExcel:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Get Excel file status
exports.getStatus = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId validation
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid report file ID format"
      );
    }

    const reportFile = await ReportFilesModel.findById(id)
      .populate("reportId", "companyName reportType")
      .populate("requestedBy", "name email");

    if (!reportFile) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Report file not found"
      );
    }

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Report file status retrieved successfully",
      reportFile
    );
  } catch (error) {
    console.error("Error in getStatus:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// List user's Excel file requests
exports.list = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;

    // User validation
    if (!req.user || !req.user.email) {
      return httpResponse(
        res,
        HTTP_CODES.UNAUTHORIZED,
        "error",
        "User not authenticated"
      );
    }

    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const query = { requestedBy: user._id };
    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const [docs, totalDocs] = await Promise.all([
      ReportFilesModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate("reportId", "companyName reportType")
        .populate("requestedBy", "name email"),
      ReportFilesModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(totalDocs / parseInt(limit));

    const result = {
      docs,
      totalDocs,
      limit: parseInt(limit),
      totalPages,
      page: parseInt(page),
      pagingCounter: skip + 1,
      hasPrevPage: parseInt(page) > 1,
      hasNextPage: parseInt(page) < totalPages,
      prevPage: parseInt(page) > 1 ? parseInt(page) - 1 : null,
      nextPage: parseInt(page) < totalPages ? parseInt(page) + 1 : null,
    };

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Report files retrieved successfully",
      result
    );
  } catch (error) {
    console.error("Error in list:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Download Excel file
exports.download = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId validation
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid report file ID format"
      );
    }

    const reportFile = await ReportFilesModel.findById(id).populate(
      "reportId",
      "companyName reportType"
    );

    if (!reportFile) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Report file not found"
      );
    }

    if (reportFile.status !== "ready") {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        `File is not ready for download. Current status: ${reportFile.status}`
      );
    }

    if (!reportFile.fileUrl) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "File URL not found"
      );
    }

    // Redirect to Azure blob
    res.redirect(reportFile.fileUrl);
  } catch (error) {
    console.error("Error in download:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
