const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;
const AppTrackingModel = require("../../models/AppTrackingModel");
const SimpleAppModel = require("../../models/SimpleAppModel");
const WorkflowModel = require("../../models/WorkflowModel");
const Certificate = require("../../models/CertificateModel");
const CourseTrackingModel = require("../../models/CourseTrackingModel");
const UserModel = require("../../models/UserModel");
const FormResponseModel = require("../../models/FormResponse");
const mongoose = require("mongoose");
const ReportModel = require("../../models/ReportModel");
const CompanyModel = require("../../models/CompanyModel");
exports.generateNextgenTalentReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Nextgen Talent",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Find all users in the company
    const users = await UserModel.find({
      company: new mongoose.Types.ObjectId(companyId),
    });
    const company = await CompanyModel.findById(companyId);
    if (!users || users.length === 0) {
      return {
        reportType: "Nextgen Talent",
        status: "error",
        message: "No users found for this company",
        data: null,
      };
    }

    // Calculate statistics for each user
    const userStats = [];

    for (const user of users) {
      const userId = user._id;
      // Usecase usage statistics
      const usecaseFilter = { userId: userId };
      const docs = await AppTrackingModel.find(usecaseFilter).sort({
        year: -1,
        month: -1,
        day: -1,
        appId: 1,
      });
      const totalUsage = docs.reduce((acc, doc) => acc + doc.count, 0);
      const totalUniqueUsecase = await AppTrackingModel.distinct(
        "appId",
        usecaseFilter
      );

      // Simple App usage statistics
      const simpleAppFilter = { userId: userId };
      const simpleApps = await SimpleAppModel.find(simpleAppFilter);
      const totalSimpleApp = simpleApps.length;

      // Workflow usage statistics
      const workflowFilter = { userId: userId };
      const workflows = await WorkflowModel.find(workflowFilter);
      const totalWorkflow = workflows.length;

      // Journey usage statistics
      const journey = await JourneyTrackingModel.findById(userId);
      let totalJourneyItems = 0;
      let completedJourneys = 0;
      if (journey) {
        if (journey.beginner && Array.isArray(journey.beginner)) {
          totalJourneyItems += journey.beginner.length;
        } else if (journey.beginner && typeof journey.beginner === "object") {
          totalJourneyItems += Object.keys(journey.beginner).length;
        }
        if (journey.expert && Array.isArray(journey.expert)) {
          totalJourneyItems += journey.expert.length;
          if (journey.expert.length > 0) {
            completedJourneys = 1;
          }
        } else if (journey.expert && typeof journey.expert === "object") {
          totalJourneyItems += Object.keys(journey.expert).length;
        }

        if (journey.master && Array.isArray(journey.master)) {
          totalJourneyItems += journey.master.length;
          if (journey.master.length > 0) {
            completedJourneys = 2;
          }
        } else if (journey.master && typeof journey.master === "object") {
          totalJourneyItems += Object.keys(journey.master).length;
        }
      }

      if (user.journeyLevel?.name == "beginner") {
        completedJourneys = 0;
      }
      if (user?.journeyLevel?.name == "expert") {
        completedJourneys = 1;
      }
      if (user?.journeyLevel?.name == "master") {
        completedJourneys = 2;
      }
      // Check if user has completed all journeys
      if (user?.journeyLevel?.allJourneyCompleted === true) {
        completedJourneys = 3;
      }
      // Certificate statistics
      let totalCertificates = await Certificate.countDocuments({
        userId: userId,
      });

      // Idea statistics
      let totalIdeas = 0;
      const getSubmittedIdeas = await FormResponseModel.find({
        submittedBy: userId,
        formType: "ideation",
      });
      totalIdeas = getSubmittedIdeas.length;

      // Completed courses statistics
      let getUserCompletedCourses = await CourseTrackingModel.countDocuments({
        user: userId,
        courseComplateStatus: "completed",
      });

      // Add user statistics
      userStats.push({
        userId: userId,
        name: user.name,
        surname: user.surname,
        email: user.email,
        totalUsecaseUsage: totalUsage,
        totalUniqueUsecase: totalUniqueUsecase.length,
        totalSimpleApp: totalSimpleApp,
        totalWorkflow: totalWorkflow,
        totalAISolutions: totalSimpleApp + totalWorkflow,
        totalJourneySteps: totalJourneyItems,
        totalJourneyCompleted: completedJourneys,
        totalCertificates: totalCertificates,
        totalIdeas: totalIdeas,
        getUserCompletedCourses: getUserCompletedCourses,
        totalAIScore:
          totalUsage +
          totalUniqueUsecase.length * 2 +
          (totalSimpleApp + totalWorkflow) * 15 +
          completedJourneys * 10 +
          totalCertificates * 5 +
          totalIdeas * 10,
      });
    }

    // Sort by totalAIScore (descending)
    userStats.sort((a, b) => b.totalAIScore - a.totalAIScore);
    // add inside report model
    const report = await ReportModel.create({
      company: companyId,
      companyName: company.companyName,
      reportType: "nextgen",
      downloadLink: "not available",
      reportData: userStats,
      createdAt: new Date(),
    });
    return {
      company: companyId,
      companyName: company.companyName,
      reportType: "Nextgen Talent",
      status: "success",
      downloadLink: "not available",
      message: "User Stats",
      data: userStats,
    };
  } catch (err) {
    console.error("Error fetching user stats:", err);
    return {
      reportType: "Nextgen Talent",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
