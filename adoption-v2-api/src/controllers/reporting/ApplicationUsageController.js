const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;
const AppTrackingModel = require("../../models/AppTrackingModel");
const SimpleAppModel = require("../../models/SimpleAppModel");
const WorkflowModel = require("../../models/WorkflowModel");
const UserModel = require("../../models/UserModel");
const CompanyModel = require("../../models/CompanyModel");
const mongoose = require("mongoose");
const ReportModel = require("../../models/ReportModel");

exports.generateApplicationUsageReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "application",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Find all users in the company
    const users = await UserModel.find({
      company: new mongoose.Types.ObjectId(companyId),
    });
    const company = await CompanyModel.findById(companyId);
    if (!users || users.length === 0) {
      return {
        reportType: "application",
        status: "error",
        message: "No users found for this company",
        data: null,
      };
    }

    // Calculate statistics for each user
    const userStats = [];
    let totalSimpleApp = 0;
    let totalWorkflow = 0;
    let totalActiveUser = 0;
    let totalUsecaseUsage = 0;
    let totalUniqueUsecase = 0;
    for (const user of users) {
      const userId = user._id;
      if (user.onboarding) {
        totalActiveUser++;
      }
      // Usecase usage statistics
      const usecaseFilter = { userId: userId };
      const docs = await AppTrackingModel.find(usecaseFilter).sort({
        year: -1,
        month: -1,
        day: -1,
        appId: 1,
      });
      const totalUsage = docs.reduce((acc, doc) => acc + doc.count, 0);
      totalUsecaseUsage += totalUsage;
      const uniqueUsecases = await AppTrackingModel.distinct(
        "appId",
        usecaseFilter
      );
      console.log(uniqueUsecases);
      totalUniqueUsecase += uniqueUsecases.length;

      // Simple App usage statistics
      const simpleAppFilter = { userId: userId };
      const simpleApps = await SimpleAppModel.find(simpleAppFilter);
      const totalUserSimpleApp = simpleApps.length;
      totalSimpleApp += totalUserSimpleApp;
      // Workflow kullanım istatistikleri
      const workflowFilter = { userId: userId };
      const workflows = await WorkflowModel.find(workflowFilter);
      const totalUserWorkflow = workflows.length;
      totalWorkflow += totalUserWorkflow;
    }
    const reportStats = {
      totalActiveUser: totalActiveUser,
      totalUsecaseUsage: totalUsecaseUsage,
      totalUniqueUsecase: totalUniqueUsecase,
      averageUsecaseUsage: (totalUsecaseUsage / totalActiveUser).toFixed(2),
      averageUniqueUsecaseUsage: (totalUniqueUsecase / totalActiveUser).toFixed(
        2
      ),
    };
    // add inside report model
    const report = await ReportModel.create({
      company: companyId,
      companyName: company.companyName,
      reportType: "application",
      reportData: reportStats,
      createdAt: new Date(),
    });
    return {
      company: companyId,
      companyName: company.companyName,
      reportType: "application",
      status: "success",
      downloadLink: "not available",
      message: "User Stats",
      data: reportStats,
    };
  } catch (err) {
    console.error("Error fetching user stats:", err);
    return {
      reportType: "application",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
