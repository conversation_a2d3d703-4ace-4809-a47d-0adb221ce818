const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;

exports.generateMSTrainingReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "MSTraining Report",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }
    return {
      reportType: "MSTraining Report",
      status: "success",
      message: "Report created successfull",
      data: null,
    };
  } catch (err) {
    return {
      reportType: "MSTraining Report",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
