const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;

exports.generateIdeationReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Ideation Report",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }
    return {
      reportType: "Ideation Report",
      status: "success",
      message: "Report created successfull",
      data: null,
    };
  } catch (err) {
    return {
      reportType: "Ideation Report",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
