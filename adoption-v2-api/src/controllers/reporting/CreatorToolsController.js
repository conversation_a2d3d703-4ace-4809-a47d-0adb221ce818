const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;
const { httpResponse } = require("../../utils/helpers");
const ReportModel = require("../../models/ReportModel");
exports.generateCreatorToolsReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Creator Tools Report",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }
    return {
      reportType: "Creator Tools Report",
      status: "success",
      message: "Report created successfull",
      data: null,
    };
  } catch (err) {
    return {
      reportType: "Creator Tools Report",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
