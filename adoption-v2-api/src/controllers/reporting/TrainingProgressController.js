const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;
const UserModel = require("../../models/UserModel");
const Certificate = require("../../models/CertificateModel");
const CourseTrackingModel = require("../../models/CourseTrackingModel");
const CompanyModel = require("../../models/CompanyModel");
const mongoose = require("mongoose");
const ReportModel = require("../../models/ReportModel");
exports.generateTrainingProgressReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  /**
   * Temporary defined static course ids
   */
  const introCourseId = "67d0363ebbac7c0b9ed65da4";
  const riskCourseId = "67d03182c1297ce0b40614bc";

  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Training Progress Report",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Find all users in the company
    const users = await UserModel.find({
      company: new mongoose.Types.ObjectId(companyId),
    });
    const company = await CompanyModel.findById(companyId);

    if (!users || users.length === 0) {
      return {
        reportType: "Training Progress Report",
        status: "error",
        message: "No users found for this company",
        data: null,
      };
    }

    // Calculate statistics for each user
    const userStats = {};
    userStats["cohortSize"] = users.length;
    let loggedInUsers = 0;
    let introductionProgramCompleted = 0;
    let riskProgramCompleted = 0;
    let beginnerJourneyCompleted = 0;
    let expertJourneyJobSpecificCompleted = 0;
    let expertJourneyCompleted = 0;
    let masterJourneyCompleted = 0;
    for (const user of users) {
      const userId = user._id;
      if (user.onboarding) {
        loggedInUsers++;
      }
      if (user.journeyLevel && user.journeyLevel.name != "beginner") {
        beginnerJourneyCompleted++;
      }
      if (user.journeyLevel && user.journeyLevel.name == "master") {
        expertJourneyJobSpecificCompleted++;
        expertJourneyCompleted++;
      }
      if (user.journeyLevel && user.journeyLevel.allJourneyCompleted == true) {
        masterJourneyCompleted++;
      }
      // Journey usage statistics
      //const journey = await JourneyTrackingModel.findById(userId);
      ///let totalJourneyItems = 0;
      // Certificate statistics
      let introCourseCertificates = await Certificate.countDocuments({
        userId: userId,
        courseId: introCourseId,
      });
      let riskCourseCertificates = await Certificate.countDocuments({
        userId: userId,
        courseId: riskCourseId,
      });
      if (introCourseCertificates > 0) {
        introductionProgramCompleted++;
      }
      if (riskCourseCertificates > 0) {
        riskProgramCompleted++;
      }

      // Tamamlanan kurs istatistikleri
      let getUserCompletedCourses = await CourseTrackingModel.countDocuments({
        user: userId,
        courseComplateStatus: "completed",
      });
    }
    userStats["loggedInUsers"] = loggedInUsers;
    userStats["introductionProgramCompleted"] = introductionProgramCompleted;
    userStats["riskProgramCompleted"] = riskProgramCompleted;
    userStats["beginnerJourneyCompleted"] = beginnerJourneyCompleted;
    userStats["expertJourneyJobSpecificCompleted"] =
      expertJourneyJobSpecificCompleted;
    userStats["expertJourneyCompleted"] = expertJourneyCompleted;
    userStats["masterJourneyCompleted"] = masterJourneyCompleted;

    // add inside report model
    const report = await ReportModel.create({
      companyName: company.companyName,
      company: companyId,
      reportType: "training",
      downloadLink: "not available",
      reportData: userStats,
      createdAt: new Date(),
    });
    return {
      companyName: company.companyName,
      company: companyId,
      reportType: "training",
      status: "success",
      downloadLink: "not available",
      message: "Success",
      data: userStats,
    };
  } catch (err) {
    console.error("Error fetching user stats:", err);
    return {
      reportType: "Training Progress Report",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
