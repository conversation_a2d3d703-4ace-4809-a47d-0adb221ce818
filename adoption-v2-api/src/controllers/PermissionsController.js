const PermissionsModel = require("../models/PermissionsModel");
const RolesModel = require("../models/RolesModel");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");
const { ObjectId } = mongoose.Types;

const { httpResponse } = require("../utils/helpers");

exports.addPermission = async (req, res, next) => {
  const { company, role, perms } = req.body;
  try {
    const checkRole = await RolesModel.findOne({ role });
    if (checkRole !== null) {
      const createPermission = await PermissionsModel.create({
        company,
        role,
        perms,
      });
      return httpResponse(
        res,
        200,
        "success",
        "Permissions added",
        createPermission
      );
    } else {
      return httpResponse(res, 400, "error", "Role already exist");
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.getPermissions = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];
  const { role, company } = req.body;

  try {
    // company bir nesne olarak gelebilir veya doğrudan string olabilir
    const companyId = company.$oid ? company.$oid : company;

    const checkPermissions = await PermissionsModel.findOne({
      role,
      company: new ObjectId(companyId),
    });

    if (!token) return httpResponse(res, 401, "error", "No token provided.'");
    jwt.verify(token, process.env.JWT_SECRET, function (err, decoded) {
      if (err)
        return httpResponse(
          res,
          401,
          "error",
          "Unauthorized Access",
          "Unauthorized Access"
        );
      if (checkPermissions === null) {
        throw new Error("Permissions not found on this company");
      } else {
        return httpResponse(
          res,
          200,
          "success",
          `${company} roles`,
          checkPermissions
        );
      }
    });
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};

exports.addRole = async (req, res, next) => {
  const { role } = req.body;
  var token = req.headers.authorization?.split(" ")[1];

  try {
    const checkRole = await RolesModel.findOne({ role });
    if (checkRole === null) {
      const createRole = await RolesModel.create({
        role,
      });
      if (!token) return httpResponse(res, 401, "error", "No token provided.'");
      jwt.verify(token, process.env.JWT_SECRET, function (err, decoded) {
        if (err)
          return httpResponse(
            res,
            401,
            "error",
            "Unauthorized Access",
            "Unauthorized Access"
          );

        return httpResponse(res, 200, "success", "Role added", createRole);
      });
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.getRoles = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];

  try {
    const checkRole = await RolesModel.find();
    if (!token) return httpResponse(res, 401, "error", "No token provided.'");
    jwt.verify(token, process.env.JWT_SECRET, function (err, decoded) {
      if (err)
        return httpResponse(
          res,
          401,
          "error",
          "Unauthorized Access",
          "Unauthorized Access"
        );

      return httpResponse(res, 200, "success", "All roles", checkRole);
    });
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.updatePermission = async (req, res, next) => {
  const { company, role, perms } = req.body;
  const token = req.headers.authorization?.split(" ")[1];

  try {
    if (!token) return httpResponse(res, 401, "error", "No token provided.");

    jwt.verify(token, process.env.JWT_SECRET, async function (err, decoded) {
      if (err)
        return httpResponse(
          res,
          401,
          "error",
          "Unauthorized Access",
          "Unauthorized Access"
        );

      const checkPermissions = await PermissionsModel.findOne({
        role,
        company: new ObjectId(company),
      });

      if (!checkPermissions) {
        return httpResponse(
          res,
          404,
          "error",
          "Permissions not found for the specified role"
        );
      }

      if (
        perms.ideation?.Analytics === false &&
        perms.ideation?.Ideas === false
      ) {
        perms.nav.ideation = false;
      }

      const allReportingPermissionsFalse = Object.values(
        perms.reporting || {}
      ).every((value) => value === false);

      if (allReportingPermissionsFalse) {
        perms.nav.reporting = false;
      }

      checkPermissions.perms = perms;

      const updatedPermissions = await checkPermissions.save();

      return httpResponse(
        res,
        200,
        "success",
        "Permissions updated",
        updatedPermissions
      );
    });
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
