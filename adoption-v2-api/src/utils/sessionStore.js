// Session Store - For storing active user sessions
const sessions = new Map(); // userId -> { token, loginTime, lastActivity, email }

// Session timeout configuration (in minutes)
const SESSION_TIMEOUT_MINUTES = 1440; // 24 hours

// Create new session
const createSession = (userId, token, email) => {
  const sessionData = {
    token,
    email,
    loginTime: new Date(),
    lastActivity: new Date(),
    isActive: true,
  };

  sessions.set(userId.toString(), sessionData);
  return sessionData;
};

// Update session (last activity time)
const updateSession = (userId) => {
  const session = sessions.get(userId.toString());
  if (session && session.isActive) {
    session.lastActivity = new Date();
    return true;
  }
  return false;
};

// Check session
const getSession = (userId) => {
  return sessions.get(userId.toString());
};

// End session (logout)
const destroySession = (userId) => {
  const session = sessions.get(userId.toString());

  if (session) {
    session.isActive = false;
    sessions.delete(userId.toString());

    return true;
  }
  return false;
};

// Check if user has active session
const isSessionActive = (userId) => {
  const session = sessions.get(userId.toString());

  if (!session || !session.isActive) {
    return false;
  }

  // Check if session has timed out
  const now = new Date();
  const timeout = SESSION_TIMEOUT_MINUTES * 60 * 1000; // Convert to milliseconds

  if (now - session.lastActivity > timeout) {
    // Session has timed out, destroy it
    destroySession(userId);
    return false;
  }

  return true;
};

// List all active sessions (for admin)
const getAllActiveSessions = () => {
  const activeSessions = [];
  sessions.forEach((session, userId) => {
    if (session.isActive) {
      const details = getSessionDetails(session);

      // Check if session has expired
      if (details.isExpired) {
        destroySession(userId);
        return; // Skip this session
      }

      activeSessions.push({
        userId,
        email: session.email,
        loginTime: session.loginTime,
        lastActivity: session.lastActivity,
        totalSessionDuration: {
          minutes: details.totalSessionDurationMinutes,
          hours: details.totalSessionDurationHours,
          readable: `${details.totalSessionDurationHours} hours ${
            details.totalSessionDurationMinutes % 60
          } minutes`,
        },
        inactivityDuration: {
          minutes: details.timeSinceLastActivityMinutes,
          seconds: details.timeSinceLastActivitySeconds,
          readable:
            details.timeSinceLastActivitySeconds < 60
              ? `${details.timeSinceLastActivitySeconds} seconds`
              : `${details.timeSinceLastActivityMinutes} minutes`,
        },
        remainingTime: {
          minutes: details.remainingTimeMinutes,
          hours: details.remainingTimeHours,
          readable: `${details.remainingTimeHours} hours ${
            details.remainingTimeMinutes % 60
          } minutes`,
        },
        sessionStatus: {
          isActive: true,
          activityStatus: details.activityStatus,
          isRecentlyActive: details.isRecentlyActive,
          timeoutInMinutes: SESSION_TIMEOUT_MINUTES,
          timeoutInHours: SESSION_TIMEOUT_MINUTES / 60,
          willExpireAt: new Date(
            session.lastActivity.getTime() + SESSION_TIMEOUT_MINUTES * 60 * 1000
          ),
        },
      });
    }
  });
  return activeSessions;
};

// Clean up sessions that have been inactive for a certain period
const cleanupInactiveSessions = (timeoutMinutes = SESSION_TIMEOUT_MINUTES) => {
  const now = new Date();
  const timeout = timeoutMinutes * 60 * 1000; // in milliseconds

  sessions.forEach((session, userId) => {
    if (session.isActive && now - session.lastActivity > timeout) {
      destroySession(userId);
    }
  });
};

// Find user's session by email
const getSessionByEmail = (email) => {
  for (const [userId, session] of sessions) {
    if (session.email === email && session.isActive) {
      return { userId, ...session };
    }
  }
  return null;
};

// Helper function to calculate session duration and remaining time
const getSessionDetails = (session) => {
  if (!session) return null;

  const now = new Date();
  const totalSessionDuration = now - session.loginTime; // Total time since login
  const timeSinceLastActivity = now - session.lastActivity; // Time since last activity
  const timeoutMs = SESSION_TIMEOUT_MINUTES * 60 * 1000;
  const remainingTime = timeoutMs - timeSinceLastActivity; // Time left until expiration

  return {
    // Total session duration since login
    totalSessionDurationMs: totalSessionDuration,
    totalSessionDurationMinutes: Math.floor(totalSessionDuration / (1000 * 60)),
    totalSessionDurationHours: Math.floor(
      totalSessionDuration / (1000 * 60 * 60)
    ),

    // Time since last activity (this increases when user is inactive)
    timeSinceLastActivityMs: timeSinceLastActivity,
    timeSinceLastActivityMinutes: Math.floor(
      timeSinceLastActivity / (1000 * 60)
    ),
    timeSinceLastActivitySeconds: Math.floor(timeSinceLastActivity / 1000),

    // Time remaining until session expires (this decreases when user is inactive)
    remainingTimeMs: Math.max(0, remainingTime),
    remainingTimeMinutes: Math.max(0, Math.floor(remainingTime / (1000 * 60))),
    remainingTimeHours: Math.max(
      0,
      Math.floor(remainingTime / (1000 * 60 * 60))
    ),

    // Status information
    isExpired: remainingTime <= 0,
    isRecentlyActive: timeSinceLastActivity < 60000, // Active within last minute
    activityStatus: timeSinceLastActivity < 60000 ? "active" : "idle",
  };
};

// Get session with detailed information
const getSessionWithDetails = (userId) => {
  const session = sessions.get(userId.toString());
  if (!session || !session.isActive) {
    return null;
  }

  const details = getSessionDetails(session);
  if (details.isExpired) {
    destroySession(userId);
    return null;
  }

  return {
    ...session,
    details,
  };
};

// Export sessions for debug purposes
const getSessionsMap = () => sessions;

// Clean up inactive sessions every 12 hours
setInterval(() => {
  cleanupInactiveSessions(SESSION_TIMEOUT_MINUTES);
}, 12 * 60 * 60 * 1000); // 12 hours in milliseconds

// Functions to be exported
module.exports = {
  createSession,
  updateSession,
  getSession,
  getSessionWithDetails,
  destroySession,
  isSessionActive,
  getAllActiveSessions,
  cleanupInactiveSessions,
  getSessionByEmail,
  getSessionDetails,
  sessions: getSessionsMap, // for debug
};
