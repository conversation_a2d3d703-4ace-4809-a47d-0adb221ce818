<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { 
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
      line-height: 1.6; 
      color: #333; 
      margin: 0; 
      padding: 0;
      background-color: #f9f9f9;
    }
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }
    .email-header {
      background-color: #f5f5f5;
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #eeeeee;
    }
    .email-logo {
      max-width: 180px;
      margin-bottom: 10px;
    }
    .email-body {
      padding: 30px;
      color: #444444;
    }
    .email-greeting {
      font-size: 22px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 20px;
    }
    .email-message {
      margin-bottom: 25px;
    }
    .support-alert {
      display: inline-block;
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 6px;
      padding: 12px 24px;
      font-size: 18px;
      font-weight: bold;
      color: #856404;
      margin: 15px 0;
      width: 100%;
      box-sizing: border-box;
    }
    .user-info {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }
    .user-info h3 {
      margin: 0 0 10px 0;
      color: #007bff;
      font-size: 16px;
    }
    .user-info p {
      margin: 5px 0;
      font-size: 14px;
    }
    .form-responses {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 20px;
      margin: 20px 0;
    }
    .form-responses h3 {
      margin: 0 0 15px 0;
      color: #495057;
      font-size: 16px;
      border-bottom: 2px solid #dee2e6;
      padding-bottom: 10px;
    }
    .response-item {
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #e9ecef;
    }
    .response-item:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
    .response-label {
      font-weight: 600;
      color: #495057;
      margin-bottom: 5px;
      font-size: 14px;
    }
    .response-value {
      color: #6c757d;
      font-size: 14px;
      background-color: #ffffff;
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #dee2e6;
    }
    .form-meta {
      background-color: #e7f3ff;
      border-left: 4px solid #007bff;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }
    .form-meta h3 {
      margin: 0 0 10px 0;
      color: #007bff;
      font-size: 16px;
    }
    .form-meta p {
      margin: 5px 0;
      font-size: 14px;
    }
    .email-footer {
      background-color: #f5f5f5;
      padding: 20px;
      text-align: center;
      color: #777777;
      font-size: 14px;
      border-top: 1px solid #eeeeee;
    }
    .timestamp {
      color: #6c757d;
      font-size: 12px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <img src="https://aitrainer.aibusinessschool.com/resources/uploads/2024/01/saas-88a1c009-0c9a-4d94-a2f3-fa017cba07d6-aibs-logo-1.png" alt="AI Business School" class="email-logo">
    </div>
    
    <div class="email-body">
      <div class="email-greeting">
        New Support Request Received
      </div>
      
      <div class="email-message">
        <div class="support-alert">🔔 A new support request has been submitted and requires your attention.</div>
        
        <div class="user-info">
          <h3>👤 User Information</h3>
          <p><strong>Name:</strong> {{userName}}</p>
          <p><strong>Email:</strong> {{userEmail}}</p>
          <p><strong>Submitted:</strong> <span class="timestamp">{{timestamp}}</span></p>
        </div>

        <div class="form-meta">
          <h3>📋 Form Details</h3>
          <p><strong>Form Type:</strong> {{formType}}</p>
          <p><strong>Title:</strong> {{formTitle}}</p>
          <p><strong>Description:</strong> {{formDescription}}</p>
        </div>

        <div class="form-responses">
          <h3>📝 User Responses</h3>
          {{formResponses}}
        </div>
        
        <p><strong>Next Steps:</strong></p>
        <ul>
          <li>Review the support request details above</li>
          <li>Respond to the user within 24-48 hours</li>
          <li>Contact the user directly at <a href="mailto:{{userEmail}}">{{userEmail}}</a></li>
        </ul>
      </div>
    </div>
    
    <div class="email-footer">
      <p>&copy; 2025 AI Business School. All rights reserved.</p>
      <p>This is an automated notification from the AI Adoption Platform.</p>
    </div>
  </div>
</body>
</html>
