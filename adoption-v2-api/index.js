require("dotenv").config();
const express = require("express");
const http = require("http");
const cors = require("cors");
const mongoose = require("mongoose");
const config = require("./config");
const cron = require("node-cron");
const {
  resetExpiredCounters,
} = require("./src/controllers/APIRequestLimitController");

const app = express();
const userRouter = require("./src/routes/userRouter");
const authRouter = require("./src/routes/authRouter");
const languageRouter = require("./src/routes/languageRouter");
const permissionsRouter = require("./src/routes/permissionsRouter");
const aitrainerRouter = require("./src/routes/AiTrainerRouter");
const companyRouter = require("./src/routes/companyRouter");
const ideationRouter = require("./src/routes/ideationRouter");
const toolkitRouter = require("./src/routes/toolkitRouter");
const trackingRouter = require("./src/routes/trackingRouter");
const workflowRouter = require("./src/routes/workflowRouter");
const simpleAppRouter = require("./src/routes/simpleAppRouter");
const appTrackingRouter = require("./src/routes/appTrackingRouter");
const connectorsRouter = require("./src/routes/connectorsRouter");
const platformSettingsRouter = require("./src/routes/platformSettingsRouter");
const shortcutsRouter = require("./src/routes/shortcutsRouter");
const courseTrackingRouter = require("./src/routes/courseTrackingRouter");
const ssoServerRouter = require("./src/routes/ssoServerRouter");
const formResponseRouter = require("./src/routes/formResponseRouter");
const quizProgressRouter = require("./src/routes/quizProgressRoutes");
const certificateRouter = require("./src/routes/certificateRouter");
const cockpitRouter = require("./src/routes/cockpitRouter");
const reportingRouter = require("./src/routes/ReportingRouter");
const mediaRouter = require("./src/routes/mediaRouter");
const apiRequestLimitRouter = require("./src/routes/apiRequestLimitRouter");
const emailTemplateRouter = require("./src/routes/emailTemplateRouter");
const reportFilesRouter = require("./src/routes/reportFilesRouter");

// Email template controller
const {
  initializeScheduledJobs,
  migrateExistingTemplates,
  createDefaultScheduledJobs,
} = require("./src/controllers/EmailTemplateController");

// MIDDLEWARES
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb" }));

// API Key Middleware - Define globally for all requests
const apiKeyMiddleware = require("./src/middleware/apiKeyMiddleware");
app.use(apiKeyMiddleware);

app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:3002",
      "https://adoptionv2dev.aibusinessschool.com",
      "https://adoptionv2.aibusinessschool.com",
      "https://adoptionv2preprod.aibusinessschool.com",
      "https://managementdev.aibusinessschool.com",
      "https://management.aibusinessschool.com",
      "https://admindev2.aibusinessschool.com",
      "https://admin2.aibusinessschool.com",
      "https://ai.aibusinessschool.com",
      "https://coursera.org",
      "http://127.0.0.1:4173",
      "https://d10o6em2qtnr4q.cloudfront.net",
      "https://*.cloudfront.net",
    ],
    credentials: true,
  })
);

app.use("/api/users", userRouter);
app.use("/api/tracking", trackingRouter);
app.use("/api/auth", authRouter);
app.use("/api/lang", languageRouter);
app.use("/api/permissions", permissionsRouter);
app.use("/api/aitrainer", aitrainerRouter);
app.use("/api/company", companyRouter);
app.use("/api/ideation", ideationRouter);
app.use("/api/toolkit", toolkitRouter);
app.use("/api/tracking", trackingRouter);
app.use("/api/workflow-creator", workflowRouter);
app.use("/api/app-creator", simpleAppRouter);
app.use("/api/tracking/app", appTrackingRouter);
app.use("/api/connectors", connectorsRouter);
app.use("/api/settings", platformSettingsRouter);
app.use("/api/shortcuts", shortcutsRouter);
app.use("/api/tracking/course", courseTrackingRouter);
app.use("/api/sso-server", ssoServerRouter);
app.use("/api/form-responses", formResponseRouter);
app.use("/api/quiz-progress", quizProgressRouter);
app.use("/api/media", mediaRouter);
app.use("/api/certificates", certificateRouter);
app.use("/api/reporting", reportingRouter);
app.use("/api/cockpit", cockpitRouter);
app.use("/api/api-limits", apiRequestLimitRouter);
app.use("/api/email-templates", emailTemplateRouter);
app.use("/api/report-files", reportFilesRouter);

// ENV VARIABLES
const { MONGODB_URI, PORT, HTTP_PORT } = config;

app.get("/api", (req, res, next) => {
  res.status(403).json({ message: "Unauthorized access" });
});

const httpServer = http.createServer(app);

mongoose.connect(MONGODB_URI).catch((error) => {
  console.log("error:", error);
});

// Cron job to reset API request limits
// Runs every day at midnight (00:00)
cron.schedule("0 0 * * *", async () => {
  try {
    const result = await resetExpiredCounters();
  } catch (error) {
    console.error("Error resetting API request limits:", error);
  }
});

app.listen(PORT, () => {
  console.log(`Server started on Port : ${PORT}...`);
});
httpServer.listen(HTTP_PORT, () => {
  console.log(`HTTP Server running on port ${HTTP_PORT}`);
});
