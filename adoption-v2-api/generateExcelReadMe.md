# Excel File Generation API

This API is used to generate Excel files from training reports. Files are stored in Azure Blob Storage and processed asynchronously using a queue system.

## Authentication

All endpoints require JWT token authentication. Send the token in the Authorization header as a Bearer token:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints

### 1. Excel File Generation Request

**POST** `/api/report-files/generate`

Initiates an Excel file generation request for a report.

**Request Body:**

```json
{
  "reportId": "507f1f77bcf86cd799439011"
}
```

**Response (201):**

```json
{
  "success": true,
  "message": "Excel generation request created successfully",
  "data": {
    "id": "507f1f77bcf86cd799439012",
    "reportId": "507f1f77bcf86cd799439011",
    "status": "pending",
    "requestedBy": "507f1f77bcf86cd799439013",
    "createdAt": "2025-01-06T10:30:00.000Z"
  }
}
```

**Error Cases:**

- `400` - Invalid reportId format
- `401` - Authentication required
- `404` - Report not found
- `500` - Server error

---

### 2. Excel File Status Check

**GET** `/api/report-files/status/:id`

Checks the generation status of the Excel file.

**Response (200) - Pending:**

```json
{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439012",
    "reportId": "507f1f77bcf86cd799439011",
    "status": "pending",
    "requestedBy": "507f1f77bcf86cd799439013",
    "createdAt": "2025-01-06T10:30:00.000Z"
  }
}
```

**Response (200) - Processing:**

```json
{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439012",
    "reportId": "507f1f77bcf86cd799439011",
    "status": "processing",
    "requestedBy": "507f1f77bcf86cd799439013",
    "createdAt": "2025-01-06T10:30:00.000Z"
  }
}
```

**Response (200) - Ready:**

```json
{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439012",
    "reportId": "507f1f77bcf86cd799439011",
    "status": "ready",
    "fileUrl": "https://yourstorageaccount.blob.core.windows.net/excel-files/training-report-2025-01-06.xlsx",
    "fileName": "training-report-2025-01-06.xlsx",
    "requestedBy": "507f1f77bcf86cd799439013",
    "createdAt": "2025-01-06T10:30:00.000Z",
    "completedAt": "2025-01-06T10:32:15.000Z"
  }
}
```

**Response (200) - Failed:**

```json
{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439012",
    "reportId": "507f1f77bcf86cd799439011",
    "status": "failed",
    "error": "Excel generation failed: Invalid data format",
    "requestedBy": "507f1f77bcf86cd799439013",
    "createdAt": "2025-01-06T10:30:00.000Z",
    "completedAt": "2025-01-06T10:32:15.000Z"
  }
}
```

**Error Cases:**

- `401` - Authentication required
- `403` - Access denied to this file
- `404` - File not found
- `500` - Server error

---

### 3. List Excel Files

**GET** `/api/report-files/`

Lists the user's Excel file requests.

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Records per page (default: 10)

**Example:** `/api/report-files/?page=2&limit=5`

**Response (200):**

```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": "507f1f77bcf86cd799439012",
        "reportId": {
          "_id": "507f1f77bcf86cd799439011",
          "title": "Q1 2025 Training Report"
        },
        "status": "ready",
        "fileUrl": "https://yourstorageaccount.blob.core.windows.net/excel-files/training-report-2025-01-06.xlsx",
        "fileName": "training-report-2025-01-06.xlsx",
        "requestedBy": {
          "_id": "507f1f77bcf86cd799439013",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "createdAt": "2025-01-06T10:30:00.000Z",
        "completedAt": "2025-01-06T10:32:15.000Z"
      },
      {
        "id": "507f1f77bcf86cd799439014",
        "reportId": {
          "_id": "507f1f77bcf86cd799439015",
          "title": "December 2024 Training Report"
        },
        "status": "failed",
        "error": "Excel generation failed: Invalid data format",
        "requestedBy": {
          "_id": "507f1f77bcf86cd799439013",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "createdAt": "2025-01-05T14:20:00.000Z",
        "completedAt": "2025-01-05T14:22:30.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalFiles": 25,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

**Error Cases:**

- `401` - Authentication required
- `500` - Server error

---

### 4. Download Excel File

**GET** `/api/report-files/download/:id`

Downloads the Excel file (redirects to Azure Blob Storage URL).

**Response (302):**
File is redirected to Azure Blob Storage URL.

**Error Cases:**

- `401` - Authentication required
- `403` - Access denied to this file
- `404` - File not found or not ready yet
- `500` - Server error

---

## Status Codes

- **pending**: Excel file generation request received, waiting in queue
- **processing**: Excel file is currently being generated
- **ready**: Excel file is ready and can be downloaded
- **failed**: Error occurred during Excel file generation

## Usage Example

```javascript
// 1. Excel file generation request
const response = await fetch("/api/report-files/generate", {
  method: "POST",
  headers: {
    Authorization: "Bearer YOUR_JWT_TOKEN",
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    reportId: "507f1f77bcf86cd799439011",
  }),
});

const result = await response.json();
const fileRequestId = result.data.id;

// 2. Status checking (polling)
const checkStatus = async () => {
  const statusResponse = await fetch(
    `/api/report-files/status/${fileRequestId}`,
    {
      headers: {
        Authorization: "Bearer YOUR_JWT_TOKEN",
      },
    }
  );

  const statusResult = await statusResponse.json();

  if (statusResult.data.status === "ready") {
    // 3. Download the file
    window.open(`/api/report-files/download/${fileRequestId}`, "_blank");
  } else if (statusResult.data.status === "failed") {
    console.error("Excel generation failed:", statusResult.data.error);
  } else {
    // Still processing, check again
    setTimeout(checkStatus, 5000); // Check again after 5 seconds
  }
};

checkStatus();
```

## Excel File Format

The generated Excel file will be in the following format:

- **Header**: "Training progress dd-mm-yyyy"
- **Cohort size**: Total number of users
- **Logged in**: Number of logged-in users (always 100%)
- **Beginner Journey**: Beginner level training courses
- **Expert Journey**: Expert level training courses
- **Master Journey**: Master level training courses

Appropriate colors and percentage calculations are automatically added for each row.

## Error Messages

- `Report ID is required` - reportId parameter is missing
- `Invalid report ID format` - reportId is not a valid MongoDB ObjectId format
- `Report not found` - The specified report was not found
- `File not found` - Excel file not found
- `File not ready yet` - Excel file is not ready yet
- `Access denied` - You don't have access to this file
