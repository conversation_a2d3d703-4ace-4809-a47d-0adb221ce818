{"header": {"home": "Startseite", "training": "Training", "create": "<PERSON><PERSON><PERSON>", "innovate": "Innovation", "cockpit": "Cockpit", "aiUseCases": "AI-Anwendungsfälle", "apply": "<PERSON><PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON>", "signOut": "Abmelden", "accountSettings": "Kontoeinstellungen", "help": "<PERSON><PERSON><PERSON>", "logo": "AIBS Logo", "contactUs": "Kontaktiere uns", "supportFormNotFound": "Supportformular nicht gefunden. Bitte versuchen Sie es später erneut.", "submittingRequest": "Ihre Anfrage wird übermittelt..."}, "journey": {"limitedUser": {"beginnerCompleted": "Sie haben die Einsteiger-Trainingsreise erfolgreich abgeschlossen.", "accessWarning": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Fi<PERSON> „grünes Licht“ gibt, werden Sie für eine Fortführung der Trainingsreisen freigeschaltet werden. Wir bitte daher um noch etwas Geduld.", "lockTooltip": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Fi<PERSON> „grünes Licht“ gibt, werden Sie für eine Fortführung der Trainingsreisen freigeschaltet werden. Wir bitte daher um noch etwas Geduld."}}, "roleUpgradeModal": {"title": "Upgrade – Ihr Zugang wurde erweitert", "accountUpgraded": "Wir freuen uns sehr, <PERSON>hnen mitteilen zu dürfen, dass Ihre Firma Ihren Account aufgewertet und Ihren Zugang zur „AI Adoption Platform“ der AI Business School erweitert hat.", "extendedAccess": "Sie haben ab jetzt deutlich erweiterten Zugang zu allen relevanten Trainingsprogrammen, <PERSON><PERSON>, <PERSON><PERSON> und Anwendungen. Wir empfehlen Ihnen, direkt mit der berufsspezifischen, strukturierten Trainingsreise auf „Experten-Stufe“ weiterzumachen.", "understand": "<PERSON>ch verstehe"}, "home": {"welcome": "Willkommen {{name}}", "jobRole": "Ihre Abteilung: {{role}} - Ihre berufliche Rolle: {{jobRole}}", "jobRolePrefix": "Ihre berufliche Rolle oder Spezialisierung: ", "trainingJourneys": "Ihre berufsspezifischen Trainingsreisen", "selectLevel": "<PERSON>ählen Sie e<PERSON> Stufe", "skipJourney": "Gesamte Journey überspringen »", "skipJourneyModal": {"description": "Basierend auf den Antworten, die Sie während des Onboardings gegeben haben, verstehen wir, dass Sie bereits über fundierte Grundkenntnisse im Bereich AI verfügen. Wenn Si<PERSON> möchten, können Sie die Beginner Journey überspringen und direkt mit der Expert Journey fortfahren."}, "levels": {"beginner": "Einsteiger", "expert": "Experte", "master": "Master"}, "journeyCard": {"completed": "Abgeschlossen", "playNow": "Jetzt abspielen", "locked": "<PERSON><PERSON><PERSON><PERSON>"}, "dailyAI": {"title": "<PERSON><PERSON><PERSON><PERSON> von AI leicht gemacht", "subtitle": "Speziell für Sie ausgewählte AI Anwendungsfälle entdecken und anwenden"}, "aiPlaygrounds": {"title": "Beliebte AI Playgrounds", "subtitle": "Einige der aktuellsten AI Technologien besser kennenlernen"}, "techCompanies": {"title": "AI Ökosysteme führender Technologieunternehmen", "subtitle": "Entdecken Sie AI Tools, interaktive Trainingsprogramme und Playgrounds führender Technologieunternehmen"}, "watchIntroVideo": "Plattform-Video", "PlatformVideoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/AI_ADOPTION_Platform_DE_V02.mp4/AI_ADOPTION_Platform_DE_V02.m3u8", "introVideo": {"title": "Plattform-Video"}}, "common": {"lockedTooltip": "Dieser Teil ist nicht in Ihrem aktuellen Abonnement enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.", "loading": "<PERSON><PERSON><PERSON> wird geladen...", "error": "Ein Fehler ist aufgetreten", "uploading": "Hochladen", "chooseFiles": "<PERSON><PERSON> au<PERSON>wählen", "unknownError": "Unbekannter Fehler", "congratulations": "Glückwunsch!", "continue": "<PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON> versuchen", "locked": "Dieses Training ist gesperrt", "completed": "Training abgeschlossen", "inProgress": "In Bearbeitung", "notStarted": "Noch nicht begonnen", "complete": "Abgeschlossen", "startNow": "Jetzt starten", "pleaseLogin": "Bitte melden Sie sich an, um auf diese Seite zuzugreifen.", "addToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "myFavorite": "<PERSON><PERSON>", "addedToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hi<PERSON>", "removedFromFavorites": "Aus Favoriten entfernt", "goodJob": "Gut gemacht!", "points": "Punkte!", "back": "Zurück", "backToApply": "Zurück zur Anwendung", "backToToolkit": "Zurück zur Toolkits", "backToPlayground": "Zurück zur Playground", "errorLoading": "<PERSON><PERSON> der Use Cases ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.", "shortcutNotFound": "Favorit nicht gefunden", "fieldRequired": "<PERSON><PERSON> ist erford<PERSON>lich", "fieldCannotBeEmpty": "<PERSON><PERSON>ld darf nicht leer sein", "pleaseCompleteRequiredFields": "Bitte füllen Si<PERSON> alle erforderlichen Felder aus.", "signInWithMicrosoftAccount": "Mit <PERSON>-<PERSON><PERSON>", "validation": {"required": "<PERSON><PERSON> ist erford<PERSON>lich"}, "warnings": {"dataPrivacy": "Diese App verwendet AI. Prüfen Sie auf Fehler. Bitte geben Si<PERSON> während der Übungen keine vertraulichen Geschäftsdaten - einschließlich Kunden-, Produkt- oder Mitarbeiterinformationen - an die AI weiter, damit <PERSON> sicher bleiben."}, "errors": {"usageLimitExceeded": "Sie haben das Nutzungslimit überschritten, bitte wenden <PERSON><PERSON> sich an Ihren Administrator.", "textGenerationFailed": "Text konnte nicht generiert werden. Bitte versuchen Sie es erneut.", "imageGenerationFailed": "Bild konnte nicht generiert werden. Bitte versuchen Sie es erneut.", "videoGenerationFailed": "Video konnte nicht generiert werden. Bitte versuchen Sie es erneut."}, "errorOccurred": "Ein Fehler ist aufgetreten!", "thankYou": "Vielen Dank!", "submitting": "Wird gesendet...", "submit": "<PERSON><PERSON><PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "true": "<PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON><PERSON>", "copy": "In die Zwischenablage kopieren", "copied": "Kopiert!", "workflows": {"title": "Berufsspezifische AI Workflows entdecken ({{role}})", "discover": "Entdecken Sie unsere AI-gestützten Workflows", "showAll": "Alle Workflows anzeigen", "run": {"editApp": "App bearbeiten", "restartApp": "<PERSON><PERSON>n", "errorLoading": "Fehler beim Laden des Workflows", "next": "<PERSON><PERSON>"}}, "result": "<PERSON><PERSON><PERSON><PERSON>", "showLess": "<PERSON><PERSON> anzeigen", "showMore": "<PERSON><PERSON> anzeigen"}, "managementUseCases": {"title": "Management-spezifische AI Use Cases und Apps", "description": "Entdecken Sie sorgfältig ausgewählte, relevante Use Cases für Ihren Management-Alltag"}, "itUseCases": {"title": "IT-bezogene AI Use Cases und Apps", "description": "Entdecken Sie sorgfältig ausgewählte, relevante IT Use Cases"}, "footer": {"privacyPolicy": "Datenschutz"}, "privacyPolicy": {"title": "Datenschutzerklärung", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, wie wir Ihre per<PERSON>önlichen Daten sammeln, verwenden und schützen. Letzte Aktualisierung: 06 September 2024"}, "termsAndConditions": {"title": "Allgemeine Geschäftsbedingungen", "subtitle": "Bitte lesen Sie diese Allgemeinen Geschäftsbedingungen sorgfältig durch, bevor Sie unsere Dienste nutzen. Letzte Aktualisierung: 08 August 2023"}, "modal": {"next": "<PERSON><PERSON>", "previous": "Zurück", "complete": "Abschließen", "completed": "Abgeschlossen", "done": "<PERSON><PERSON><PERSON>", "close": "Schließen", "watchVideoFirst": "<PERSON><PERSON> schauen Sie zuerst das Video an.", "continueTraining": "Training fortsetzen", "completeTraining": "Training abschließen", "cancel": "Abbrechen", "confirm": "Bestätigen", "back": "Zurück", "submit": "Einreichen", "continue": "<PERSON><PERSON>", "review": "Überprüfen", "reviewYourSelections": "Überprüfen Sie Ihre Auswahl."}, "training": {"title": "Ihre berufsspezifischen Trainingsreisen", "recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, themenspezifische Trainings", "recommendedSubtitle": "<PERSON><PERSON><PERSON> <PERSON><PERSON> per<PERSON>önlich ausgewählte und empfohlene Trainings und Kurse", "currentLevel": {"prefix": "Ihre derzeitige Stufe:"}, "progress": {"title": "Trainingsfortschritt", "stepsCompleted": "{{completed}} von {{total}} <PERSON><PERSON><PERSON>n abgeschlossen", "currentStep": "Aktueller Schritt", "stepCompleted": "Sie haben diesen Schritt abgeschlossen", "allStepsCompleted": "Alle Schritte abgeschlossen"}, "topicSpecific": {"title": "Themenspezifische Trainingsprogramme", "subtitle": "Erweitern Sie Ihr Wissen mit spezialisierten AI-Trainingsprogrammen für gezieltes Lernen und Beherrschung."}, "technicalSpecialists": {"title": "Generative AI (GenAI) für Tech-Profis", "subtitle": "Empfohlene Trainings für Tech-Spezialisten"}, "technical": {"title": "Technische AI-Trainingsprogramme", "subtitle": "Erweitern Sie Ihr Wissen mit spezialisierten AI-Trainingsprogrammen für gezieltes Lernen und Beherrschung."}, "toolSpecific": {"title": "Tool-spezifische Trainings", "subtitle": "Entdecken Sie die AI Lösungen und Tools führender Technologieunternehmen mit spannenden Kursen und interaktiven AI Playgrounds"}}, "apply": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Entdecken Sie berufsspezifische AI Apps und Use Cases und wenden Sie sie an", "discoverMore": {"useCases": "Berufsspezifische AI Use Cases und Apps entdecken ({{role}})", "showAll": "<PERSON><PERSON> anzeigen", "specialSelection": "Persönliche Auswahl für Sie", "workflows": "Berufsspezifische AI Workflows entdecken ({{role}})"}, "management": {"title": "Management-spezifische AI Use Cases und Apps entdecken", "subtitle": "Persönliche Auswahl für Sie"}, "it": {"title": "IT-bezogene AI Use Cases und Apps entdecken", "subtitle": "Persönliche Auswahl für Sie"}, "aiPlaygrounds": {"title": "Beliebte AI Playgrounds", "subtitle": "Einige der aktuellsten AI Technologien besser kennenlernen"}, "favorites": {"title": "Meine Favoriten: Use Cases, Apps und Workflows", "subtitle": "Die ich al<PERSON> Favoriten markiert habe", "addToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "myFavorite": "<PERSON><PERSON>", "addedToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hi<PERSON>", "removedFromFavorites": "Aus Favoriten entfernt"}, "techCompanies": {"title": "AI Ökosysteme führender Technologieunternehmen", "subtitle": "Entdecken Sie AI Tools, interaktive Trainingsprogramme und Playgrounds führender Technologieunternehmen"}, "common": {"loading": "Inhalte werden geladen...", "error": "<PERSON><PERSON> der Inhalte ist ein Fehler aufgetreten.", "retry": "<PERSON><PERSON><PERSON> versuchen", "locked": "Diese Funktion ist gesperrt", "completed": "Abgeschlossen", "inProgress": "In Bearbeitung", "startNow": "Jetzt starten", "viewMore": "<PERSON><PERSON> anzeigen", "usedTimes": "{{count}} mal verwendet", "workflows": {"title": "Berufsspezifische AI Workflows entdecken ({{role}})", "discover": "Entdecken Sie unsere AI-gestützten Workflows", "showAll": "Alle Workflows anzeigen", "run": {"editApp": "App bearbeiten", "restartApp": "<PERSON><PERSON>n", "errorLoading": "Fehler beim Laden des Workflows"}}}, "workflows": {"title": "Berufsspezifische AI Workflows entdecken ({{role}})", "subtitle": "Persönliche Auswahl für Sie", "showAll": "Alle Workflows anzeigen"}}, "create": {"title": "Moderne Creator Tools", "subtitle": "Spielerisch einfach eigene AI Lösungen, Apps und Workflows kreieren", "simpleTools": {"title": "Einfache Creator Tools für alle", "subtitle": "<PERSON><PERSON><PERSON> starten, ohne jegliche weitere Voraussetzungen"}, "advancedTools": {"title": "Anspruchsvolle Creator Tools", "subtitle": "Anspruchsvollere AI Lösungen selber bauen – für Fortgeschrittene"}, "buttons": {"startNow": "Jetzt anfangen"}, "common": {"loading": "Kreation-Tools werden geladen...", "error": "<PERSON><PERSON> der Tools ist ein Fehler aufgetreten.", "retry": "<PERSON><PERSON><PERSON> versuchen", "locked": "<PERSON><PERSON> ist gesperrt", "completed": "Tool-Setup abgeschlossen", "inProgress": "In Bearbeitung", "startNow": "Jetzt starten", "viewMore": "<PERSON><PERSON> anzeigen", "usedTimes": "{{count}} mal verwendet"}, "workflowCreator": {"header": {"loadSample": "Beispiel-Workflow laden", "saveDraft": "Entwurf speichern", "saveAndRun": "Speichern & Ausführen", "workflowNamePlaceholder": "Wie lautet der Name deines Flows?", "loadSampleTooltip": "<PERSON><PERSON> den Beispiel-Workflow laden können, müssen Sie alle vorhandenen Komponenten entfernen"}, "tour": {"steps": {"appName": {"title": "Workflow-Name", "content": "Beginnen Si<PERSON> damit, Ihrem Workflow einen aussagekräftigen Namen zu geben. Dies hilf<PERSON>, ihn später zu identifizieren."}, "headerButtons": {"title": "Aktionsschaltflächen", "content": "Verwenden Sie diese Schaltflächen, um einen Beispiel-Workflow zu laden, Ihre Arbeit als Entwurf zu speichern oder Ihren fertigen Workflow zu speichern und auszuführen."}, "componentsArea": {"title": "Komponenten-Panel", "content": "Dieses Panel enthält alle Komponenten, die Sie zum Erstellen Ihres Workflows verwenden können. Ziehen Sie sie per Drag & Drop in den Workflow-Bereich."}, "dropArea": {"title": "Workflow-Bereich", "content": "<PERSON>iehen Sie Komponenten hierher, um Ihren Workflow zu erstellen. Sie können sie in der Reihenfolge anordnen, in der sie ausgeführt werden sollen."}}}, "components": {"title": "Komponenten", "textInput": "Texteingabe", "display": "Ergebnisanzeige", "prompt": "Prompt"}, "alerts": {"enterAppName": "<PERSON>te geben Si<PERSON> einen App-Namen ein.", "createWorkflow": "<PERSON>e müssen einen Workflow erstellen, bevor Sie ihn speichern und ausführen können.", "includeDisplay": "Sie müssen am Ende Ihres Workflows eine Anzeige-Komponente für die ordnungsgemäße Ausführung einfügen.", "fillInputs": "Bitte füllen Sie alle erforderlichen Eingabefelder aus.", "fillOutputNames": "Bitte füllen Sie alle erforderlichen Ausgabenamen für Prompts aus.", "includeVariable": "<PERSON><PERSON> müssen eine Variable im Prompt einfügen.", "maxDisplay": "Sie können bis zu 1 Anzeige-Komponente hinzufügen.", "removeExisting": "<PERSON><PERSON> den Beispiel-Workflow laden können, müssen Sie alle vorhandenen Komponenten entfernen", "noEditSample": "Beispiel-Workflow kann nicht im Bearbeitungsmodus geladen werden"}, "dialog": {"creating": "Ihre App wird erstellt...", "updating": "Ihre App wird aktualisiert...", "buildingMessage": "Wir erstellen Ihre App, bitte geben Si<PERSON> uns einige Sekunden", "updatingMessage": "Wir aktualisieren Ihre App, bitte geben Si<PERSON> uns einige Sekunden"}, "settings": {"title": "Parametereinstellungen", "temperature": "Temperature", "topP": "Top P", "presencePenalty": "Presence penalty", "frequencyPenalty": "Frequency penalty", "tooltips": {"temperature": "<PERSON><PERSON>pling-Temperatur verwendet werden soll, zwischen 0 und 2. <PERSON><PERSON>here Werte wie 0,8 machen die Ausgabe zuf<PERSON>ger, während niedrigere Werte wie 0,2 sie fokussierter und deterministischer machen. Wir empfehlen im Allgemeinen, entweder diese oder top_p zu ändern, aber nicht beide.", "topP": "Eine Alternative zum Sampling mit Temperature, genan<PERSON><PERSON><PERSON>, bei der das Modell die Ergebnisse der Token mit der top_p-Wahrscheinlichkeitsmasse berücksichtigt. So bedeutet 0,1, dass nur die Token, die die oberen 10% der Wahrscheinlichkeitsmasse ausmachen, berücksichtigt werden. Wir empfehlen im Allgemeinen, entweder diese oder Temperature zu ändern, aber nicht beide.", "frequencyPenalty": "Zahl zwischen 0 und 2,0. Werte bestrafen neue Token auf der Grundlage ihrer vorhandenen Häufigkeit im bisherigen Text und verringern die Wahrscheinlichkeit, dass das Modell dieselbe Zeile wörtlich wiederholt.", "presencePenalty": "Zahl zwischen 0 und 2,0. Werte bestrafen neue Token danach, ob sie im bisherigen Text vorkommen, und erhöhen die Wahrscheinlichkeit, dass das Modell über neue Themen spricht."}}, "componentsColumn": {"title": "Komponenten", "tooltips": {"textInput": "Fügen Sie Texteingabefelder hinzu, um Benutzerinformationen zu sammeln, die in Ihren Prompts verwendet werden können.", "display": "Zeigen Sie die endgültigen Ergebnisse Ihres Workflows an. Sie können Text, Variablen oder andere von Ihren Prompts generierte Inhalte anzeigen.", "prompt": "Verwenden Sie Variablen aus Texteingaben, um dynamische Inhalte zu generieren. Sie können mehrere Variablen in einem einzigen Prompt verwenden."}}, "dropArea": {"emptyState": "Ziehen Sie die Komponenten per Drag & Drop in diesen Bereich", "form": {"input": "Eingabe", "inputDetail": "Eingabedetail", "outputName": "Ausgabename", "prompt": "Prompt"}, "variables": {"promptTitle": "Prompt-Variablen:", "displayTitle": "Anzeige-Variablen:", "displaySubtitle": "Wählen Sie die Themenbereiche aus, die im Endergebnis angezeigt werden sollen."}}}}, "innovate": {"title": "Innovation", "subtitle": "Eigene Ideen und Lösungen teilen.", "buttons": {"shareIdeas": "<PERSON><PERSON><PERSON> teilen"}, "common": {"loading": "Innovations-Tools werden geladen...", "error": "<PERSON><PERSON> der Tools ist ein Fehler aufgetreten.", "retry": "<PERSON><PERSON><PERSON> versuchen", "locked": "Diese Funktion ist gesperrt", "completed": "Idee erfolgreich eingereicht", "inProgress": "In Bearbeitung", "startNow": "Jetzt starten", "viewMore": "<PERSON><PERSON> anzeigen", "usedTimes": "{{count}} mal verwendet"}, "form": {"title": "Teilen Sie Ihre Innovation", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> un<PERSON> von Ihrer innovativen Idee oder Lösung", "submit": "I<PERSON><PERSON> e<PERSON><PERSON>ichen", "cancel": "Abbrechen"}}, "tools": {"openai": {"title": "OpenAI Tools", "description": "<PERSON><PERSON><PERSON><PERSON>, wie Sie OpenAI Tools wirksam nutzen können."}, "microsoft": {"title": "AI Tools von Microsoft", "description": "Hier können Sie die gesamte Palette relevanter AI Tools von Microsoft erlernen und ausprobieren.", "trainingSteps": {"chatgptIntro": "Einführung in ChatGPT"}, "top10Copilot": {"title": "Top 10, die <PERSON><PERSON> zu<PERSON>t mit Copilot ausprobieren sollten", "description": "Entdecken Sie die wichtigsten Funktionen für den Einstieg in Microsoft Copilot."}, "promptingCheatSheet": {"title": "Cheatsheet für Prompting", "description": "Lernen Sie effektive Prompting-Techniken für Microsoft Copilot."}, "microsoft365ToolGuidelinesForCopilot": {"title": "Richtlinien für Microsoft 365-Tools mit Copilot", "description": "<PERSON>rste Schritte mit Microsoft 365 Copilot"}}, "githubCopilot": {"title": "GitHub Copilot", "description": "Begeben Sie sich auf eine spannende Reise mit unserer interaktiven Trainingsplattform, die das transformative Potenzial der AI-basierten Innovationen von GitHub Copilot veranschaulicht. Meistern Sie praktische Herausforderungen in verschiedenen Programmiersprachen, um Ihre Programmierkenntnisse zu verfeinern und Ihre Paar- / Tandem-Programmierkenntnisse zu verbessern.<ul><li>Mehrsprachige Unterstützung: JavaScript- und Python-Beispiele</li><li>AI-Modellvergleiche: Vergleichen Sie die Fähigkeiten verschiedener AI-Modelle, die in GitHub Copilot verfügbar sind</li><li>Praktische Herausforderungen: 8 Core Challenges und 5 AI-Modellvergleichs-Disziplinen</li><li>Sichere Umgebung: Vorkonfigurierter GitHub Codespace – bereit zum Experimentieren</li></ul>", "tryItButton": "Jetzt ausprobieren", "benefits": {"title": "<PERSON><PERSON><PERSON><PERSON>", "beginners": {"title": "<PERSON><PERSON><PERSON> GitHub Copilot <PERSON>:", "description": "<PERSON><PERSON><PERSON>, wie Sie GitHub Copilot effektiv nutzen und beginnen Sie, es praktisch anzuwenden und in Ihre tägliche Programmierarbeit zu integrieren."}, "advanced": {"title": "<PERSON><PERSON><PERSON> fortgeschrit<PERSON><PERSON> Nutzer von GitHub Copilot:", "description1": "Erhalten Sie Zugang zu den neuesten AI-Modellen, <PERSON><PERSON><PERSON> und Tricks, um Ihre Möglichkeiten zu erweitern.", "description2": "Nutzen Sie GitHub Copilot, um die Codeerstellung auf der Grundlage funktionaler und technischer Spezifikationen zu automatisieren."}}, "highlights": {"title": "Wichtigste Highlights", "intro": "Auch wenn Sie keine Unternehmenslizenz für GitHub Copilot besitzen, können Sie den interaktiven Trainingsplayground mit GitHub Copilot Free nutzen, das von GitHub kostenlos angeboten wird.", "setup": "Um Copilot Free zu verwenden, müssen Sie Copilot Free zunächst in den Einstellungen Ihres GitHub-Kontos aktivieren.", "activation": {"step1": "<PERSON><PERSON><PERSON> Sie in der oberen rechten Ecke einer beliebigen Seite auf GitHub auf Ihr Profilfoto und dann auf „Your Copilot“.", "step2": "Klicken Sie auf „Start using Copilot Free“, um Copilot Free zu aktivieren und einen „Conversation Thread“ zu öffnen.", "step3": "Klicken Sie auf „Use Copilot in other IDEs“ und folgen Sie den Anweisungen für Ihre IDE."}, "with": "Mit GitHub Copilot Free können Sie die folgenden Dinge tun:", "completions": {"title": "2.000 intelligente Code-Vervollständigungen pro Monat:", "description": "Erhalten Sie kontextabhängige Code-Vorschläge, die den Kontext Ihrer GitHub-Projekte und Ihres VS Code Workspace berücksichtigen."}, "chat": {"title": "50 Copilot-Chat-Nachrichten pro Monat:", "description": "Bitten Sie Copilot um Hilfe beim Verstehen von Code, beim Refactoring oder beim Debuggen eines Problems."}, "model": {"title": "Wählen Sie Ihr AI:", "description": "Wählen Sie zwischen OpenAI GPT-4o, Google Gemini 2.0 Flash oder Claude 3.5 Sonnet."}}, "inAction": {"title": "Wesentliche Merkmale", "imageAlt": "GitHub Copilot Code-Beispiel"}, "environment": {"title": "Vorkonfigurierte Entwicklungsumgebung", "description": "GitHub Codespaces stellt die Grundlage für den interaktiven Trainingsplayground und eine vollständig verwaltete, cloudbasierte Umgebung dar, die auf das Erlernen von Copilot zugeschnitten ist.<ul><li>Microsoft Visual Studio Code Online dient als Benutzeroberfläche und gewährleistet eine vertraute und robuste Coding-Erfahrung.</li><li>Die GitHub Copilot-Erweiterung ist vorinstalliert und vorkonfiguriert, sodass Benutzer Copilot sofort ohne zusätzliche Setups verwenden können.</li></ul>"}, "challenges": {"title": "Szenariobasierte Herausforderungen", "description": "Vordefinierte Codierungsaufgaben, um den Nutzen von Copilot in realen Situationen zu demonstrieren, z. B:<ul><li><PERSON><PERSON><PERSON> von algorithmischen Problemen.</li><li>Schreiben und Optimieren von Unit-Tests.</li><li>Mit gängigen Frameworks wie React arbeiten.</li><li>Sofortiges Feedback, das die Beiträge von Copilot zur Lösung hervorhebt.</li></ul>"}, "trainingProgress": {"currentStepTitle": "Einführung in GitHub Copilot"}}, "aiPlanet": {"title": "<PERSON><PERSON> von AI <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>, wie Sie die Tools von AI Planet wirksam nutzen können"}, "aiBusinessSchool": {"title": "Tools der AI Business School", "description": "<PERSON><PERSON><PERSON><PERSON>, wie Sie die eigenen AI Tools der AI Business School wirksam nutzen können"}, "toolkits": {"title": "Download-Toolkits und Ressourcen", "description": "Praktische Ressourcen, e<PERSON><PERSON><PERSON>ßlich Spickzettel für die Eingabeaufforderung, Microsoft 365 Copilot-Tool-Richtlinien für Word, PowerPoint, Teams und Starter-Toolkits für die Einrichtung von Copilot in Ihrer Organisation."}}, "accountSettings": {"title": "Kontoeinstellungen", "subtitle": "Sie können hier einige Ihrer persönlichen Daten direkt anpassen. Einige andere hingegen können nur über eine Kontaktaufnahme geändert werden. In dem Fall wenden Sie sich bitte über den „Hilfe-Bereich“ an uns.", "tabs": {"basicInfo": "Grundlegende Informationen", "personalInfo": "Persönliche Informationen", "language": "<PERSON><PERSON><PERSON>"}, "userProfile": {"title": "Benutzerprofil", "firstName": "Vornamen", "lastName": "Nachname", "saveChanges": "Änderungen speichern", "saving": "Wird gespeichert..."}, "passwordChange": {"title": "Ändern Sie Ihr Passwort", "currentPassword": "Aktuelles Passwort", "newPassword": "Neues Passwort", "confirmPassword": "Bestätigen Sie das neue Passwort", "veryWeak": "<PERSON><PERSON> schwach", "weak": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "strong": "<PERSON>", "passwordStrength": "Passwort Stärke", "lowercaseUppercase": "Mindestens ein Klein- und ein Großbuchstabe", "number": "Mindestens e<PERSON> (0-9)", "specialChar": "Mindestens ein Sonderzeichen (!@#$%^&*.)", "minLength": "Mindestens 8 Zeichen", "passwordsNotMatch": "Passwörter stimmen nicht überein"}, "resetModal": {"title": "Benutzerdaten zurücksetzen", "description": "Diese Aktion setzt die aktuellen Benutzerinformationen zurück.", "deleting": "Wird gel<PERSON>t...", "onboardData": "Onboarding-Daten", "journeyData": "Journey-Daten", "resetButton": "Benutzerdaten zurücksetzen", "userData": "Benutzerdaten"}, "personalInfo": {"industry": {"label": "In welcher Branche sind Sie interessiert?", "placeholder": "Branche auswählen"}, "language": {"label": "Was ist Ihre bevorzugte Sprache?"}, "businessFunction": {"label": "In welchem Geschäftsbereich arbeiten Sie?"}, "businessJobRole": {"label": "In welcher geschäftlichen Rolle arbeiten Sie?"}, "managementRole": {"label": "Sind Sie derzeit in einer Führungsposition?"}, "technicalBackground": {"label": "Wie würden Sie sich in Bezug auf Ihren technischen Hintergrund einschätzen?"}, "aiKnowledge": {"label": "Wie würden Sie Ihr aktuelles AI-Wissen und Ihre Erfahrung einschätzen?"}, "disabledFieldTooltip": "Die Antworten, die Sie während des Onboarding-Prozesses gegeben haben, sind endgültig und können später nicht mehr geändert werden."}}, "notifications": {"userUpdated": "Benutzerinformationen wurden erfolgreich aktualisiert", "passwordChanged": "Passwort wurde erfolgreich geändert", "error": "Ein Fehler ist aufgetreten", "userDataReset": "Benutzerdaten wurden erfolgreich zurückgesetzt"}, "cockpit": {"welcome": {"title": "Persönliches Cockpit", "description": "Einheitliches Dashboard für einen umfassenden Überblick über Ihre Fortschritte."}, "statistics": {"appliedTotalUseCases": "Angewandte Use Cases gesamt", "createdApp": "Erstellte Apps", "createdWorkflow": "Erstellte Workflows", "appliedUniqueUseCases": "Unterschiedliche Use Cases", "submittedIdeas": "Eingereichte Ideen", "obtainedCertificates": "<PERSON><PERSON><PERSON><PERSON>", "completedTrainingJourneys": "Abgeschlossene Trainingsreisen"}, "common": {"times": "mal", "loading": "Wird geladen...", "mostFrequentlyUsedUseCases": "Meine am häufigsten verwendeten Anwendungsfälle", "specialSelectionForYou": "Spezielle Auswahl für Sie", "noFavorites": "<PERSON>e haben noch keine Favorite<PERSON>.", "noMostUsedApps": "Sie haben noch keine A<PERSON> verwendet."}, "badges": {"New": "<PERSON>eu"}, "actions": {"View certificate": "Zertifikat anzeigen"}, "items": {"AI adoption survey": "AI-Adoptionsumfrage", "Master journey assessment": "Master-Journey-<PERSON><PERSON><PERSON><PERSON>", "Test Project": "Testprojekt", "Job-specific use case training for marketing": "Berufsspezifisches Anwendungsfalltraining für Marketing", "Human face generator": "Generator für menschliche Gesichter", "Equipment calibration assistant": "Assistent für Gerätkalibrierung", "E-mail optimizer": "E-Mail-Optimierer", "Translator GPT": "Übersetzer GPT", "Maintenance schedule optimizer": "Wartungsplan-Optimierer", "AI Beginner in IT, AI & Data assessment & certificate": "AI-Ein<PERSON>iger in IT, AI & Datenbewertung & Zertifikat", "Successfully navigate AI risks": "AI-Risiken erfolgreich navigieren"}, "tabs": {"myTrainings": "<PERSON>ne <PERSON>s", "topUseCaseApps": "Top Use Cases", "createdApps": "Erstellte Apps", "ideasSubmitted": "Eingereichte Ideen", "certificates": "Zertifikate", "navigation": "Navigation"}, "trainingTabs": {"inProgress": "In Bearbeitung", "completed": "Abgeschlossen", "goToTraining": "Zum <PERSON> gehen", "noInProgressCourses": "<PERSON><PERSON> haben keine <PERSON> in Bearbeitung.", "noCompletedCourses": "Sie haben noch keine Kurse abgeschlossen."}, "createdApps": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON>, die ich erstellt habe"}, "submittedIdeas": {"title": "<PERSON>ne eingereichten Ideen", "subtitle": "<PERSON><PERSON><PERSON>, die ich eingereicht habe", "viewDetails": "Details anzeigen", "created": "Erstellt am:"}, "levelUp": {"message": "Um mehr Punkte zu sammeln und aufzusteigen, wenden Sie mehr Anwendungsfälle an, erstellen Sie Workflows und teilen Sie mehr Ideen!"}, "certificates": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die ich erhalten habe", "training": "Training", "date": "Datum", "view": "<PERSON><PERSON><PERSON>", "share": "Teilen", "noCertificates": "Sie haben noch keine Zertifikate erhalten.", "levelUp": {"message": "Um mehr Punkte zu sammeln und aufzusteigen, wenden Sie mehr Anwendungsfälle an, erstellen Sie Workflows und teilen Sie mehr Ideen!"}}}, "onboarding": {"steps": {"welcome": "<PERSON><PERSON><PERSON><PERSON>", "changePassword": "Passwort ändern", "language": "Was ist Ihre bevorzugte Sprache?", "businessFunction": "In welcher Unternehmensfunktion arbeiten Sie?", "jobRole": "Was ist Ihre berufliche Spezialisierung?", "managementRole": "Sind Sie derzeit in einer Führungsposition?", "technicalBackground": "Wie würden Sie sich selbst in Bezug auf Ihren technischen Hintergrund einschätzen?", "aiExperience": "Wie würden Sie Ihren derzeitigen Wissens- und Erfahrungsstand zu AI einschätzen?", "industry": "Welche Branche interessiert Sie?", "termsAndConditions": "Geschäftsbedingungen", "review": {"title": "Überprüfen Sie Ihre Auswahl", "description": "Ihre Einstellungen können später nicht mehr geändert werden. Sie können Ihre Auswahl auf der Seite Kontoeinstellungen einsehen."}, "of": "von"}, "welcome": {"title": "Willkommen {{username}}", "description": "Personalisieren Sie Ihre AI-Reise in {{stepCount}} <PERSON><PERSON><PERSON><PERSON>, indem Sie Ihre Interessengebiete auswählen und Ihre Erfahrung auf Ihre Berufsrollen und Domänen abstimmen."}, "jobRole": {"customRoleLabel": "<PERSON><PERSON><PERSON> Sie Ihre Berufsrolle ein", "customRolePlaceholder": "Geben Sie Ihre spezifische Berufsrolle oder Position ein", "noResults": "<PERSON><PERSON> Berufsrollen gefunden, die Ihrer Suche entsprechen. Versuchen Sie es mit anderen Schlüsselwörtern.", "searchPlaceholder": "Geben Sie mindestens 3 <PERSON><PERSON><PERSON> ein, um nach Berufsrollen zu suchen..."}, "industry": {"showAll": "Alle Branchen anzeigen", "hide": "<PERSON>en ausblenden"}, "password": {"newPassword": "Neues Passwort", "confirmPassword": "Passwort bestätigen", "requirements": "Passwortanforderungen", "validation": {"minLength": "Mindestens 8 Zeichen", "hasUpperCase": "Mindestens ein Großbuchstabe", "hasLowerCase": "Mindestens ein Kleinbuchstabe", "hasSpecialChar": "Mindestens ein Sonderzeichen (!@#$%^&*(),.)", "passwordsMatch": "Passwörter stimmen überein"}}, "form": {"selectOption": "Wählen Sie eine Option", "required": "<PERSON><PERSON> ist erford<PERSON>lich", "acceptTerms": "Bitte akzeptieren Sie die Allgemeinen Geschäftsbedingungen"}, "terms": {"accept": "I accept the", "privacyPolicy": "Privacy Policy", "and": "and", "termsAndConditions": "Terms and Conditions"}, "completion": {"processing": "Vielen Dank für Ihre Zeit! Wir stellen die Plattform für Sie ein.", "processingStep": "Verarbeitung {{stepTitle}}", "generatingStep": "Generierung {{step<PERSON><PERSON><PERSON>}}", "waitingForResponse": "<PERSON>ten auf Systemantwort...", "success": "Onboarding erfolgreich abgeschlossen!", "passwordTooltip": "Bitte geben Sie Ihr Passwort gemäß den Regeln ein."}, "mandatoryVideo": {"title": "Obligatorisches Schulungsvideo", "description": "<PERSON>te sehen <PERSON> sich dieses obligatorische Schulungsvideo an, bevor <PERSON> den Onboarding-Prozess abschließen.", "watchRequirement": "OBLIGATORISCH: <PERSON><PERSON> müssen das gesamte Video ansehen. Das Video kann nicht übersprungen, pausiert oder vorgespult werden. Das Onboarding wird automatisch abgeschlossen, wenn das Video endet.", "completed": "Video abgeschlossen! Sie können nun mit dem Abschluss Ihres Onboardings fortfahren.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_Final_Willkommen_bei_der_AI_Business_School_240425_DE_V02.mp4/Video_Final_Willkommen_bei_der_AI_Business_School_240425_DE_V02.m3u8", "footerNote": "Hin<PERSON>s: Zum Fortfahren ist das Anschauen des Videos erforderlich. Anschliessend werden Ihre Angaben erfasst und Sie werden automatisch eingeloggt.", "skipVideo": "Video überspringen"}}, "textUsecase": {"form": {"title": "Use-Case-Eingaben", "tooltip": "Füllen Sie die Formularfelder aus, um Text zu generieren", "addToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "poweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "cancel": "Abbrechen", "generate": "<PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON> gene<PERSON>"}, "output": {"title": "Ausgabe", "modifyResponse": "Antwort ändern", "shorter": "<PERSON><PERSON><PERSON><PERSON> machen", "longer": "<PERSON><PERSON><PERSON> machen", "simpler": "Vereinfachen", "copy": "In die Zwischenablage kopieren", "copied": "Kopiert!", "clear": "Ausgabe löschen", "emptyTitle": "Anwendungsfall-Assistent", "emptyDescription": "Sie können AI-gestützten Text generieren, indem Sie das Formular links ausfüllen. Das Formular enthält {{count}} Felder.", "feature1": "AI-gestützte Generierung", "feature2": "Bearbeitungsoptionen", "feature3": "Einfaches Kopieren", "feature4": "Schnelles Löschen"}}, "heygenVideoCreator": {"welcome": {"title": "HeyGen video creator", "description": "<p><PERSON>rstellen Sie professionelle Videos in Minuten! - z.B. für Mitarbeiterschulungen, Produktaktualisierungen oder Stakeholder-Präsentationen. Binden Sie Ihre Zuschauer mühelos in verschiedenen Sprachen ein.</p><p>Passen Sie jeden Aspekt an Ihre Ziele an - von der Auswahl des idealen Avatars und der Stimme bis hin zur effizienten Gestaltung der perfekten Geschichte für Ihre Videos, Präsentationen oder Vorschläge.</p>"}, "chooseAvatar": {"title": "Avatar auswählen", "female": "<PERSON><PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON><PERSON>", "addToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen"}, "chooseVoice": {"title": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "selectVoice": "<PERSON><PERSON><PERSON> au<PERSON>wählen"}, "videoScript": {"title": "Video-<PERSON><PERSON><PERSON><PERSON>", "generateVideo": "Video generieren", "generateVideoTooltip": "Maximal 8 Videos sind erlaubt, hochgeladene Videos werden maximal 7 Tage gespeichert.", "generatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "characterLimit": " / 500", "limitExceeded": "Text überschreitet das Limit von 500 Zeichen", "initialText": "Willkommen in der Welt der AI-gestützten Avatar-Videoerstellung, maßgeschneidert für Sie. Hier haben Sie die Möglichkeit, Ihre Geschichte mit anpassbaren Skripten zu gestalten, aus einer Vielzahl von Stimmen zu wählen und sogar Ihre bevorzugte Sprache auszuwählen. Lassen Sie Ihrer Kreativität freien Lauf und tauchen Sie ein in das Experimentieren.", "noVoiceSelected": "<PERSON>te wählen Si<PERSON> eine Stimme au<PERSON>, bevor <PERSON> ein Video generieren", "noAvatarSelected": "<PERSON>te wählen Si<PERSON> einen Avatar aus, bevor <PERSON> ein Video generieren", "avatarNotFound": "Ausgewählter Avatar konnte nicht gefunden werden. Bitte versuchen Si<PERSON> einen anderen Avatar auszuwählen.", "avatarIdNotFound": "Avatar-ID konnte nicht gefunden werden. Bitte versuchen Si<PERSON> einen anderen Avatar auszuwählen.", "generationFailed": "Videogenerierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "noVideoIdInResponse": "<PERSON>ine Video-ID in der Antwort. Bitte versuchen Sie es später erneut.", "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut."}, "previewVideo": {"title": "Video-Vors<PERSON>u", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generating": "Video generieren", "generatingDescription": "Die Videogenerierung läuft und kann etwa 10 Minuten dauern. Die genaue Zeit kann je nach Serverauslastung, Skriptlänge und anderen Faktoren variieren. Vielen Dank für Ihre Geduld!", "status": "Status", "videoGenerated": "Video erfolgreich generiert", "generationFailed": "Videogenerierung fehlgeschlagen. Bitte versuchen Sie es erneut."}}, "playgrounds": {"common": {"backToPrompt": "Zurück zur Eingabeaufforderung", "typeMessage": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht hier ein...", "welcomeTitle": "Wie kann ich Ihnen heute helfen?", "sendMessage": "Nachricht senden", "back": "Zurück"}, "chatGPT": {"title": "ChatGPT", "description": "Will<PERSON>mmen in der faszinierenden Welt von ChatGPT, in der wir mit eigenen Worten beschreiben können, was wir wollen, und die AI uns dann entsprechende Ergebnisse liefert.", "tryItButton": "Selbst ausprobieren", "tryItYourself": "Selbst ausprobieren", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Final%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BUPD%5D+__+%5BDE%5D-ChatGPT-Playground-Teaser-%5BPEREMECI%5D_vf.mp4/Final%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BUPD%5D+__+%5BDE%5D-ChatGPT-Playground-Teaser-%5BPEREMECI%5D_vf.m3u8", "settings": "Einstellungen", "model": "<PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "frequencyPenalty": "Frequency penalty", "presencePenalty": "Presence penalty", "disclaimer": "Bitte teilen Sie während der Übungen keine vertraulichen Geschäftsdaten, einsch<PERSON>ßlich Kunden-, Produkt- oder Mitarbeiterinformationen, mit ChatGPT, um die Sicherheit sensibler Daten jederzeit zu gewährleisten.", "welcome": "Willkommen beim <PERSON>tGPT Playground", "welcomeDescription": "Fragen Si<PERSON> mich alles und ich werde mein Bestes tun, um Ihnen zu helfen.", "showSettings": "Einstellungen anzeigen", "settingsTooltip": "<PERSON>er können Si<PERSON> das Modell, die Temperatur und andere AI-Parameter anpassen", "samplePrompt": "Was ist generative AI, und wie unterscheidet sie sich von traditioneller AI?", "trainingProgress": {"currentStepTitle": "Einführung in ChatGPT"}}, "dallE": {"title": "DALL·E", "description": "Ideen in kreative Bilder verwandeln!", "tryItButton": "Selbst ausprobieren", "tryItYourself": "Versuchen Sie es selbst", "tryItDescription": "Probieren Sie die untenstehende Aufforderung aus oder ändern Sie den Text der Aufforderung, um DALL-E jetzt auszuprobieren.", "typePrompt": "Beschreiben Sie das Bild, das Sie generieren möchten...", "generateImage": "<PERSON><PERSON><PERSON> gene<PERSON>", "disclaimer": "<PERSON>te stellen <PERSON> sicher, dass Ihre Bildgenerierungsaufforderungen unseren Inhaltsrichtlinien entsprechen und keine unangemessenen oder schädlichen Inhalte anfordern.", "samplePrompt": "Eine ruhige Landschaftsmalerei eines Bergsees bei Sonnenuntergang, mit lebendigen Farben, die sich im ruhigen Wasser spiegeln", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/FINAL%E2%9A%A0%EF%B8%8F28-11+%5BUPD%5D+__+%5BDE%5D-Dall-E-Playground-Teaser-%5BPEREMECI%5D.mp4/FINAL%E2%9A%A0%EF%B8%8F28-11+%5BUPD%5D+__+%5BDE%5D-Dall-E-Playground-Teaser-%5BPEREMECI%5D.m3u8", "trainingProgress": {"currentStepTitle": "Einführung in DALL-E"}}, "simpleAIAppCreator": {"title": "Simple AI App Creator", "description": "Auf spielerisch einfache Weise eigene AI Apps kreieren", "createAppButton": "<PERSON>pp erstellen", "myAppsTitle": "<PERSON><PERSON>", "myAppsSubtitle": "<PERSON><PERSON>, die ich erstellt habe und bearbeiten kann.", "noAppsText": "Sie haben noch keine A<PERSON> erstellt.", "loadError": "<PERSON><PERSON>hrer Apps ist ein Fehler aufgetreten.", "noAppsYet": "Sie haben noch keine A<PERSON> erstellt.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F+04-11+%5BNEW%5D+__+%5BDE%5D-Simple-AI-App-Creator-Teaser-%5BPEREMECI%5D.mp4/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F+04-11+%5BNEW%5D+__+%5BDE%5D-Simple-AI-App-Creator-Teaser-%5BPEREMECI%5D.m3u8"}, "agenticWorkflowCreator": {"title": "Agentic workflow Creator", "description": "Einfache Agentic Workflows in Form von Prompt-Ketten kreieren", "createAppButton": "Workflow erstellen", "myAppsTitle": "<PERSON>ne er<PERSON>llten Workflows", "myAppsSubtitle": "<PERSON> von Ihnen erstellten Anwendungen.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F+04-11+%5BNEW%5D+__+%5BDE%5D-Agentic-Workflow-Creator-Teaser-%5BPEREMECI%5D.mp4/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F+04-11+%5BNEW%5D+__+%5BDE%5D-Agentic-Workflow-Creator-Teaser-%5BPEREMECI%5D.m3u8", "loadError": "<PERSON>im <PERSON>hrer Workflows ist ein Fehler aufgetreten.", "noWorkflowsYet": "Sie haben noch keine Workflows erstellt."}, "heygen": {"title": "HeyGen Video Creator", "description": "Ihre eigenen Ideen in wenigen Minuten auf dem Bildschirm zum Leben erwecken - mit HeyGen: dieses versierte AI-Tool produziert kinderleicht professionelle Videos mit AI-generierten Avataren. <PERSON><PERSON> sich unser Einführungsvideo an und legen Sie direkt los!", "tryItButton": "Jetzt ausprobieren", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_17-01_2025+%5BUPD%5D+__+%5BDE%5D-Heygen-Playground-Teaser-%5B020125DE1%5D-%5BV2%5D_vf.mp4/Video_17-01_2025+%5BUPD%5D+__+%5BDE%5D-Heygen-Playground-Teaser-%5B020125DE1%5D-%5BV2%5D_vf.m3u8", "trainingProgress": {"currentStepTitle": "Einführung in DALL-E"}}, "advancedGenAI": {"title": "Advanced AI Creator", "description": "<PERSON><PERSON> dieser hochmodernen Lösung von AI Planet können Sie mit einigen der besten AI Tools der Welt leistungsstarke, eigene AI Lösungen erstellen", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_%5BDE%5D-Final_Advanced+GenAI+creator-Teaser_28.10.2024.mp4/Video_%5BDE%5D-Final_Advanced+GenAI+creator-Teaser_28.10.2024.m3u8", "trainingProgress": {"currentStepTitle": "Einführung in Advanced GenAI"}}, "chatGPTDeveloperTools": {"title": "ChatGPT IT Entwickler Tools", "description": "<PERSON><PERSON><PERSON><PERSON>, spezifische IT-Aufgaben vereinfachen und automatisieren", "tryItButton": "Selbst ausprobieren", "tryItYourself": "Selbst ausprobieren", "tryItDescription": "Verwenden Sie den unten stehenden Beispiel-Prompt oder geben Sie Ihren eigenen ein, um ChatGPT jetzt auszuprobieren.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Final%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BNEW%5D+_+%5BEN%5D-IT-Playground-ChatGPT-Developers-Teaser-w+talking+head%5BPEREMECI%5D.mp4/Final%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BNEW%5D+_+%5BEN%5D-IT-Playground-ChatGPT-Developers-Teaser-w+talking+head%5BPEREMECI%5D.m3u8", "samplePrompt": "[Sprache/Framework/DBMS]: Python/Django/PostgreSQL\n[Problembeschreibung]: Sie entwickeln eine Mitfahrzentrale-Anwendung, die Fahrer mit Fahrgästen verbindet. Jeder Fahrer und Fahrgast hat einen bestimmten Standort, der durch Breiten- und Längenkoordinaten dargestellt wird. Ihre Aufgabe ist es, einen Algorithmus zu entwerfen, der Fahrer effizient mit Fahrgästen in der Nähe zusammenbringt und dabei die Entfernung zwischen ihren Standorten berücksichtigt. Die Anwendung sollte die bestmögliche Übereinstimmung finden, um die Wartezeiten für Fahrgäste zu minimieren und die Auslastung der Fahrer zu optimieren.\n-----------------------------------------------------\nGenerieren Sie Code mit [Sprache/Framework/DBMS], um das Problem zu lösen: [Problembeschreibung].\n\nVerwenden Sie die folgenden Anweisungen und Vorschläge:\n\n- Sicherheit: Untersuchen Sie den Code auf potenzielle Schwachstellen\n- Kompatibilität: Bereiten Sie Vorschläge zu möglichen Kompatibilitätsproblemen vor\n- Kommentare hinzufügen: Machen Sie den Code verständlicher, indem Sie Kommentarzeilen hinzufügen\n- Aktuell bleiben: Benachrichtigen Sie über mögliche Versionsunterschiede\n\nWenn Sie einen Algorithmus verwendet haben, erklären Sie ihn. Zeigen Sie [Sprache/Framework/DBMS]-Versionen als Tabelle an.", "trainingProgress": {"currentStepTitle": "Ein<PERSON>ührung in ChatGPT IT Entwickler Tools"}}}, "assistantUsecase": {"pdfViewer": {"uploadTitle": "PDF-Date<PERSON> hochladen oder Beispieldokument verwenden", "selectLocalFile": "Lokale Datei auswählen", "or": "oder", "loadSample": "Beispieldokument laden", "uploadFeatureTooltip": "Diese Funktion ist in Ihrem aktuellen Abonnement nicht enthalten – für weitere Informationen kontaktieren Sie bitte Ihren AI Business School Account Manager.", "prevPage": "Vorherige Seite", "nextPage": "Nächste Seite", "page": "Seite", "of": "von", "zoomOut": "Verkleinern", "zoomIn": "Vergrößern", "fullscreen": "Vollbild"}, "chat": {"title": "AI Assistent", "askPlaceholder": "Fragen Sie mich etwas...", "sampleQuestions": "Beispielfragen:", "closeQuestions": "Fragen schließen", "analyzingPdf": "PDF wird analysiert, bitte warten...", "welcomeMessage": "<PERSON><PERSON>, klicken Sie auf 'Beispieldokument laden', um die Anwendungsbeispiele anzuzeigen.", "pdfError": "<PERSON>im <PERSON>den des PDFs ist ein Fehler aufgetreten. Der Chat ist dennoch verfügbar.", "pdfMissing": "PDF-URL fehlt. Der Chat ist dennoch verfügbar.", "summarizeError": "<PERSON><PERSON>fassen des PDFs ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "pdfLoadWarning": "Bitte klicken Sie auf die Schaltfläche 'Beispieldokument laden', um ein PDF-Dokument zu laden, bevor <PERSON> fort<PERSON>hren.", "pdfLoadSuccess": "PDF analysiert. Wie kann ich Ihnen helfen?", "messageError": "Ents<PERSON>ldigung, ein <PERSON> ist aufgetreten. Bitte versuchen Sie es erneut.", "assistant": "Assistent", "you": "<PERSON><PERSON>"}}, "imageUsecase": {"wait": {"process": "Bitte warten Sie, bis der aktuelle Vorgang abgeschlossen ist...", "seconds": "Bitte warten Si<PERSON> einige Sekunden..."}, "error": {"savingProgress": "<PERSON>im S<PERSON>ichern des Fortschritts ist ein Fehler aufgetreten.", "prefix": "<PERSON><PERSON>", "general": "<PERSON><PERSON> des Bildes ist ein Fehler aufgetreten"}, "form": {"title": "Bildgenerator", "tooltip": "<PERSON>üllen Sie die Felder aus, um ein Bild zu generieren.", "aspectRatio": "Seitenverhältnis", "aspectRatioTypes": {"square": "<PERSON>uadratisch", "wide": "Breit", "portrait": "Porträt"}, "poweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "model": "DALL-E 3", "cancel": "Abbrechen", "generateNew": "<PERSON><PERSON> gene<PERSON>", "generate": "<PERSON><PERSON><PERSON>"}, "output": {"title": "Generiertes Bild", "emptyMessage": "<PERSON> von Ihnen erstellte Bild wird hier angezeigt.", "generating": "Wird generiert...", "newImage": "<PERSON>eu", "imagesGenerated": "Bilder gene<PERSON><PERSON>", "download": "Bild her<PERSON><PERSON><PERSON>n", "openInNewTab": "In neuem Tab <PERSON>", "clear": "Löschen", "clickToZoom": "Zum Vergrößern klicken"}, "warning": {"maxImages": "Sie können maximal 5 Bilder erstellen."}}, "tutorials": {"simpleAIAppCreator": {"title": "Simple AI App creator <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, wie Sie benutzerdefinierte Anwendungsfälle erstellen, die auf Ihre spezifischen Geschäftsanforderungen zugeschnitten sind.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_01-%5BDe%5D-Use+Case+App+Creator-Vf.mp4/Video_01-%5BDe%5D-Use+Case+App+Creator-Vf.m3u8", "completedMessage": "Video abgeschlossen", "pleaseLogin": "Bitte melden Sie sich an, um auf dieses Tutorial zuzugreifen.", "backToPlayground": "Zurück zum Playground"}, "agenticWorkflowCreator": {"title": "Agentic workflow creator <PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>, wie Sie Agentic Workflows erstellen, die komplexe Aufgaben mit Hilfe von AI automatisieren, mehrstufige Prozesse autonom abschließen und die Leistung des GPT-Modells iterativ verbessern.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video+03+%5BDE%5D+-+WorkflowCreator-Vf_14TH+august.mp4/Video+03+%5BDE%5D+-+WorkflowCreator-Vf_14TH+august.m3u8", "completedMessage": "Video abgeschlossen", "pleaseLogin": "Bitte melden Sie sich an, um auf dieses Tutorial zuzugreifen.", "backToPlayground": "Zurück zum Playground"}, "advancedGenAICreator": {"title": "Advanced AI Creator Anleitung", "description": "Entdecken Sie das leistungsstarke AI-Tool und seine unendlichen Möglichkeiten. Lernen Sie in diesem Tutorial Schritt für Schritt, wie Sie einen Übersetzungs-Chatbot erstellen.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F15-11-%5BUPD%5D+_+DE-Advanced-GenAI-Tutorial-Audio-Patch-V02-%5BPEREMECI%5D_vf.mp4/Video%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F15-11-%5BUPD%5D+_+DE-Advanced-GenAI-Tutorial-Audio-Patch-V02-%5BPEREMECI%5D_vf.m3u8", "completedMessage": "Video abgeschlossen", "pleaseLogin": "Bitte melden Sie sich an, um auf dieses Tutorial zuzugreifen.", "backToPlayground": "Zurück zum Playground"}, "heygen": {"title": "Einführung in HeyGen", "description": "<PERSON><PERSON><PERSON>, wie <PERSON> mit HeyGen, einem leistungsstarken AI-Videoproduktionswerkzeug, personalisierte Videos erstellen.", "pleaseLogin": "Bitte melden Sie sich an, um auf dieses Tutorial zuzugreifen.", "howToUseAvatars": "Wie man öffentliche Avatare in HeyGen verwendet", "welcomeText": "Willkommen zu HeyGen-Tutorials von HeyGen! Machen <PERSON> sich bereit, in die aufregende Welt der Avatare einzutauchen und die Geheimnisse dahinter zu entdecken. In diesem Tutorial führen wir Sie Schritt für Schritt durch den Prozess der Auswahl eines öffentlichen Avatars und der Erstellung eines erstaunlichen Videos.", "meetNewestAvatars": "<PERSON><PERSON><PERSON> Si<PERSON> die neuesten Avatare von HeyGen kennen", "meetHeygenText": "<PERSON><PERSON>n Sie HeyGen kennen, die Plattform für Videoproduktion der nächsten Generation, die Ihren Text in nur wenigen Minuten in ein professionelles Sprechervideo verwandelt!", "howWeCreated": "Wie wir einen AI-Avatar erstellt haben!", "howWeCreatedText": "Dieses Video zeigt den <PERSON>, wie diese AI-Avatare zum Leben erweckt werden, von der sorgfältigen Auswahl und Vorbereitung der Schauspieler bis hin zur Erfassung der komplexen Details menschlicher Ausdrücke und Bewegungen. Wir zeichnen jede Nuance auf, um Ihnen die lebensechtesten digitalen Avatare zu präsentieren!"}, "spesific_sales": {"title": "Spezifische Vertrieb Use Cases", "description": "Beispiele aus der Praxis, wie verschiedene Rollen im Vertrieb - vom Verkäufer im Außendienst bis zum Enablement Manager - Co<PERSON><PERSON> nutzen können, um die täglichen Aufgaben zu optimieren.", "backToPlayground": "Zurück zum Playground", "pdfUrl": "https://aibs-content.s3.eu-central-2.amazonaws.com/Specific+Sales+use+cases_scenario+library.pdf"}}, "promptLibrary": {"noFavorites": "Sie haben noch keine Favoriten-Prompts. Durchsuchen Sie die Bibliothek und fügen Sie einige zu Ihren Favoriten hinzu!", "title": "Prompt-Bibliothek", "description": "Ent<PERSON>cken und nutzen Sie unsere Sammlung effektiver Prompts für verschiedene AI-Anwendungen", "allPrompts": "Alle Prompts", "yourFavoritePrompts": "<PERSON><PERSON><PERSON>-Prompts", "selectProvider": "Anbieter auswählen", "allProviders": "Alle Anbieter", "selectApp": "App ausw<PERSON>hlen", "allApps": "Alle Apps", "selectFunction": "Funktion auswählen", "allFunctions": "Alle Funktionen", "errorLoadingPrompts": "Fehler beim Laden der Prompts. Bitte versuchen Sie es später erneut.", "errorLoadingFavorites": "Fehler beim Laden Ihrer Lieblings-Prompts. Bitte versuchen Sie es später erneut.", "noPromptsFound": "<PERSON><PERSON> Prompts gefunden. Bitte versuchen Sie es mit einer anderen Filterung."}, "promptModal": {"title": "<PERSON><PERSON> Prompt ausprobieren", "makeItYourOwn": "<PERSON>hen Si<PERSON> es zu Ihrem eigenen", "author": "Provider", "worksIn": "Arbeitet in", "function": "Funktion", "copyPrompt": "Prompt kopieren", "copied": "Kopiert!", "tryThisPrompt": "<PERSON><PERSON> Prompt ausprobieren", "tryItIn": "<PERSON><PERSON>ren <PERSON>e es in {{app}} aus", "open": "<PERSON><PERSON><PERSON>", "typeMessage": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht hier ein...", "howCanIHelp": "Wie kann ich Ihnen heute helfen?", "settings": {"title": "Einstellungen", "model": "<PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "frequencyPenalty": "Frequency penalty", "presencePenalty": "Presence penalty", "temperatureDescription": "Steuert die Zufälligkeit: Eine Senkung führt zu fokussierteren und deterministischeren Antworten, während eine Erhöhung zu vielfältigeren und kreativeren Ausgaben führt. (0 = fokussiert, 2 = kreativ)", "topPDescription": "Steuert die Vielfalt durch Nucleus-Sampling: Niedrigere Werte halten die Antworten stärker auf hochwahrscheinliche Tokens fokussiert, während höhere Werte mehr Vielfalt zulassen. (0 = fokussiert, 1 = vielfältig)", "frequencyPenaltyDescription": "Reduziert Wiederholungen, indem die Wahrscheinlichkeit der Verwendung von To<PERSON>s, die bereits im Text erschienen sind, verringert wird. Höhere Werte bedeuten weniger Wiederholung. (0 = keine Strafe, 2 = hohe Strafe)", "presencePenaltyDescription": "Fö<PERSON>rt die Verwendung neuer Themen, indem Tokens bestraft werden, die im gesamten Text erscheinen. Höhere Werte ermutigen dazu, neue Themen abzudecken. (0 = keine Strafe, 2 = hohe Strafe)"}, "fileSelector": {"selectFile": "<PERSON>ä<PERSON>en Sie e<PERSON> Datei", "loadingFiles": "<PERSON><PERSON> werden geladen...", "pleaseSelectFile": "Bitte wählen Sie e<PERSON> Datei, um fortzufahren", "shared": "Get<PERSON><PERSON>"}, "warnings": {"loginTooltip": "<PERSON>ür eine effektive Nutzung müssen Si<PERSON> sich mit Ihrem Microsoft-Konto anmelden."}, "backToPrompt": "<PERSON><PERSON><PERSON> zum Prompt", "loading": {"msalNotInitialized": "MSAL noch nicht initialisiert, Initialisierung läuft...", "msalInitialized": "MSAL in useEffect initialisiert", "msalInitFailed": "MSAL-Initialisierung in useEffect fehlgeschlagen", "msalInitDuringLogin": "MSAL während der Anmeldung initialisiert", "msalInitFailedDuringLogin": "MSAL-Initialisierung während der Anmeldung fehlgeschlagen", "usingInteractiveMethod": "Stille Token-Akquisition fehlgeschlagen, interaktive Methode wird verwendet", "loginSuccess": "Erfolgreich mit Ihrem Microsoft-Konto angemeldet: {{name}}", "loginError": "Bei der Anmeldung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."}, "errors": {"tokenAcquisitionFailed": "Token-Akquisition fehlgeschlagen", "personalFilesError": "Persönliche Dateien konnten nicht abgerufen werden:", "sharedFilesError": "Gemeinsam genutzte Dateien konnten nicht abgerufen werden:", "loginError": "Anmeldefehler:"}, "logs": {"allUserFiles": "Alle Benutzerdateien:"}}, "shareYourIdea": {"title": "Teilen Sie Ihre Idee", "description": "Teilen Sie Ihre eigene Idee für einen spezifischen AI Use Case (Anwendungsfall): be<PERSON><PERSON><PERSON><PERSON>, wie mit Hilfe von AI, Verbesserungen, Optimierungen und Innovationen in der eigenen Firma und am eigenen Arbeitsplatz erreicht werden können.", "buttons": {"NewIdea": "Eine neue Idee einreichen"}, "modal": {"title": "Teilen Sie Ihre Idee", "submitting": "Ihre Idee wird übermittelt..."}}, "ideation": {"form": {"title": "Teilen Sie Ihre Idee", "defaultTitle": "Teilen Sie Ihre Idee", "defaultDescription": "Teilen Sie Ihre Idee für einen AI-Anwendungsfall", "formNotFound": "Formular nicht gefunden. Bitte versuchen Sie es später erneut.", "errors": {"requiredFields": "Bitte füllen Si<PERSON> alle erforderlichen Felder aus", "submission": "Bei der Übermittlung Ihrer Idee ist ein Fehler aufgetreten", "generic": "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut."}, "success": {"submitted": "Ihre Idee wurde erfolgreich eingereicht"}, "steps": {"describe": {"title": "Beschreiben Sie Ihre Idee", "label": "Beschreibung"}, "classify": {"title": "Klassifizieren Sie Ihre Idee", "label": "Klassifikation"}}}, "submissions": {"title": "<PERSON>ne eingereichten Ideen", "subtitle": "<PERSON><PERSON><PERSON>, die ich eingereicht habe", "noIdeas": "Sie haben noch keine Ideen eingereicht"}}, "simpleAIApps": {"create": {"title": "Simple AI App Creator", "description": "Auf spielerisch einfache Weise eigene AI Apps kreieren.", "nameSection": {"title": "Name der App Use Case", "placeholder": "Wie lautet der Name Ihrer App?"}, "descriptionSection": {"title": "Beschreibung der App Use Case", "placeholder": "Wie lautet die Beschreibung Ihrer Anwendung?"}, "warning": "Bitte geben Si<PERSON> während der Übungen keine vertraulichen Geschäftsinformationen Daten, einsch<PERSON>ßlich Kunden-, Produkt- oder Mitarbeiterinformationen, an ChatGPT weiter, um die Sicherheit von Daten jederzeit zu gewährleisten.", "buttons": {"back": "<PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "reviewApp": "Vorschau", "create": "<PERSON><PERSON><PERSON><PERSON>"}, "tour": {"welcome": "<PERSON><PERSON><PERSON>n beim Simple AI App Creator", "next": "<PERSON><PERSON>", "previous": "Zurück", "done": "<PERSON><PERSON><PERSON>", "skip": "Überspringen", "showTourAgain": "Tour anzeigen", "steps": {"appDescription": {"title": "App-Name & Beschreibung", "content": "Beginnen Sie mit der Eingabe eines Namens und einer Beschreibung für Ihre App. Dies hilft Benutzern zu verstehen, was <PERSON><PERSON><PERSON> <PERSON><PERSON> macht."}, "inputTypes": {"title": "Eingabetypen", "content": "<PERSON><PERSON>hlen Sie aus verschiedenen Eingabetypen, die Sie Ihrem Formular hinzufügen möchten. Sie können bis zu 5 Formularelemente hinzufügen."}, "formElements": {"title": "Formularelemente", "content": "Konfigurieren Sie hier Ihre Formularelemente. Sie können Namen, Standardwerte und Optionen für Dropdown-Felder hinzufügen."}, "navigationButtons": {"title": "<PERSON>ter zum nächsten Schritt", "content": "Verwenden Sie diese Schaltflächen, um zwischen den Schritten zu navigieren oder zurückzugehen, um Ihr Formular zu bearbeiten."}}, "step2": {"welcome": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>-Schreiben", "steps": {"promptEditor": {"title": "Prompt-Editor", "content": "Schreiben Sie hier Ihren Prompt. Sie können Formularelemente aus dem rechten Bereich per Drag-and-Drop in Ihren Prompt einfügen."}, "editInputs": {"title": "Eingaben bearbeiten", "content": "Dieses Panel zeigt alle Ihre Formularelemente an. Wenn Si<PERSON> sie korrigieren möchten, können Si<PERSON> auf das Symbol klicken und zurückgehen."}, "appSettings": {"title": "Modelleinstellungen", "content": "Konfigurieren Sie KI-Modelleinstellungen wie Temperatur, Top-P und Frequenzstrafe, um das Ausgabeverhalten zu steuern."}, "navigationButtons": {"title": "Überprüfen Sie Ihre App", "content": "<PERSON><PERSON><PERSON> Sie die Schritte abgeschlossen haben, können Si<PERSON> eine Vorschau Ihrer Bewerbung anzeigen und prüfen, wie sie funktioniert."}}}}, "loading": "Wird geladen...", "review": {"title": "App-Überprüfung", "previous": "<PERSON><PERSON><PERSON><PERSON>", "creating": "Wird erstellt...", "updating": "Wird aktualisiert...", "createApp": "<PERSON>pp erstellen", "updateApp": "App aktualisieren"}, "toast": {"success": {"title": "Anwendung erfolgreich erstellt!", "description": "Sie werden zur Anwendungsseite weitergeleitet."}, "error": {"title": "Ein Fehler ist aufgetreten", "description": "<PERSON><PERSON> der Anwendung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."}}, "formElements": {"title": "Formularelemente", "addField": "<PERSON><PERSON>", "inputName": "Eingabename", "inputNamePlaceholder": "Eingabename hinzufügen", "defaultText": "Standardtext", "defaultTextPlaceholder": "Standardtext hinzufügen (optional)", "addOptions": "Optionen hinzufügen", "optionPlaceholder": "Option", "addOption": "Option hinzufügen"}, "inputTypes": {"title": "Eingabe-Optionen", "shortText": "Kurzer Text", "paragraph": "Längerer Text", "dropdown": "Auswahlliste", "radio": "Radio", "yesNo": "<PERSON><PERSON> / <PERSON><PERSON>", "checkbox": "Kontrollkästchen", "fileUpload": "<PERSON><PERSON> ho<PERSON>n"}, "promptEditor": {"title": "Aufforderung zum Schreiben", "elementsTitle": "Prompt-Elemente festlegen", "elementsDescription": "Sie können die von Ihnen erstellten Eingabeparameter durch Ziehen und Ablegen in Ihre Eingabeaufforderung aufnehmen.", "yourPrompt": "Den Prompt festlegen"}, "editInputs": {"title": "Eingaben bearbeiten"}, "appSettings": {"title": "Modelleinstellungen", "model": "<PERSON><PERSON>", "models": {"gpt4o": "GPT-4o"}, "temperature": {"title": "Temperature", "tooltip": "Eine niedrigere Temperatur führt zu weniger zufälligen Antworten. Je näher die Temperatur gegen <PERSON> geht, desto deterministischer und sich wiederholender wird das Modell."}, "topP": {"title": "Top P", "tooltip": "Steuert die Vielfalt durch Nucleus-Sampling: 0,5 bedeutet, dass die Hälfte aller wahrscheinlichkeitsgewichteten Optionen berücksichtigt wird."}, "frequencyPenalty": {"title": "Frequency penalty", "tooltip": "Wie stark neue Token basierend auf ihrer bisherigen Häufigkeit im Text bestraft werden. Verringert die Wahrscheinlichkeit, dass das Modell dieselbe Zeile wörtlich wiederholt."}, "presencePenalty": {"title": "Presence penalty", "tooltip": "Wie stark neue Token bestraft werden, b<PERSON><PERSON><PERSON>, ob sie bereits im bisherigen Text vorkommen. E<PERSON>h<PERSON>ht die Wahrscheinlichkeit, dass das Modell über neue Themen spricht."}}}, "edit": {"toast": {"updateSuccess": "Anwendung erfolgreich aktualisiert! Sie werden zur Anwendungsseite weitergeleitet.", "updateError": "Beim Aktualisieren der Anwendung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."}}}, "useCases": {"title": {"default": "Anwendungsfälle", "withFunction": "Berufsspezifische AI Use Cases und Apps ({{function}})"}, "description": "Entdecken Sie sorgfältig ausgewählte, relevante Use Cases für Ihre Unternehmensfunktion und Ihren spezifischen Job-Bereich", "filters": {"all": "Alle Anwendungsfälle", "text": "Texterstellung", "video": "Videoerstellung", "image": "Bilderstellung"}, "noResults": "<PERSON><PERSON> Anwendungsfälle für den ausgewählten Filter gefunden."}, "sheets": {"it": {"title": "IT-Spickzettel", "description": "Spick<PERSON><PERSON>l sind komprimierte, informative Dokumente, die als schnelle Referenz dienen und Sie durch ein bestimmtes Thema oder eine bestimmte Fähigkeit führen. <PERSON>e sollen I<PERSON> helfen, die wichtigsten Konzepte, <PERSON>eln, <PERSON><PERSON><PERSON><PERSON> oder Schritte zu einem bestimmten Thema zu verstehen.", "reminder": "<PERSON><PERSON>, dass die Spickzettel Ihren Lernprozess ergänzen und nicht ersetzen sollen. Sie sind ein praktisches Hilfsmittel, um schnell auf wichtige Informationen zuzugreifen, aber es ist wichtig, das Thema zu vertiefen, um ein umfassendes Verständnis zu erlangen. Viel Spass beim Lernen!", "title1": "ChatGPT Spickzettel für Datenwissenschaft", "title2": "Spickzettel zur Qualität in IT-Projekten", "title3": "Spickzettel für den Lebenszyklus der Systementwicklung", "title4": "Spickzettel für Softwareentwurfsmuster und -methodik", "pdfUrl1": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-fa562cce-3952-428c-90ca-309625fa6648-ChatGPT_Cheat_Sheet_compressed.pdf&embedded=true", "pdfUrl2": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-decccfb8-fd60-4d4b-bdcf-99aa7d34d12f-quality-in-it-projects-3.pdf&embedded=true", "pdfUrl3": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-030102b8-6543-4af1-bdec-632e84e608c2-systems-development-life-cycle-3.pdf&embedded=true", "pdfUrl4": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-61d4b877-f07a-4abb-b6bc-0038eba22f58-sdm7306_software-design-patterns-and-methodology_1-2.pdf&embedded=true"}, "chatgpt": {"title": "ChatGPT-Spickzettel", "description": "Spick<PERSON><PERSON>l sind komprimierte, informative Dokumente, die als schnelle Referenz dienen und Sie durch ein bestimmtes Thema oder eine bestimmte Fähigkeit führen. <PERSON>e sollen I<PERSON> helfen, die wichtigsten Konzepte, <PERSON>eln, <PERSON><PERSON><PERSON><PERSON> oder Schritte zu einem bestimmten Thema zu verstehen.", "reminder": "<PERSON><PERSON>, dass die Spickzettel Ihren Lernprozess ergänzen und nicht ersetzen sollen. Sie sind ein praktisches Hilfsmittel, um schnell auf wichtige Informationen zuzugreifen, aber es ist wichtig, das Thema zu vertiefen, um ein umfassendes Verständnis zu erlangen. Viel Spass beim Lernen!", "pdfUrl": "https://aibs-content.s3.eu-central-2.amazonaws.com/CheatSheet_Six+best+prompting+practices+for+better+ChatGPT+results_DE_Vf.pdf"}, "dalle": {"title": "DALL·E Prompt sheet", "description": "Prompt Sheets sind komprimierte, informative Dokumente, die als schnelle Referenz dienen und Sie durch ein bestimmtes Thema oder eine bestimmte Fähigkeit führen. <PERSON>e sollen Ihnen helfen, die wichtigsten Konzepte, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> oder Schritte zu einem bestimmten Thema zu verstehen.", "reminder": "<PERSON><PERSON>, dass die Aufgabenblätter Ihren Lernprozess ergänzen und nicht vollständig ersetzen sollen. Sie sind ein praktisches Hilfsmittel, um schnell an wichtige Informationen heranzukommen, aber für ein umfassendes Verständnis ist es wichtig, das Thema zu vertiefen. Viel Spass beim Lernen!", "pdfUrl": "https://aibscontentstorage.blob.core.windows.net/content-files/Common/Platform_PDFs/DALLE_Prompting_cheat_sheet_EN.pdf"}}, "videoPlayer": {"playbackSpeed": "Wiedergabegeschwindigkeit", "fullscreen": "Vollbild", "exitFullscreen": "<PERSON><PERSON><PERSON><PERSON> beenden", "videoNotFound": "Video nicht gefunden", "videoNotPlayable": "Dieses Video kann derzeit nicht abgespielt werden. Alternativ können Sie das Video auf YouTube ansehen.", "watchOnYoutube": "<PERSON><PERSON> <PERSON> an<PERSON>", "videoError": "<PERSON><PERSON> des Videos ist ein Fehler aufgetreten. Erneuter Versuch...", "videoUnavailable": "Videoinhalt ist derzeit nicht verfügbar.", "disableSubtitles": "Deaktivieren", "subtitles": "Untertitel"}, "support": {"form": {"errors": {"unavailable": "Supportformular nicht gefunden. Bitte versuchen Sie es später erneut.", "requiredFields": "Bitte füllen Si<PERSON> alle erforderlichen Felder aus", "submission": "Bei der Übermittlung Ihrer Anfrage ist ein Fehler aufgetreten", "submit": "Bei der Übermittlung Ihrer Anfrage ist ein Fehler aufgetreten"}, "success": {"submitted": "Ihre Anfrage wurde erfolgreich übermittelt"}, "defaultTitle": "Support-An<PERSON><PERSON>", "defaultDescription": "Füllen Sie das Formular aus, um eine Support-Anfrage einzureichen"}}, "course": {"nav": {"programDetails": "Programmdetails", "outcomes": "Lernergebnisse", "programContent": "Programminhalt", "certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "actions": {"start": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON>", "completed": "Abgeschlossen", "tryAgain": "<PERSON><PERSON><PERSON> versuchen"}, "headers": {"skillsGain": "Fähigkeiten, die Si<PERSON> erwerben werden", "detailsToKnow": "Wissenswerte Details", "instructors": "<PERSON><PERSON><PERSON><PERSON>", "outcomes": "Lernergebnisse", "programContent": "Programminhalt"}, "complete": "Abgeschlossen", "topics": "Themen", "defaultTitle": "<PERSON><PERSON><PERSON>", "contentTypes": {"playground": "Playground", "videoGeneration": "Videogenerierung", "generation": "{{type}}-Gene<PERSON>ung", "pdf": "PDF", "imageGeneration": "Bildgenerierung", "form": "Formular", "text": "Text", "video": "Video"}, "navigation": {"next": "<PERSON><PERSON>", "previous": "Zurück", "completeCourse": "KURS ABSCHLIESSEN", "tooltips": {"watchVideo": "<PERSON><PERSON> müssen das Video an<PERSON>hen, um diesen Abschnitt abzuschließen.", "completeQuiz": "<PERSON>e müssen das Quiz abschließen, um fortzufahren.", "interactContent": "<PERSON><PERSON> müssen mit dem Inhalt interagieren, um fortzufahren.", "completeAllTopics": "<PERSON>e müssen alle Themen abschließen", "passQuiz": "Sie müssen mindestens 70% im Quiz erreichen, um fortzufahren", "completeAllContent": "<PERSON><PERSON> müssen alle Module a<PERSON>, bevor Si<PERSON> weiter können."}}, "details": {"duration": {"title": "<PERSON><PERSON>", "subtitle": "Insgesamt {{duration}}, flexibles, selbstbestimmtes Lernen"}, "certificate": {"title": "Abschlusszertifikat", "subtitle": "Erhalten Sie ein Abschlusszertifikat und fügen Sie es zu LinkedIn hinzu"}, "aiPoints": {"title": "AI-Punkte", "subtitle": "Schließen Sie den Kurs ab, um 5 AI-Punkte zu erhalten"}}, "certificate": {"title": "Verdienen Si<PERSON> ein teilbares Karrierezertifikat", "benefits": {"linkedIn": "Fügen Sie diese Bescheinigung zu Ihrem LinkedIn-Profil, Lebenslauf oder Lebenslauf hinzu", "socialMedia": "Teilen Sie es in sozialen Medien und in Ihrer Leistungsüberprüfung", "expertise": "Werden Sie für Ihre Expertise in Generative AI anerkannt"}, "certifiedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von"}, "completionModal": {"title": "Glückwunsch! 🎉", "congratsTitle": "Glückwunsch! 🎉", "content": "Glückwunsch zum Abschluss des Kurses! 🎉", "viewCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "downloadCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shareOnLinkedIn": "Teilen auf LinkedIn", "continueJourney": "<PERSON><PERSON> nä<PERSON> Journey", "backToHome": "<PERSON><PERSON><PERSON> zu Startseite", "close": "Schließen"}, "defaultDescription": "Dieser Kurs bietet eine eingehende Erkundung von Generative AI und ChatGPT, mit Fokus auf leistungsstarke Techniken, Tools und Anwendungen. Sie gewinnen praktische Erfahrung durch Übungen mit ChatGPT und DALL·E, während Sie auch die Einschränkungen und zukünftigen Auswirkungen von GenAI verstehen. Der Kurs schließt mit einer Bewertung und Zertifizierung ab, zusammen mit Einblicken in die AI-Adoption und Produktivität in verschiedenen Branchen. Diese Reise wird Sie ausstatten, um das Potenzial von GenAI in realen Szenarien zu nutzen.", "errors": {"loading": "Beim Laden der Kursdetails ist ein Fehler aufgetreten", "noContent": "Dieser Kurs hat noch keinen Inhalt", "noTopic": "Dieser Kurs hat noch keine Themen"}}, "courseContent": {"pdfPageOf": "Seite {{pageNum}} / {{numPages}}", "usecaseSlugNotFound": "Anwendungsfall-Slug nicht gefunden", "errorLoadingUsecase": "Fehler beim Laden des Anwendungsfalls:", "noUsecaseDataFor": "<PERSON><PERSON>falldaten verfügbar für", "playgroundNotSupported": "Playground-<PERSON><PERSON> wird nicht unterstützt", "videoUrlNotFound": "Video-URL nicht gefunden", "formSubmittedSuccess": "Ihr Formular wurde erfolgreich übermittelt.", "interactWithContent": "Bitte interagieren Sie mit dem Inhalt, bevor Si<PERSON> fortfahren.", "watchVideoFirst": "Bitte schauen Sie mindestens 90% des Videos an.", "completeQuizFirst": "Bitte schließen Sie das Quiz ab.", "progressSaving": "<PERSON>hr Fortschritt wird ges<PERSON>, bitte warten <PERSON>...", "contentCompleted": "Inhalt abgeschlossen!", "errorSavingProgress": "Beim Speichern Ihres Fortschritts ist ein Fehler aufgetreten!", "dataConflict": "Datenkonflikt aufgetreten. Bitte warten Sie einige Sekunden und versuchen Sie es erneut.", "contentCompletedSuccess": "Ihr Inhalt ist abgeschlossen!", "topicCompleted": "Thema erfolgreich abgeschlossen!", "formSubmitted": "Formular erfolgreich übermittelt!", "quizPassed": "Test erfolgreich abgeschlossen!", "quizFailed": "Sie haben das Quiz nicht bestanden. Versuchen Sie es erneut!", "usecaseCompleted": "Anwendungsfall erfolgreich abgeschlossen!", "interactiveCompleted": "Interaktive Übung erfolgreich abgeschlossen!", "videoCompleted": "Video erfolgreich abgeschlossen!", "pleaseWait": "<PERSON>te warten Si<PERSON>, während wir Ihren Fortschritt aktualisieren...", "courseReadyToComplete": "Kurs bereit zum Abschließen! Klicken Sie auf die Schaltfläche 'Kurs abschließen'.", "chapter": "<PERSON><PERSON><PERSON>", "topics": "<PERSON>a", "notFound": "Inhalt nicht gefunden", "pdf": {"errorLoading": "Fehler beim Laden des PDF", "openInNewTab": "In neuem Tab <PERSON>", "notFound": "PDF nicht gefunden", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "playground": {"completed": "ChatGPT Playground erfolgreich abgeschlossen!"}, "noOptionsFound": "Keine Optionen gefunden", "errorCompletingContent": "Fehler beim Abschließen des Inhalts!", "fileUpload": {"clickToUpload": "<PERSON><PERSON><PERSON>, um Date<PERSON> ho<PERSON>zuladen", "supportedFormats": "Unterstützte Formate: JPG, PNG, PDF, DOC, TXT, CSV", "maxSize": "Maximale Dateigröße: 10MB", "uploadedFiles": "Hochgeladene Dateien", "required": "Datei-Upload ist erforderlich", "fileTooLarge": "Datei ist zu groß", "fileTypeNotAllowed": "<PERSON><PERSON><PERSON> nicht erlaubt"}, "fileTooLarge": "Datei ist zu groß", "fileTypeNotAllowed": "<PERSON><PERSON><PERSON> nicht erlaubt", "certificate": {"generated": "Zertifikat erfolgreich generiert!", "defaultTitle": "Kurszertifikat", "defaultInstructor": "<PERSON><PERSON><PERSON><PERSON>", "congratulations": "Herzlichen Glückwunsch! 🎉", "completionMessage": "Herzlichen Glückwunsch zum Abschluss des Kurses! 🎉 {{action}} Sie Ihr Zertifikat, laden Sie es herunter oder teilen Sie Ihren Erfolg auf LinkedIn.", "view": "<PERSON><PERSON><PERSON>", "get": "<PERSON><PERSON>", "generating": "Wird generiert...", "viewCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "generateCertificate": "Zertifikat generieren", "yourCertificate": "<PERSON><PERSON> Zert<PERSON>", "backToHome": "<PERSON><PERSON><PERSON> zu Startseite", "share": "Teilen", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shareOnLinkedIn": "Auf LinkedIn teilen", "downloadCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificatePreview": "Zertifikatsvorschau"}, "errors": {"userInfoMissing": "Benutzerinformationen fehlen. Bitte versuchen Sie es später erneut.", "certificateGeneration": "Zertifikat konnte nicht generiert werden. Bitte versuchen Sie es erneut."}}, "quiz": {"title": "Quiz", "notFound": "Quiz nicht gefunden.", "enterYourAnswer": "G<PERSON>en Sie Ihre Antwort ein", "correctAnswer": "Richtige Antwort", "finish": "Quiz beenden", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "congratulationsPassed": "Herzlichen Glückwunsch! Sie haben das Quiz bestanden", "sorryFailed": "<PERSON>s tut uns leid, <PERSON><PERSON> haben das Quiz nicht bestanden", "pleaseTryAgain": "Bitte versuchen Sie es erneut.", "thankYouForSubmitting": "Vielen Dank für das Ausfüllen des Quiz. Bitte überprüfen Sie Ihre Antworten, bevor <PERSON> mit dem nächsten Schritt fortfahren.", "dragItemsInstructions": "Ziehen Sie Elemente aus den unten stehenden Optionen und legen Sie sie auf die entsprechenden Ablagebereiche.", "availableOptionsToMatch": "Verfügbare Optionen zum Abgleichen:", "submittedResultsDisplayed": "Quiz abgeschickt. Die Ergebnisse werden unten angezeigt.", "allOptionsMatched": "Alle Optionen wurden zugeordnet. Sie können sie bei Bedarf neu anordnen.", "matchItemsInstructions": "Ordnen Sie Elemente ihren richtigen Beschreibungen zu:", "someMatchesIncorrect": "Einige Zuordnungen sind falsch oder fehlen.", "allMatchesCorrect": "Alle Z<PERSON>rdnungen sind korrekt!", "unknownQuestionType": "Unbekannter Fragetyp", "correctMatch": "<PERSON><PERSON><PERSON>", "dropAnswerHere": "Antwort hier ablegen", "dragTip": "Tipp: <PERSON><PERSON><PERSON>e Optionen von oben und legen Sie sie in die passenden Ablagebereiche neben jeder Beschreibung.", "errors": {"failedToRetrieveData": "Quiz-<PERSON>n konnten nicht abgerufen werden.", "errorLoading": "<PERSON><PERSON> beim Laden des Quiz", "errorOccurredWhileLoading": "<PERSON><PERSON> Quiz ist ein Fehler aufgetreten:", "noValidDataFound": "<PERSON><PERSON> gültigen Quiz-Daten gefunden.", "completionError": "<PERSON><PERSON>chließen des Quiz ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."}}, "workflows": {"title": "Berufsspezifische AI Workflows entdecken ({{role}})", "discover": "Entdecken Sie unsere AI-gestützten Workflows", "subtitle": "Persönliche Auswahl für Sie", "showAll": "Alle Workflows anzeigen", "run": {"editApp": "App bearbeiten", "restartApp": "<PERSON><PERSON>n", "errorLoading": "Fehler beim Laden des Workflows"}}, "maintenance": {"title": "System wird gewartet", "subtitle": "Wir führen derzeit planmäßige Wartungsarbeiten durch, um Ihre Erfahrung zu verbessern.", "description": "Unser System ist vorübergehend nicht verfügbar, während wir wichtige Updates durchführen. Wir entschuldigen uns für eventuelle Unannehmlichkeiten und danken Ihnen für Ihre Geduld.", "logout": "Abmelden", "contact": "<PERSON>i dringenden Fragen wenden Sie sich bitte an unser Support-Team."}}