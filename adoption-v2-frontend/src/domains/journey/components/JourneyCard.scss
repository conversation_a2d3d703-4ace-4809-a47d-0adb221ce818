@use '../../../styles/abstracts/variables' as *;

// Journey Level Up Modal styling
.journey-level-up-modal-content {
  padding: 0px 24px !important;
  
  p {
    margin: 12px 0;
    line-height: 1.6;
    font-size: 16px;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      font-weight: 600;
    }
  }
}

// Continue button styling
.continue-button {
  background-color: $primary-color !important;
  color: white !important;
  text-transform: inherit !important;
  padding: 6px 16px !important;
  font-weight: 500 !important;
  
  &:hover {
    background-color: darken($primary-color, 10%) !important;
  }
}

.carousel-card {
  border-radius: $border-radius-md;
  height: 100%;
  display: flex;
  box-shadow: $shadow-sm;
  flex-direction: column;
  overflow: hidden;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF;
  border: 1px solid $divider-color;
  transition: all 0.3s ease;

  &.completed {
    opacity: 0.8;
    border-color: $success-color;

    .card-status {
      .completed-badge {
        display: flex;
        align-items: center;
        color: $success-color;
        font-weight: 500;
        
        .MuiSvgIcon-root {
          font-size: 20px;
          margin-right: $spacing-1;
        }
      }
    }

    .journey-card-content {
      .card-button {
        background-color: $success-color;
        color: $bg-paper;

        &:hover {
          background-color: $success-color-dark;
        }
      }
    }

    &:hover {
      opacity: 1;
    }
  }

  &.in-progress {
    border-color: $primary-color;
  }

  &.locked {
    .card-title {
      color: $text-secondary !important;
    }
  }

  .card-status {
    padding: $spacing-4 $spacing-4 0 $spacing-4;
    margin-bottom: $spacing-2;
    .status-badge {
      display: flex;
      align-items: center;
      gap: $spacing-1;
      color: $primary-color;
      
      .MuiSvgIcon-root {
        font-size: 20px;
      }

      span {
        font-size: 13px;
        font-weight: 400;
      }

      &.locked {
        color: $text-secondary;
      }

      &.completed {
        color: $success-color;
      }

      &.in-progress {
        color: $primary-color;

        .MuiSvgIcon-root {
          animation: spin 2s linear infinite;
        }
      }
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    &.journey-card-content {
      padding: $spacing-2 $spacing-4 $spacing-4 $spacing-4 !important;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: $text-primary;
      margin: 0 0 $spacing-1 0;
      line-height: 1.4;
    }

    .card-description {
      font-size: 14px;
      color: $text-secondary;
      line-height: 1.6;
      margin: 0 0 $spacing-4 0;
      flex: 1;
    }
    

    .card-image {
      width: 100%;
      height: 124px;
      object-fit: cover;
      margin-top: $spacing-3;
      margin-bottom: $spacing-3;
      border-radius: $border-radius-sm;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 124px;
        object-fit: cover;
        display: block;
      }
    }
  }

  @media (max-width: 768px) {
    .card-status {
      padding: $spacing-4 $spacing-4 $spacing-3 $spacing-4;
    }
    .card-content {

      .card-title {
        font-size: 16px;
      }

      .card-description {
        font-size: 13px;
        margin-bottom: $spacing-2;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}