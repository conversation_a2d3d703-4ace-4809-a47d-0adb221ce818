import { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import '../styles/UsecaseCard.scss';
import '../styles/TextUsecase.scss';
import WelcomeHeader from '@components/WelcomeHeader/WelcomeHeader.jsx';
import useFormRender from '../../hooks/useFormRender.jsx';
import {
  Container,
  Grid,
  Card,
  CardHeader,
  CardContent,
  CardActions,
  Typography,
  Button,
  CircularProgress,
  Stack,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  Box,
  MenuItem,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import CleaningServicesIcon from '@mui/icons-material/CleaningServices';
import CompressIcon from '@mui/icons-material/Compress';
import ExpandIcon from '@mui/icons-material/Expand';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTranslation } from 'react-i18next';
import FavoriteButton from '@components/FavoriteButton';
import { useUseAzureOpenAIConnectorsMutation } from '../../../../redux/services/connectors-api';
import { useUpdateAppTrackingMutation } from '../../../../redux/services/app-tracking-api';
import { useSelector } from 'react-redux';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { useLocation } from 'react-router-dom';
import useUpdateJourneyTracking from '../../../journey/utils/updateJourneyTracking';
import HtmlRenderer from '@components/HtmlRenderer';

const TextUsecase = ({
  data,
  onGenerate,
  id,
  onlyShowUsecase = false,
  disableFavorites = false,
  disableTracking = false,
  type = 'usecase',
  hideTitle = false,
}) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const user = useSelector((state) => state.auth.user);
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState('');
  const [displayedResponse, setDisplayedResponse] = useState('');
  const [contentQueue, setContentQueue] = useState([]);
  const [typingSpeed] = useState(10);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const abortControllerRef = useRef(null);
  const typingTimerRef = useRef(null);
  const [copied, setCopied] = useState(false);
  const [modifyAnchorEl, setModifyAnchorEl] = useState(null);
  const [generateText] = useUseAzureOpenAIConnectorsMutation();
  const [updateAppTracking] = useUpdateAppTrackingMutation();
  const [hasCalledGenerate, setHasCalledGenerate] = useState(false);
  const [lastGenerateTime, setLastGenerateTime] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const formRef = useRef(null);
  const outputContentRef = useRef(null);
  const [showExpandButton, setShowExpandButton] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const lastScrollPositionRef = useRef(0);
  const outputCardRef = useRef(null);

  // Create a ref to store the previous usecase ID
  const prevUsecaseIdRef = useRef(id);

  // Get journey parameters from URL state
  const journeyCardId = location.state?.journeyCardId;
  const journeyTrackingData = location.state?.journeyTrackingData;
  const journeyLevel = location.state?.journeyLevel;

  // Get resetState flag sent by TrainingProgress
  const resetState = location.state?.resetState === true;

  const defaultDescription =
    '<p>Create a clear and concise meeting agenda based on the meeting title, key topics, participants, and time duration.</p><p>Provide an organized structure for meetings, helping participants stay focused on the relevant topics within the allotted time.</p>';

  const { formData, renderFormField, formFields, getLocalizedData } = useFormRender(
    data,
    i18n,
    defaultDescription
  );

  // Function to completely reset state
  const resetAllState = useCallback(() => {
    // Stop typing effect
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    // Cancel API request if exists
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Clear all states
    setIsLoading(false);
    setResponse('');
    setDisplayedResponse('');
    setContentQueue([]);
    setHasGenerated(false);
    setSeconds(0);
    setIsTyping(false);
    setHasCalledGenerate(false);
    setLastGenerateTime(0);

    // Clear useCaseReset flag from SessionStorage
    sessionStorage.removeItem('useCaseReset');
  }, []);

  // Reset state with resetState flag from TrainingProgress or useCaseReset flag
  useEffect(() => {
    // Check if resetState flag came
    if (resetState) {
      resetAllState();
    }

    // Check useCaseReset flag from sessionStorage
    const useCaseReset = sessionStorage.getItem('useCaseReset') === 'true';
    if (useCaseReset) {
      resetAllState();
    }
  }, [resetState, resetAllState]);

  // Reset state when usecase ID changes
  useEffect(() => {
    if (prevUsecaseIdRef.current !== id) {
      resetAllState();
      prevUsecaseIdRef.current = id;
    }
  }, [id, resetAllState]);

  useEffect(() => {
    let interval;
    if (isLoading || isTyping) {
      interval = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      setSeconds(0);
    }
    return () => clearInterval(interval);
  }, [isLoading, isTyping]);

  const formatTime = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const replacePlaceholders = (prompt, variables) => {
    let result = prompt;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      result = result.replaceAll(placeholder, value || '');
    });
    return result;
  };

  const handleCancel = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }
    setIsLoading(false);
    setIsTyping(false);
    setResponse('');
    setDisplayedResponse('');
    setContentQueue([]);
    setHasGenerated(false);
    setSeconds(0);
  };

  useEffect(() => {
    if (!response) {
      setHasCalledGenerate(false);
    }
  }, [response]);

  const handleOnGenerate = useCallback(async () => {
    const now = Date.now();
    if (now - lastGenerateTime < 2000 || hasCalledGenerate) {
      return;
    }

    setLastGenerateTime(now);
    setHasCalledGenerate(true);

    if (onGenerate) {
      try {
        await onGenerate(true);
      } catch (_error) {
        // Silently pass the error
      }
    }
  }, [onGenerate, hasCalledGenerate, lastGenerateTime]);

  useEffect(() => {
    if (hasGenerated && onGenerate) {
      handleOnGenerate();
    }
  }, [hasGenerated, onGenerate, handleOnGenerate]);

  const handleButtonClick = async () => {
    try {
      const localizedData = getLocalizedData();

      // Simplified form fields validation
      const formFieldsToCheck = formFields;
      const missingFields = formFieldsToCheck.filter((field) => !formData[field.name]);

      if (missingFields.length > 0) {
        setResponse('Please fill in all required fields.');
        return;
      }

      // Make buttons visible as soon as Generate starts
      setHasGenerated(true);

      setHasCalledGenerate(false);
      setLastGenerateTime(0);

      // -------- MOVING TRACKING OPERATIONS HERE --------
      // Perform journey tracking update
      if (journeyCardId && journeyTrackingData && journeyLevel && user?._id && !disableTracking) {
        try {
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: journeyTrackingData,
            userLevel: journeyLevel.toLowerCase(),
            cardId: journeyCardId,
          });

          // Send notification via localStorage for TrainingProgress
          localStorage.setItem('journey_tracking_updated', Date.now().toString());

          // Also update TrainingProgress next button content
          // Dispatch a custom event for Footer to listen
          window.dispatchEvent(
            new CustomEvent('update_training_progress', {
              detail: {
                source: 'usecase',
                cardId: journeyCardId,
                trackingData: true,
                timestamp: Date.now(),
              },
            })
          );

          // Add a permanent marker
          localStorage.setItem('tracking_completed_card', journeyCardId);
        } catch (_error) {
          // Tracking error will not be handled
        }
      }

      // Perform app tracking update
      try {
        if (!disableTracking) {
          const currentDate = new Date();
          await updateAppTracking({
            userId: user?._id,
            appId: id,
            appType: 'usecase',
            year: currentDate.getFullYear(),
            month: currentDate.getMonth() + 1,
            day: currentDate.getDate(),
          });
        }
      } catch (_error) {
        // Tracking error will not be handled
      }
      // -------- END OF TRACKING OPERATIONS --------

      abortControllerRef.current = new AbortController();

      // Clear timer for typing effect
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
        typingTimerRef.current = null;
      }

      // Clear output area
      setDisplayedResponse('');
      setContentQueue([]);

      setIsLoading(true);
      setResponse('');
      setSeconds(0);

      let processedPrompt;
      if (data.api_type === 'completions') {
        processedPrompt = replacePlaceholders(
          localizedData.completions_settings?.completions_prompt ||
            data.completions_settings.completions_prompt ||
            '',
          formData
        );
      } else if (data.api_type === 'stream') {
        processedPrompt = replacePlaceholders(
          localizedData.stream_settings?.stream_prompt || data.stream_settings.stream_prompt || '',
          formData
        );
      } else {
        throw new Error('Invalid API type');
      }

      let isFirstResponse = true;

      const result = await generateText({
        prompt: processedPrompt,
        temperature: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.temperature
            : data.stream_settings.temperature
        ),
        frequency: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.frequency_penalty
            : data.stream_settings.frequency_penalty
        ),
        presence: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.presence_penalty
            : data.stream_settings.presence_penalty
        ),
        stream: true,
        html: true,
        abortController: abortControllerRef.current,
        onUpdate: async (streamContent) => {
          try {
            if (!streamContent || streamContent.trim() === '') return;

            if (typeof streamContent === 'string' && streamContent.startsWith('data: ')) {
              const jsonData = streamContent.slice(6).trim();

              if (jsonData === '[DONE]') {
                setIsTyping(false);
                return;
              }

              try {
                const parsed = JSON.parse(jsonData);
                if (parsed.content) {
                  const content = parsed.content;

                  if (content.includes('{"content":')) {
                    return;
                  }

                  setContentQueue((prev) => [...prev, content]);
                  setIsTyping(true);

                  if (isFirstResponse) {
                    isFirstResponse = false;
                    // Scroll to output on mobile devices
                    if (window.innerWidth <= 768 && outputCardRef.current) {
                      setTimeout(() => {
                        outputCardRef.current.scrollIntoView({
                          behavior: 'smooth',
                          block: 'start',
                        });
                      }, 100);
                    }
                  }
                }
              } catch (_parseError) {
                if (jsonData && jsonData.trim()) {
                  if (!jsonData.includes('{"content":')) {
                    setContentQueue((prev) => [...prev, jsonData.trim()]);
                    setIsTyping(true);
                    // Scroll to output on mobile devices (when first content arrives)
                    if (window.innerWidth <= 768 && outputCardRef.current && isFirstResponse) {
                      setTimeout(() => {
                        outputCardRef.current.scrollIntoView({
                          behavior: 'smooth',
                          block: 'start',
                        });
                      }, 100);
                    }
                  }
                }
              }
            }
          } catch (_error) {
            setIsTyping(false);
            setContentQueue((prev) => [
              ...prev,
              '\n\nError: ' + (_error?.message || 'Error processing response'),
            ]);
          }
        },
      });

      if (result.error) {
        if (
          result.error.data &&
          typeof result.error.data === 'string' &&
          result.error.data.includes('data: {')
        ) {
          setHasGenerated(true);
          return;
        }

        if (result.error.name === 'AbortError' || result.error.message === 'Response cancelled') {
          return;
        }

        throw new Error(
          typeof result.error.data === 'string'
            ? result.error.data
            : t('common.errors.textGenerationFailed')
        );
      }

      setHasGenerated(true);
    } catch (error) {
      setResponse(error.message || t('common.errors.textGenerationFailed'));
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const getModelName = () => {
    let modelName = 'GPT-4o';

    if (data.api_type === 'completions') {
      modelName = data.completions_settings?.model_selection || 'GPT-4o';
    } else if (data.api_type === 'stream') {
      modelName = data.stream_settings?.model_selection || 'GPT-4o';
    } else if (data.api_type === 'assistants') {
      modelName = data.assistants_settings?.model_selection || 'GPT-4o';
    }

    // Change model names containing GPT-4 to GPT-4o
    if (modelName.includes('GPT-4') || modelName.includes('gpt-4')) {
      modelName = 'GPT-4o';
    }

    return modelName
      .split(' ')
      .map((word) => {
        if (word.length <= 3) {
          return word.toUpperCase();
        } else {
          return word.substring(0, 3).toUpperCase() + word.slice(3);
        }
      })
      .join(' ');
  };

  const stripHtml = (html) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || '';
  };

  const handleCopy = async () => {
    try {
      // Try copying in both HTML and plain text formats
      const htmlContent = displayedResponse;
      const plainText = stripHtml(displayedResponse);

      // Copy using ClipboardItem for modern browsers
      if (navigator.clipboard?.write) {
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlContent], { type: 'text/html' }),
          'text/plain': new Blob([plainText], { type: 'text/plain' }),
        });

        await navigator.clipboard.write([clipboardItem]);
      } else {
        // Copy as plain text if ClipboardItem is not supported
        await navigator.clipboard.writeText(plainText);
      }

      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (_err) {
      // Copy error silently passed
    }
  };

  const handleClear = () => {
    // Terminate current generate operation
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Clear timer to stop typing effect
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    setIsLoading(false);
    setIsTyping(false);
    setResponse('');
    setDisplayedResponse('');
    setContentQueue([]);
    setHasGenerated(false);
  };

  const handleModifyClick = (event) => {
    setModifyAnchorEl(event.currentTarget);
  };

  const handleModifyClose = () => {
    setModifyAnchorEl(null);
  };

  const handleModifyOption = async (type) => {
    handleModifyClose();

    // Cancel current typing operation and generate process
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    setIsTyping(false);
    setContentQueue([]);

    const modificationPrompts = {
      shorter: 'Please make the following response shorter while keeping the main points:',
      longer: 'Please expand the following response with more details and examples:',
      simpler: 'Please simplify the following response to make it easier to understand:',
    };

    try {
      const localizedData = getLocalizedData();

      setIsLoading(true);
      setResponse('');
      setDisplayedResponse('');
      setContentQueue([]);
      setIsTyping(true);

      // Create a new AbortController
      abortControllerRef.current = new AbortController();

      // Get plain text by cleaning HTML content
      const plainTextContent = stripHtml(displayedResponse);

      // Create prompt from form data
      let basePrompt;
      if (data.api_type === 'completions') {
        basePrompt = replacePlaceholders(
          localizedData.completions_settings?.completions_prompt ||
            data.completions_settings.completions_prompt ||
            '',
          formData
        );
      } else if (data.api_type === 'stream') {
        basePrompt = replacePlaceholders(
          localizedData.stream_settings?.stream_prompt || data.stream_settings.stream_prompt || '',
          formData
        );
      } else {
        throw new Error('Invalid API type');
      }

      // Combine modification prompt with base prompt
      const combinedPrompt = `${basePrompt}\n\n${modificationPrompts[type]}\n\n${plainTextContent}`;

      const result = await generateText({
        _id: '6798d8e88cea31048c9f37ba',
        prompt: combinedPrompt,
        temperature: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.temperature
            : data.stream_settings.temperature
        ),
        frequency: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.frequency_penalty
            : data.stream_settings.frequency_penalty
        ),
        presence: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.presence_penalty
            : data.stream_settings.presence_penalty
        ),
        stream: true,
        html: true,
        model:
          data.api_type === 'completions'
            ? data.completions_settings.model_selection
            : data.stream_settings.model_selection,
        abortController: abortControllerRef.current,
        onUpdate: (streamContent) => {
          try {
            if (!streamContent || streamContent.trim() === '') return;

            if (typeof streamContent === 'string' && streamContent.startsWith('data: ')) {
              const jsonData = streamContent.slice(6).trim();

              if (jsonData === '[DONE]') {
                setIsTyping(false);
                return;
              }

              try {
                const parsed = JSON.parse(jsonData);
                if (parsed.content) {
                  const content = parsed.content;

                  if (content.includes('{"content":')) {
                    return;
                  }

                  setContentQueue((prev) => [...prev, content]);
                  setIsTyping(true);
                }
              } catch (_parseError) {
                if (jsonData && jsonData.trim()) {
                  if (!jsonData.includes('{"content":')) {
                    setContentQueue((prev) => [...prev, jsonData.trim()]);
                    setIsTyping(true);
                  }
                }
              }
            }
          } catch (_error) {
            // Error silently handled
          }
        },
      });

      if (result.error) {
        if (result.error.name === 'AbortError' || result.error.message === 'Response cancelled') {
          return;
        }
        throw new Error(result.error.data || 'Failed to modify response');
      }

      setHasGenerated(true);
    } catch (error) {
      setResponse(error.message || 'Error modifying response. Please try again.');
    } finally {
      setIsLoading(false);
      setIsTyping(false);
      setHasCalledGenerate(false);
      setLastGenerateTime(0);
      if (onGenerate) {
        try {
          await onGenerate(true);
        } catch (_error) {
          // Silently pass the error
        }
      }
    }
  };

  useEffect(() => {
    if (formRef.current) {
      const formHeight = formRef.current.scrollHeight;
      setShowExpandButton(formHeight > 500);
    }
  }, [formFields]);

  const handleExpandClick = () => {
    setIsExpanded(!isExpanded);
  };

  useEffect(() => {
    const markdownElement = outputContentRef.current?.querySelector('.markdown-body');
    if (!markdownElement) return;

    let scrollTimer;

    const handleScroll = (e) => {
      // Check if event comes from user or programmatically
      if (e.isTrusted) {
        // When user scrolls
        setUserHasScrolled(true);

        // Save last scroll position
        lastScrollPositionRef.current = markdownElement.scrollTop;

        // Turn off automatic scroll
        setShouldAutoScroll(false);
      }

      // Check last scroll position
      const { scrollTop, scrollHeight, clientHeight } = markdownElement;
      const isAtBottom = scrollHeight - (scrollTop + clientHeight) < 20;

      // If user reaches the bottom, re-enable automatic scroll
      if (isAtBottom) {
        setShouldAutoScroll(true);
        setUserHasScrolled(false);
      }

      // Clear scroll timer and restart
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(() => {
        // This timer is only for tracking when scroll event ends
      }, 200);
    };

    markdownElement.addEventListener('scroll', handleScroll);

    // Cleanup function
    return () => {
      markdownElement.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimer);
    };
  }, [contentQueue.length]);

  useEffect(() => {
    if (contentQueue.length > 0 && (contentQueue.length > 5 || isTyping === false)) {
      const timer = setTimeout(() => {
        if (contentQueue.length > 0) {
          const nextContent = contentQueue[0];

          // Update content
          setDisplayedResponse((prev) => prev + nextContent);
          setContentQueue((prev) => prev.slice(1));

          // Manage scroll operation
          const markdownElement = outputContentRef.current?.querySelector('.markdown-body');

          if (markdownElement) {
            if (userHasScrolled) {
              // If user has scrolled, preserve their position
              requestAnimationFrame(() => {
                markdownElement.scrollTop = lastScrollPositionRef.current;
              });
            } else if (shouldAutoScroll && nextContent.length > 10) {
              // If user hasn't scrolled and auto scroll is on, scroll to bottom
              requestAnimationFrame(() => {
                markdownElement.scrollTop = markdownElement.scrollHeight;
              });
            }
          }
        }
      }, typingSpeed);

      // Save timer reference
      typingTimerRef.current = timer;

      return () => {
        clearTimeout(timer);
        typingTimerRef.current = null;
      };
    }
  }, [contentQueue, typingSpeed, shouldAutoScroll, isTyping, userHasScrolled]);

  return (
    <Box sx={{ padding: 0 }}>
      {hideTitle === false && (
        <WelcomeHeader
          isPageView={!onlyShowUsecase}
          title={getLocalizedData().title || data.title}
          description={getLocalizedData().description || data.content || defaultDescription}
          showProgress={false}
        />
      )}

      <Container className="text-usecase">
        <Grid container spacing={3}>
          <Grid item xs={12} md={5} className="fields-grid-item-no-margin">
            <Card
              className={`text-form-card text-form-card-container ${isExpanded ? 'expanded' : ''}`}
            >
              <CardHeader
                title={
                  <Box
                    className="card-title-wrapper"
                    sx={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Typography variant="h6" className="title">
                      {t('textUsecase.form.title')}
                    </Typography>
                    <Tooltip title={t('textUsecase.form.tooltip')} arrow placement="top">
                      <HelpOutlineIcon className="helper-icon" fontSize="small" />
                    </Tooltip>
                  </Box>
                }
                action={
                  <>
                    {!disableFavorites && (
                      <FavoriteButton
                        shortcutID={data._id}
                        shortcutType={type === 'ai_app' ? 'ai_app' : 'usecase'}
                        iconOnly={false}
                      />
                    )}
                  </>
                }
                className="card-header"
              />
              <CardContent className="card-content form-content scrollable-form-content">
                <form id={`usecase-field-form-${data._id}`} ref={formRef}>
                  {formFields.map((field) => renderFormField(field))}
                </form>
                {showExpandButton && (
                  <Box className="show-more-container">
                    <Button
                      onClick={handleExpandClick}
                      className="show-more-button"
                      endIcon={isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                    >
                      {isExpanded
                        ? t('common.showLess', 'Show Less')
                        : t('common.showMore', 'Show More')}
                    </Button>
                  </Box>
                )}
              </CardContent>
              <CardActions className="card-actions">
                <Typography variant="caption" className="model-info">
                  {t('textUsecase.form.poweredBy')}{' '}
                  <Box component="span" className="model-name">
                    {getModelName()}
                  </Box>
                </Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                  {(isLoading || isTyping) && (
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={handleCancel}
                      size="small"
                      className="cancel-button"
                      fullWidth={false}
                    >
                      {t('textUsecase.form.cancel')}
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    fullWidth
                    disabled={isLoading || isTyping}
                    onClick={handleButtonClick}
                    className="generate-button"
                    startIcon={
                      isLoading || isTyping ? <CircularProgress size={20} /> : <AutoAwesomeIcon />
                    }
                  >
                    {isLoading || isTyping
                      ? formatTime(seconds)
                      : hasGenerated
                        ? t('textUsecase.form.regenerate')
                        : t('textUsecase.form.generate')}
                  </Button>
                </Stack>
              </CardActions>
            </Card>
          </Grid>

          <Grid item xs={12} md={7} className="output-grid-item-no-margin">
            <Card className="text-output-card" ref={outputCardRef}>
              <CardHeader
                title={
                  <Typography variant="h6" className="title">
                    {t('textUsecase.output.title')}
                  </Typography>
                }
                action={
                  <Stack direction="row" spacing={1}>
                    <Tooltip title={t('textUsecase.output.modifyResponse')}>
                      <span>
                        <IconButton
                          onClick={handleModifyClick}
                          size="small"
                          className="action-button"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>

                    <Menu
                      anchorEl={modifyAnchorEl}
                      open={Boolean(modifyAnchorEl)}
                      onClose={handleModifyClose}
                      anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                      }}
                      transformOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center',
                      }}
                    >
                      <MenuItem onClick={() => handleModifyOption('shorter')}>
                        <ListItemIcon>
                          <CompressIcon fontSize="small" />
                        </ListItemIcon>
                        {t('textUsecase.output.shorter')}
                      </MenuItem>
                      <MenuItem onClick={() => handleModifyOption('longer')}>
                        <ListItemIcon>
                          <ExpandIcon fontSize="small" />
                        </ListItemIcon>
                        {t('textUsecase.output.longer')}
                      </MenuItem>
                      <MenuItem onClick={() => handleModifyOption('simpler')}>
                        <ListItemIcon>
                          <AutoAwesomeIcon fontSize="small" />
                        </ListItemIcon>
                        {t('textUsecase.output.simpler')}
                      </MenuItem>
                    </Menu>

                    <Tooltip
                      title={copied ? t('textUsecase.output.copied') : t('textUsecase.output.copy')}
                    >
                      <span>
                        <IconButton
                          onClick={handleCopy}
                          size="small"
                          className={`action-button ${!displayedResponse ? 'faded-button' : ''}`}
                          disabled={!displayedResponse}
                        >
                          {copied ? (
                            <CheckIcon fontSize="small" />
                          ) : (
                            <ContentCopyIcon fontSize="small" />
                          )}
                        </IconButton>
                      </span>
                    </Tooltip>

                    <Tooltip title={t('textUsecase.output.clear')}>
                      <span>
                        <IconButton
                          onClick={handleClear}
                          size="small"
                          className={`action-button ${!hasGenerated && !displayedResponse ? 'faded-button' : ''}`}
                          disabled={!hasGenerated && !displayedResponse}
                        >
                          <CleaningServicesIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </Stack>
                }
                className="card-header"
              />
              <CardContent className="card-content output-content" ref={outputContentRef}>
                {!displayedResponse && (
                  <Box className="text-output-empty-message">
                    <Box className="placeholder-content">
                      <InfoOutlinedIcon className="info-icon" />
                      <Typography variant="h6" className="placeholder-title">
                        AI use case
                      </Typography>
                      <Typography variant="body2" className="placeholder-subtitle">
                        {t('textUsecase.output.emptyDescription', {
                          count: formFields?.length || 0,
                          interpolation: { escapeValue: false },
                        })}
                      </Typography>

                      <Box className="placeholder-features">
                        <Box className="feature-item">
                          <AutoAwesomeIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature1')}
                          </Typography>
                        </Box>
                        <Box className="feature-item">
                          <EditIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature2')}
                          </Typography>
                        </Box>
                        <Box className="feature-item">
                          <ContentCopyIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature3')}
                          </Typography>
                        </Box>
                        <Box className="feature-item">
                          <CleaningServicesIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature4')}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                )}
                {displayedResponse && (
                  <>
                    <HtmlRenderer
                      content={displayedResponse}
                      className="markdown-body visible"
                      sanitize={true}
                      formatCodeBlocks={true}
                    />
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        <Box className="ai-disclaimer-box">{t('common.warnings.dataPrivacy')}</Box>
      </Container>
    </Box>
  );
};

TextUsecase.propTypes = {
  data: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    shortcutID: PropTypes.string,
    title: PropTypes.string.isRequired,
    content: PropTypes.string,
    usecase_icon_url: PropTypes.string,
    slug: PropTypes.string.isRequired,
    api_type: PropTypes.oneOf(['completions', 'stream', 'assistants']).isRequired,
    translations: PropTypes.objectOf(
      PropTypes.shape({
        title: PropTypes.string.isRequired,
        description: PropTypes.string,
        completions_settings: PropTypes.shape({
          form_fields: PropTypes.arrayOf(
            PropTypes.shape({
              _id: PropTypes.string,
              type: PropTypes.string.isRequired,
              label: PropTypes.string.isRequired,
              name: PropTypes.string.isRequired,
              default_value: PropTypes.string,
              choices: PropTypes.string,
            })
          ),
          completions_prompt: PropTypes.string,
          temperature: PropTypes.string,
          top_p: PropTypes.string,
          frequency_penalty: PropTypes.string,
          presence_penalty: PropTypes.string,
          model_selection: PropTypes.string,
          max_token: PropTypes.string,
        }),
        _id: PropTypes.string,
      })
    ),
    completions_settings: PropTypes.shape({
      form_fields: PropTypes.arrayOf(
        PropTypes.shape({
          _id: PropTypes.string.isRequired,
          type: PropTypes.string.isRequired,
          label: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          default_value: PropTypes.string,
          choices: PropTypes.string,
        })
      ),
      completions_prompt: PropTypes.string,
      temperature: PropTypes.string.isRequired,
      top_p: PropTypes.string.isRequired,
      frequency_penalty: PropTypes.string.isRequired,
      presence_penalty: PropTypes.string.isRequired,
      model_selection: PropTypes.string.isRequired,
      max_token: PropTypes.string.isRequired,
    }),
    stream_settings: PropTypes.shape({
      stream_form: PropTypes.shape({
        form_fields: PropTypes.arrayOf(
          PropTypes.shape({
            _id: PropTypes.string.isRequired,
            type: PropTypes.string.isRequired,
            label: PropTypes.string.isRequired,
            name: PropTypes.string.isRequired,
            default_value: PropTypes.string,
            choices: PropTypes.string,
          })
        ),
      }),
      stream_prompt: PropTypes.string,
      temperature: PropTypes.string.isRequired,
      top_p: PropTypes.string.isRequired,
      frequency_penalty: PropTypes.string.isRequired,
      presence_penalty: PropTypes.string.isRequired,
      model_selection: PropTypes.string.isRequired,
      max_token: PropTypes.string.isRequired,
    }),
    assistants_settings: PropTypes.shape({
      form_fields: PropTypes.arrayOf(
        PropTypes.shape({
          _id: PropTypes.string.isRequired,
          type: PropTypes.string.isRequired,
          label: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          default_value: PropTypes.string,
          choices: PropTypes.string,
        })
      ),
      assistants_prompt: PropTypes.string,
      temperature: PropTypes.string,
      model_selection: PropTypes.string,
    }),
  }).isRequired,
  onGenerate: PropTypes.func,
  id: PropTypes.string.isRequired,
  onlyShowUsecase: PropTypes.bool,
  disableFavorites: PropTypes.bool,
  disableTracking: PropTypes.bool,
  type: PropTypes.string,
  hideTitle: PropTypes.bool,
};

export default TextUsecase;
