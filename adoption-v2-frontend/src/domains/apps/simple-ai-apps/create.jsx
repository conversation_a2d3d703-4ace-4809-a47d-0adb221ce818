import React, { useState, useRef, useEffect } from 'react';
import { Container, Grid, Typography, Box } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useTranslation } from 'react-i18next';
// import { driver } from 'driver.js';
// import 'driver.js/dist/driver.css';

import Iconify from '../../../components/Iconify/iconify';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader';
import TextUsecase from '../components/usecases/TextUsecase';
import AppDescription from './components/AppDescription/AppDescription';
import FormElements from './components/FormElements/FormElements';
import InputTypes from './components/InputTypes/InputTypes';
import PromptEditor from './components/PromptEditor/PromptEditor';
import AppSettings from './components/AppSettings/AppSettings';
import NavigationButtons from './components/NavigationButtons/NavigationButtons';
import EditInputs from './components/EditInputs/EditInputs';
import ReviewButtons from './components/ReviewButtons/ReviewButtons';
import { useCreateSimpleAppMutation } from '../../../redux/services/CreateApp-api';
import { useAddShortcutMutation } from '../../../redux/services/shortcuts-api';
import { useGetJourneyTrackingQuery } from '../../../redux/services/journey-api';
import useUpdateJourneyTracking from '../../../domains/journey/utils/updateJourneyTracking';
import { developmentLogs } from '../../../utils/developmentLogs';

function CreateSimpleAIApp() {
  const { t } = useTranslation();
  const [appName, setAppName] = useState('');
  const [elements, setElements] = useState([]);
  const maxLength = 50;
  const elementLimit = 5;
  const [currentStep, setCurrentStep] = useState(0);
  const editorRef = useRef(null);
  const [promptContent, setPromptContent] = useState('');
  const maxPromptLength = 2048;
  const [temperature, setTemperature] = useState(1);
  const [topP, setTopP] = useState(1);
  const [frequencyPenalty, setFrequencyPenalty] = useState(0);
  const [presencePenalty, setPresencePenalty] = useState(0);
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const [focusedElementId, setFocusedElementId] = useState(null);
  const [description, setDescription] = useState('');
  const [isInputTypesBouncing, setIsInputTypesBouncing] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  // const [showTour, setShowTour] = useState(false);
  // const driverRef = useRef(null);
  const [createSimpleApp, { isLoading, isSuccess, isError, error, data }] =
    useCreateSimpleAppMutation();
  const [addShortcut] = useAddShortcutMutation();

  // Get journey card id from state
  const journeyCardId = location.state?.cardId;

  // Get user data from Redux store
  const { user } = useSelector((state) => state.auth);

  // Get hook and data for updating journey tracking
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const { data: trackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  const steps = ['Create Form', 'Prompt writing', 'Review & Create'];

  const handleNameChange = (event) => {
    const value = event.value || event.target.value;
    if (value.length <= maxLength) {
      setAppName(value);
    }
  };

  // Utility function for mobile device control
  const isMobile = () => window.innerWidth <= 768;

  const handleAddElement = (type) => {
    if (elements.length < elementLimit) {
      const newElementId = Date.now();
      setElements([
        ...elements,
        {
          id: newElementId,
          type: type.name,
          inputName: '',
          defaultText: '',
          options: type.name === 'Dropdown' ? [{ id: Date.now(), value: '' }] : [],
        },
      ]);

      // Scroll to newly added element on mobile
      if (isMobile()) {
        setTimeout(() => {
          const newElement = document.querySelector(`[data-element-id="${newElementId}"]`);
          if (newElement) {
            newElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      }
    }
  };

  const handleAddOption = (elementId) => {
    const newOptionId = Date.now();
    setElements(
      elements.map((element) =>
        element.id === elementId
          ? {
              ...element,
              options: [...element.options, { id: newOptionId, value: '' }],
            }
          : element
      )
    );

    // Scroll to newly added option on mobile
    if (isMobile()) {
      setTimeout(() => {
        const elementContainer = document.querySelector(`[data-element-id="${elementId}"]`);
        if (elementContainer) {
          elementContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  };

  const handleRemoveElement = (elementId) => {
    setElements(elements.filter((element) => element.id !== elementId));

    const elementToRemove = elements.find((el) => el.id === elementId);
    if (elementToRemove && editorRef.current) {
      const content = editorRef.current.getContent();
      // Regex for both strong tagged and normal versions
      const regex = new RegExp(
        `<strong>\\[${elementToRemove.inputName}\\]</strong>|\\[${elementToRemove.inputName}\\]`,
        'g'
      );
      const updatedContent = content.replace(regex, '');
      editorRef.current.setContent(updatedContent);
      setPromptContent(updatedContent);
    }
  };

  const handleElementChange = (elementId, field, value) => {
    setElements(
      elements.map((element) =>
        element.id === elementId ? { ...element, [field]: value } : element
      )
    );
  };

  const handleRemoveOption = (elementId, optionId) => {
    setElements(
      elements.map((element) =>
        element.id === elementId
          ? {
              ...element,
              options: element.options.filter((option) => option.id !== optionId),
            }
          : element
      )
    );
  };

  const handleOptionChange = (elementId, optionId, value) => {
    setElements(
      elements.map((element) =>
        element.id === elementId
          ? {
              ...element,
              options: element.options.map((option) =>
                option.id === optionId ? { ...option, value } : option
              ),
            }
          : element
      )
    );
  };

  const inputTypes = [
    { name: 'Short Text', icon: <Iconify icon="mingcute:text-fill" />, disabled: false },
    { name: 'Paragraph', icon: <Iconify icon="hugeicons:paragraph" />, disabled: false },
    { name: 'Dropdown', icon: <Iconify icon="ri:dropdown-list" />, disabled: false },
    { name: 'Radio', icon: <Iconify icon="formkit:radio" />, disabled: true },
    { name: 'Yes / No', icon: <Iconify icon="line-md:switch-off" />, disabled: true },
    { name: 'Checkbox', icon: <Iconify icon="ri:checkbox-multiple-line" />, disabled: true },
    { name: 'File upload', icon: <Iconify icon="solar:cloud-upload-broken" />, disabled: true },
  ];

  const isFormValid = () => {
    if (!appName.trim()) return false;
    if (!elements.length) return false;
    if (currentStep === 1 && promptContent.replace(/<[^>]*>/g, '').length === 0) return false;

    return elements.every((element) => {
      if (!element.inputName.trim()) return false;

      if (element.type === 'Dropdown') {
        return element.options.length > 0 && element.options.every((option) => option.value.trim());
      }

      return true;
    });
  };

  const handleContinue = () => {
    setCurrentStep(currentStep + 1);

    // Scroll page to top when moving to second step
    if (currentStep === 0) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);

    // Scroll page to top when moving to first step
    if (currentStep === 1) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handleCreate = async () => {
    // If submission is already in progress, prevent another submission
    if (isSubmitting || isLoading) {
      return;
    }

    // Set submission state to active
    setIsSubmitting(true);

    // Create form data
    const rawFormData = {
      appName,
      description,
      maxLength,
      elements,
      prompt: promptContent,
      temperature,
      topP,
      frequencyPenalty,
      presencePenalty,
      selectedModel,
    };

    // Function to clean HTML tags
    const stripHtmlTags = (html) => {
      if (!html) return '';
      // Remove HTML tags and resolve HTML entities
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      return tempDiv.textContent || tempDiv.innerText || '';
    };

    // Function to convert element types to API format
    const mapElementTypeToApiType = (type) => {
      switch (type) {
        case 'Short Text':
          return 'input';
        case 'Paragraph':
          return 'textarea';
        case 'Dropdown':
          return 'select';
        default:
          return 'input'; // Default as input
      }
    };

    // Convert to the format required by the API
    const simple_app_form = elements.map((element) => {
      const baseField = {
        type: mapElementTypeToApiType(element.type),
        label: element.defaultText || element.inputName,
        name: element.inputName,
        value: element.defaultText || '',
      };

      // If type is Dropdown, add choices
      if (element.type === 'Dropdown' && element.options && element.options.length > 0) {
        baseField.choices = element.options.map((opt) => opt.value).join('\n');
      }

      return baseField;
    });

    // Create slug from appName
    const slug = appName
      .toLowerCase()
      .replace(/\s+/g, '-') // Replace spaces with dashes
      .replace(/[^\w\-]+/g, '') // Remove non-alphanumeric characters
      .replace(/\-\-+/g, '-') // Convert multiple dashes to single dash
      .replace(/^-+/, '') // Remove dashes from start
      .replace(/-+$/, ''); // Remove dashes from end

    // Data format to be sent to the API
    const apiData = {
      title: appName,
      slug: slug,
      simple_app_form: simple_app_form,
      temperature: temperature.toString(),
      top_p: topP.toString(),
      presence_penalty: presencePenalty.toString(),
      frequency_penalty: frequencyPenalty.toString(),
      prompt: stripHtmlTags(promptContent),
      model: selectedModel,
      description: stripHtmlTags(description),
      userId: user?._id || '',
    };

    try {
      // API call using RTK Query mutation
      const response = await createSimpleApp(apiData).unwrap();

      // Automatically add application to favorites
      if (response.data && response.data._id && user?._id) {
        try {
          await addShortcut({
            userId: user._id,
            shortcutType: 'ai_app',
            shortcutID: response.data._id,
          }).unwrap();
        } catch (shortcutError) {
          console.error('Failed to add app to favorites:', shortcutError);
          // We consider app creation successful even if adding to favorites fails
        }
      }

      // Update journey tracking - if cardId exists
      if (journeyCardId && user?._id && user?.journeyLevel?.name && trackingData) {
        try {
          // Use user's journey level
          const userLevel = user.journeyLevel.name.toLowerCase();

          // Update journey tracking
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: trackingData,
            userLevel: userLevel,
            cardId: journeyCardId,
          });

          // Refresh Redux cache - this way useGetJourneyTrackingQuery results in other components are also updated
          const baseQueryApi = new URL(window.location.origin).toString();
          const cacheKey = `${baseQueryApi}getJourneyTracking(${user._id})`;

          // If we could use Redux-Toolkit Query's tag and cache invalidation features,
          // This cache invalidation process would happen automatically.
          // Here we manually save a timestamp to localStorage,
          // Footer component will detect this and refetch
          localStorage.setItem('journey_tracking_updated', Date.now().toString());
        } catch (trackingError) {
          developmentLogs('Failed to update journey tracking:', trackingError);
          // Continue even if journey update fails
        }
      }

      // Show toast and redirect after 3 seconds
      toast.success(
        `${t('simpleAIApps.create.toast.success.title')} ${t('simpleAIApps.create.toast.success.description')}`,
        {
          position: 'top-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: 'light',
          icon: '🎉',
          onClose: () => {
            navigate(`/ai_apps/${response.data.slug}/`);
          },
        }
      );
    } catch (error) {
      console.error('Error details:', error);
      toast.error(
        `${t('simpleAIApps.create.toast.error.title')} ${t('simpleAIApps.create.toast.error.description')}`,
        {
          position: 'top-right',
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: 'light',
        }
      );
      // Process completed, reset submission state
      setIsSubmitting(false);
    }
  };

  const handleDragStart = (e, element) => {
    // Send input parameter within noneditable span
    e.dataTransfer.setData(
      'text/plain',
      `<span contenteditable="false" class="mceNonEditable input-param"><b>{${element.inputName}}</b></span>`
    );
  };

  const handleEditorDrop = (e) => {
    e.preventDefault();
    const text = e.dataTransfer.getData('text/plain');
    if (editorRef.current) {
      editorRef.current.execCommand('mceInsertContent', false, text);
    }
  };

  const handleEditorChange = (content, editor) => {
    setDescription(content);
  };
  const handlePromptChange = (content, editor) => {
    setPromptContent(content);
  };

  const handleEditElement = (elementId) => {
    setFocusedElementId(elementId);
    setCurrentStep(0);
  };

  const handleAddFieldClick = () => {
    setIsInputTypesBouncing(true);
    setTimeout(() => setIsInputTypesBouncing(false), 1000);
  };

  useEffect(() => {
    if (focusedElementId && currentStep === 0) {
      const inputElement = document.querySelector(`[data-element-id="${focusedElementId}"]`);
      if (inputElement) {
        inputElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        inputElement.focus();
      }
      setFocusedElementId(null);
    }
  }, [currentStep, focusedElementId]);

  // Tour configuration for first step
  // const initTour = useCallback(() => {
  //   driverRef.current = driver({
  //     showProgress: true,
  //     showButtons: ['next', 'previous', 'close'],
  //     steps: [
  //       {
  //         element: '.app-description',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.steps.appDescription.title'),
  //           description: t('simpleAIApps.create.tour.steps.appDescription.content'),
  //           side: 'bottom',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.input-types',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.steps.inputTypes.title'),
  //           description: t('simpleAIApps.create.tour.steps.inputTypes.content'),
  //           side: 'left',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.form-elements',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.steps.formElements.title'),
  //           description: t('simpleAIApps.create.tour.steps.formElements.content'),
  //           side: 'right',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.navigation-buttons',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.steps.navigationButtons.title'),
  //           description: t('simpleAIApps.create.tour.steps.navigationButtons.content'),
  //           side: 'top',
  //           align: 'start',
  //         },
  //       },
  //     ],
  //     nextBtnText: t('simpleAIApps.create.tour.next'),
  //     prevBtnText: t('simpleAIApps.create.tour.previous'),
  //     doneBtnText: t('simpleAIApps.create.tour.done'),
  //     closeBtnText: t('simpleAIApps.create.tour.skip'),
  //     onDestroyStarted: () => {
  //       localStorage.setItem('simpleAIAppCreatorTourShown', 'true');
  //       if (driverRef.current) {
  //         driverRef.current.destroy();
  //       }
  //     },
  //     onReset: () => {
  //       localStorage.setItem('simpleAIAppCreatorTourShown', 'true');
  //       if (driverRef.current) {
  //         driverRef.current.destroy();
  //       }
  //     },
  //   });
  // }, [t]);

  // Tour configuration for second step
  // const initTourStep2 = useCallback(() => {
  //   driverRef.current = driver({
  //     showProgress: true,
  //     showButtons: ['next', 'previous', 'close'],
  //     steps: [
  //       {
  //         element: '.prompt-editor',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.step2.steps.promptEditor.title'),
  //           description: t('simpleAIApps.create.tour.step2.steps.promptEditor.content'),
  //           side: 'bottom',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.edit-inputs',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.step2.steps.editInputs.title'),
  //           description: t('simpleAIApps.create.tour.step2.steps.editInputs.content'),
  //           side: 'left',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.app-settings',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.step2.steps.appSettings.title'),
  //           description: t('simpleAIApps.create.tour.step2.steps.appSettings.content'),
  //           side: 'top',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.navigation-buttons',
  //         popover: {
  //           title: t('simpleAIApps.create.tour.step2.steps.navigationButtons.title'),
  //           description: t('simpleAIApps.create.tour.step2.steps.navigationButtons.content'),
  //           side: 'top',
  //           align: 'start',
  //         },
  //       },
  //     ],
  //     nextBtnText: t('simpleAIApps.create.tour.next'),
  //     prevBtnText: t('simpleAIApps.create.tour.previous'),
  //     doneBtnText: t('simpleAIApps.create.tour.done'),
  //     closeBtnText: t('simpleAIApps.create.tour.skip'),
  //     // Save to localStorage when tour is completed or closed
  //     onDestroyStarted: () => {
  //       localStorage.setItem('simpleAIAppCreatorTourStep2Shown', 'true');
  //       if (driverRef.current) {
  //         driverRef.current.destroy();
  //       }
  //     },
  //     onReset: () => {
  //       localStorage.setItem('simpleAIAppCreatorTourStep2Shown', 'true');
  //       if (driverRef.current) {
  //         driverRef.current.destroy();
  //       }
  //     },
  //   });
  // }, [t]);

  // Start tour for first step
  // useEffect(() => {
  //   if (currentStep === 0) {
  //     // Check if tour has been shown in localStorage
  //     const tourShown = localStorage.getItem('simpleAIAppCreatorTourShown');

  //     // If tour hasn't been shown before
  //     if (!tourShown) {
  //       initTour();

  //       // Start tour after page is fully loaded
  //       const timer = setTimeout(() => {
  //         if (driverRef.current) {
  //           driverRef.current.drive();
  //           setShowTour(true);
  //         }
  //       }, 1000);

  //       return () => clearTimeout(timer);
  //     }
  //   }
  // }, [currentStep, initTour]);

  // Start tour for second step
  // useEffect(() => {
  //   if (currentStep === 1) {
  //     // Check if second tour has been shown in localStorage
  //     const tourStep2Shown = localStorage.getItem('simpleAIAppCreatorTourStep2Shown');

  //     // If second tour hasn't been shown before
  //     if (!tourStep2Shown) {
  //       initTourStep2();

  //       // Start tour after page is fully loaded
  //       const timer = setTimeout(() => {
  //         if (driverRef.current) {
  //           driverRef.current.drive();
  //           setShowTour(true);
  //         }
  //       }, 1000);

  //       return () => clearTimeout(timer);
  //     }
  //   }
  // }, [currentStep, initTourStep2]);

  // Data preparation function for TextUsecase component
  const prepareAppDataForTextUsecase = (appData) => {
    if (!appData) return null;

    // Clean HTML tags
    const cleanDescription = appData.description
      ? appData.description.replace(/<\/?[^>]+(>|$)/g, '')
      : '';

    // Convert form fields to the format expected by TextUsecase
    const formFields = (appData.elements || []).map((element) => {
      // Convert input type to format expected by TextUsecase
      let fieldType;

      switch (element.type) {
        case 'Dropdown':
          fieldType = 'select';
          break;
        case 'Paragraph':
          fieldType = 'textarea';
          break;
        case 'Short Text':
          fieldType = 'text';
          break;
        case 'Yes / No':
          fieldType = 'radio';
          break;
        case 'Checkbox':
          fieldType = 'checkbox';
          break;
        case 'File upload':
          fieldType = 'file';
          break;
        default:
          fieldType = 'text';
      }

      return {
        _id: element.id.toString(),
        type: fieldType,
        label: element.inputName,
        name: element.inputName,
        default_value: element.defaultText || '',
        choices:
          element.type === 'Dropdown' ? element.options.map((opt) => opt.value).join('\n') : '',
      };
    });

    // Create complete data structure expected by TextUsecase
    return {
      _id: Date.now().toString(),
      title: appData.appName,
      slug: appData.appName.toLowerCase().replace(/\s+/g, '-'),
      content: cleanDescription,
      api_type: 'stream',
      stream_settings: {
        stream_form: {
          form_fields: formFields,
        },
        stream_prompt: appData.promptContent?.replace(/<\/?[^>]+(>|$)/g, '') || '',
        temperature: appData.temperature?.toString() || '1',
        top_p: appData.topP?.toString() || '1',
        frequency_penalty: appData.frequencyPenalty?.toString() || '0',
        presence_penalty: appData.presencePenalty?.toString() || '0',
        model_selection: appData.selectedModel || 'gpt-4o',
        max_token: '4000',
      },
    };
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 0, mb: 3 }}>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />

      <Box sx={{ p: 0 }}>
        <Grid container spacing={0}>
          <Grid item mb={4} xs={12}>
            <WelcomeHeader
              isPageView
              title={t('simpleAIApps.create.title')}
              description={t('simpleAIApps.create.description')}
            />
          </Grid>
          <Grid container spacing={3}>
            <Grid item xs={12} md={12} lg={12}>
              {elements.length > 0 && (
                <ReviewButtons
                  currentStep={currentStep}
                  isFormValid={isFormValid}
                  handleBack={handleBack}
                  handleContinue={handleCreate}
                  isSubmitting={isSubmitting || isLoading}
                />
              )}
            </Grid>
          </Grid>
          {currentStep === 0 ? (
            <Grid container spacing={3}>
              <Grid item xs={12} md={8} lg={8}>
                <div className="app-description">
                  <AppDescription
                    appName={appName}
                    description={description}
                    maxLength={maxLength}
                    handleNameChange={handleNameChange}
                    handleEditorChange={handleEditorChange}
                  />
                </div>

                <div className="form-elements">
                  <FormElements
                    elements={elements}
                    setElements={setElements}
                    handleRemoveElement={handleRemoveElement}
                    handleElementChange={handleElementChange}
                    handleOptionChange={handleOptionChange}
                    handleAddOption={handleAddOption}
                    handleRemoveOption={handleRemoveOption}
                    focusedElementId={focusedElementId}
                    onAddFieldClick={handleAddFieldClick}
                  />
                </div>
              </Grid>

              <Grid item xs={12} md={4} lg={4}>
                <InputTypes
                  elements={elements}
                  elementLimit={elementLimit}
                  inputTypes={inputTypes}
                  handleAddElement={handleAddElement}
                  isBouncing={isInputTypesBouncing}
                />
              </Grid>
            </Grid>
          ) : currentStep === 1 ? (
            <Grid container spacing={3}>
              <Grid item xs={12} md={8} lg={8}>
                <div className="prompt-editor">
                  <PromptEditor
                    elements={elements}
                    promptContent={promptContent}
                    maxPromptLength={maxPromptLength}
                    handlePromptChange={handlePromptChange}
                    handleDragStart={handleDragStart}
                    handleEditorDrop={handleEditorDrop}
                    editorRef={editorRef}
                  />
                </div>

                <div className="app-settings">
                  <AppSettings
                    selectedModel={selectedModel}
                    temperature={temperature}
                    topP={topP}
                    frequencyPenalty={frequencyPenalty}
                    presencePenalty={presencePenalty}
                    setSelectedModel={setSelectedModel}
                    setTemperature={setTemperature}
                    setTopP={setTopP}
                    setFrequencyPenalty={setFrequencyPenalty}
                    setPresencePenalty={setPresencePenalty}
                  />
                </div>
              </Grid>
              <Grid item xs={12} md={4} lg={4}>
                <div className="edit-inputs">
                  <EditInputs elements={elements} onEditElement={handleEditElement} />
                </div>
              </Grid>
            </Grid>
          ) : (
            <Grid item mb={4} xs={12}>
              <TextUsecase
                data={prepareAppDataForTextUsecase({
                  appName,
                  description,
                  elements,
                  promptContent,
                  temperature,
                  topP,
                  frequencyPenalty,
                  presencePenalty,
                  selectedModel,
                })}
                disableFavorites={true}
                disableTracking={true}
                hideTitle={true}
                id={Date.now().toString()}
                onlyShowUsecase={true}
              />
            </Grid>
          )}
          <Grid container spacing={3}>
            <Grid item xs={12} md={8} lg={8}>
              <NavigationButtons
                currentStep={currentStep}
                isFormValid={isFormValid}
                handleBack={handleBack}
                handleContinue={handleContinue}
              />
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
}

export default CreateSimpleAIApp;
