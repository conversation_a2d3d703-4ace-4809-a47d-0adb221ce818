import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Tabs,
  Tab,
  Card,
  CardContent,
  Typography,
  Button,
  Stack,
  Grid,
  CircularProgress,
} from '@mui/material';
import PropTypes from 'prop-types';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import styles from './TrainingTabs.module.scss';
import cockpitStyles from '../../Cockpit.module.scss';
import {
  useGetCompletedCoursesByUserIdQuery,
  useGetInProgressCoursesByUserIdQuery,
} from '../../../../redux/services/courses-api';

import { useSelectedCoursesQuery } from '../../../../redux/services/cds-api';
// Function to strip HTML tags from content
const stripHtml = (html) => {
  if (!html) return '';
  const doc = new DOMParser().parseFromString(html, 'text/html');
  return doc.body.textContent || '';
};

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2, height: '100%' }}>{children}</Box>}
    </div>
  );
};

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

const TrainingTabs = ({ userId }) => {
  const [value, setValue] = useState(0);
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const { data: completedCourses } = useGetCompletedCoursesByUserIdQuery(userId);
  const { data: inProgressCourses } = useGetInProgressCoursesByUserIdQuery(userId);

  // Devam eden kurslar için
  const inProgressCourseIds = inProgressCourses?.data?.map((course) => course.course) || [];
  const { data: selectedInProgressCourses, isLoading: selectedInProgressCoursesLoading } =
    useSelectedCoursesQuery(inProgressCourseIds, {
      serializeQueryArgs: () => 'inProgressCourses',
    });

  // Tamamlanan kurslar için
  const completedCourseIds = completedCourses?.data?.map((course) => course.course) || [];
  const { data: selectedCompletedCourses, isLoading: selectedCompletedCoursesLoading } =
    useSelectedCoursesQuery(completedCourseIds, {
      serializeQueryArgs: () => 'completedCourses',
    });

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box className={`${styles.innerTabsContainer} ${cockpitStyles.appCardsContainer}`}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange}>
          <Tab label={t('cockpit.trainingTabs.inProgress')} className={styles.capitalizedTab} />
          <Tab label={t('cockpit.trainingTabs.completed')} />
        </Tabs>
      </Box>
      <Box sx={{ flexGrow: 1, height: '100%' }}>
        <TabPanel value={value} index={0}>
          <div
            className={`${styles.innerTabPanel} ${selectedInProgressCourses?.data?.length > 1 ? styles.twoColumnsGrid : ''}`}
          >
            {selectedInProgressCoursesLoading ? (
              <Grid container justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
              </Grid>
            ) : selectedInProgressCourses?.data?.length > 0 ? (
              selectedInProgressCourses?.data?.map((course) => (
                <Card key={course.id} className={styles.courseCard}>
                  <CardContent className={styles.courseInfo}>
                    <div className={styles.courseHeader}>
                      <Typography variant="h5" component="h4" className={styles.courseTitle}>
                        {course?.translations?.[currentLanguage]?.title ||
                          course?.translations?.en?.title}
                      </Typography>
                      <Typography variant="body2" className={styles.courseDescription}>
                        {stripHtml(
                          course?.translations?.[currentLanguage]?.description ||
                            course?.translations?.en?.description
                        )}
                      </Typography>
                    </div>
                    <Stack
                      direction="row"
                      className={styles.courseActions}
                      justifyContent="space-between"
                      alignItems="center"
                    >
                      <Box className={styles.progressInfo}>
                        {course?.translations?.[currentLanguage]?.providerImage ||
                        course?.translations?.en?.providerImage ? (
                          <Box
                            component="img"
                            src={
                              course?.translations?.[currentLanguage]?.providerImage ||
                              course?.translations?.en?.providerImage
                            }
                            alt="Provider"
                            className={styles.providerImage}
                          />
                        ) : null}
                      </Box>
                      <Button
                        variant="contained"
                        className={styles.continueButton}
                        href={`/course/${course._id}`}
                        target={course.newTab ? '_blank' : '_self'}
                        endIcon={<ArrowForwardIcon />}
                        size="small"
                      >
                        {t('cockpit.trainingTabs.goToTraining')}
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Typography>{t('cockpit.trainingTabs.noInProgressCourses')}</Typography>
            )}
          </div>
        </TabPanel>
        <TabPanel value={value} index={1}>
          <div
            className={`${styles.innerTabPanel} ${selectedCompletedCourses?.data?.length > 1 ? styles.twoColumnsGrid : ''}`}
          >
            {selectedCompletedCoursesLoading ? (
              <Grid container justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
              </Grid>
            ) : selectedCompletedCourses?.data?.length > 0 ? (
              selectedCompletedCourses?.data?.map((course) => (
                <Card key={course.id} className={styles.courseCard}>
                  <CardContent className={styles.courseInfo}>
                    <div className={styles.courseHeader}>
                      <Typography variant="h5" component="h4" className={styles.courseTitle}>
                        {course?.translations?.[currentLanguage]?.title ||
                          course?.translations?.en?.title}
                      </Typography>
                      <Typography variant="body2" className={styles.courseDescription}>
                        {stripHtml(
                          course?.translations?.[currentLanguage]?.description ||
                            course?.translations?.en?.description
                        )}
                      </Typography>
                    </div>
                    <Stack
                      direction="row"
                      className={styles.courseActions}
                      justifyContent="space-between"
                      alignItems="center"
                    >
                      <Box className={styles.progressInfo}>
                        {course?.translations?.[currentLanguage]?.providerImage ||
                        course?.translations?.en?.providerImage ? (
                          <Box
                            component="img"
                            src={
                              course?.translations?.[currentLanguage]?.providerImage ||
                              course?.translations?.en?.providerImage
                            }
                            alt="Provider"
                            className={styles.providerImage}
                          />
                        ) : null}
                      </Box>
                      <Button
                        variant="contained"
                        className={styles.continueButton}
                        href={`/course/${course._id}`}
                        target={course.newTab ? '_blank' : '_self'}
                        endIcon={<ArrowForwardIcon />}
                        size="small"
                      >
                        {t('cockpit.trainingTabs.goToTraining')}
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Typography>{t('cockpit.trainingTabs.noCompletedCourses')}</Typography>
            )}
          </div>
        </TabPanel>
      </Box>
    </Box>
  );
};

TrainingTabs.propTypes = {
  userId: PropTypes.string.isRequired,
};

export default TrainingTabs;
