@use '../../../../styles/abstracts/variables' as *;

// Renk değişkenleri
$success-color: #2e7d32;
$success-hover-color: #1b5e20;
$white-color: #fff;
$title-color: #2A2B3F;
$description-color: #666;
$border-color: #e0e0e0;

// <PERSON><PERSON>lge değişkenleri
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

// <PERSON><PERSON>er değişkenler
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-6: 24px;
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

.innerTabsContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: $spacing-2;
}

.innerTabPanel {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.twoColumnsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-6;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }

  .courseCard {
    margin-bottom: 0;
  }
}

.capitalizedTab {
  text-transform: inherit;
}

.courseCard {
  display: flex;
  gap: $spacing-3;
  padding: $spacing-2;
  border-radius: $border-radius-lg;
  background-color: $bg-paper;
  box-shadow: $shadow-sm;
  transition: all 0.3s ease;
  border: 1px solid $border-color;
  margin-bottom: $spacing-4;
  height: calc(100% - ($spacing-3 * 2));
  box-shadow:none !important;
  overflow: hidden;

  .twoColumnsGrid & {
    flex-direction: column;

    @media (min-width: 992px) {
      flex-direction: row;
    }
  }
}

.courseLeft {
  flex: 0 0 240px;
  height: 200px;
  margin: 5px;
  padding: 10px;
  border-radius: $border-radius-md;
  overflow: hidden;
  box-shadow: $shadow-xs;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.courseInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
  padding: $spacing-4 !important;
}

.courseRight {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  gap: $spacing-4;
}

.courseHeader {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.courseTitle {
  font-size: 1rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  color: $primary-color !important;
  line-height: 1.2 !important;

  @media (min-width: $mobile) {
    font-size: 1.15rem !important;
  }

  @media (min-width: $tablet) {
    font-size: 1.3rem !important;
  }
}

.courseDescription {
  font-size: 0.8rem !important;
  color: $text-secondary !important;
  display: -webkit-box !important;
  overflow: hidden !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  line-height: 1.4 !important;

  @media (min-width: $mobile) {
    font-size: 0.85rem !important;
  }

  @media (min-width: $tablet) {
    font-size: 0.9rem !important;
  }
}

.chapterInfo {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;

  h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: $title-color;
    margin: 0;
    line-height: 1.4;
    letter-spacing: 0.5px;
  }

  p {
    font-size: 0.875rem;
    color: $description-color;
    line-height: 1.6;
    margin: 0;
    opacity: 0.9;
  }
}

.courseActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: $spacing-4;
  border-top: 1px solid $border-color;

  @media (max-width: $mobile) {
    margin-top: $spacing-2;
  }

  @media (min-width: $mobile) {
    margin-top: $spacing-3;
  }
}

.progressInfo {
  display: flex;
  align-items: center;
  gap: $spacing-2;
  padding: $spacing-2;
  background-color: rgba($bg-paper, 0.8);
  border-radius: $border-radius-sm;
  opacity: 0.9;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }

  img {
    height: 24px;
    width: auto;
    object-fit: contain;
  }
}

.providerImage {
  height: 28px !important;
  object-fit: contain !important;
  margin-right: 10px !important;
}

.continueButton {
  min-width: auto !important;
  height: 28px !important;
  border-radius: 6px !important;
  text-transform: none !important;
  font-weight: 500 !important;
  font-size: 0.75rem !important;
  padding: 0.3rem 1.2rem !important;
  transition: all 0.3s ease !important;
  box-shadow: none !important;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  }

  :global(.MuiSvgIcon-root) {
    font-size: 0.85rem !important;
  }
}

.successButton {
  background-color: $success-color !important;
  color: $white-color !important;
  text-transform: capitalize;

  &:hover {
    background-color: $success-hover-color !important;
  }
}