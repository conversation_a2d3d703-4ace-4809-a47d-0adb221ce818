import PropTypes from 'prop-types';
import { Box, Typography, TextField } from '@mui/material';
import { Editor } from '@tinymce/tinymce-react';
import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Droppable, Draggable } from '@hello-pangea/dnd';
import EmptyDropArea from './EmptyDropArea';
import ArrowIcon from './ArrowIcon';

import '../../WorkflowCreator.scss';

const WorkflowDropArea = ({
  droppedElements,
  onDrop,
  onDragOver,
  onDeleteElement,
  onElementInputChange,
  onElementDetailChange,
  onOutputNameChange,
}) => {
  const { t } = useTranslation();
  const [editorRefs, setEditorRefs] = useState({});
  const prevDroppedElementsRef = useRef(droppedElements);

  const hasDisplayElement = droppedElements.some((element) => element.type === 'output');

  // Update editor refs when elements are removed
  useEffect(() => {
    const currentElementIds = droppedElements.map((el) => el.id);
    const editorRefIds = Object.keys(editorRefs);

    const hasChanges = editorRefIds.some((id) => !currentElementIds.includes(id));

    if (hasChanges) {
      const newRefs = {};
      Object.entries(editorRefs).forEach(([id, editor]) => {
        if (currentElementIds.includes(id)) {
          newRefs[id] = editor;
        }
      });
      setEditorRefs(newRefs);
    }
  }, [droppedElements, editorRefs]);

  // Handle element reordering and update editor content
  useEffect(() => {
    const prevElements = prevDroppedElementsRef.current;
    const currentElements = droppedElements;

    // Check if elements have been reordered (same elements but different order)
    const sameElementsDifferentOrder =
      prevElements.length === currentElements.length &&
      prevElements.every((el) => currentElements.some((cel) => cel.id === el.id)) &&
      JSON.stringify(prevElements.map((el) => el.id)) !==
        JSON.stringify(currentElements.map((el) => el.id));

    if (sameElementsDifferentOrder) {
      // Find prompt elements that need their content updated
      currentElements.forEach((element) => {
        if (element.type === 'prompt' && editorRefs[element.id]) {
          // Force editor content update to refresh variables
          const editor = editorRefs[element.id];
          if (editor) {
            const content = element.promptContent || '';
            editor.setContent(content);
          }
        }
      });
    }

    // Update the ref
    prevDroppedElementsRef.current = currentElements;
  }, [droppedElements, editorRefs]);

  const handleEditorInit = (editor, elementId) => {
    setEditorRefs((prev) => ({
      ...prev,
      [elementId]: editor,
    }));

    editor.on('dragover', (e) => {
      e.preventDefault();
      e.stopPropagation();
    });

    editor.getBody().addEventListener(
      'drop',
      (e) => {
        handleEditorDrop(editor, e);
      },
      true
    );

    editor.getBody().setAttribute('contenteditable', 'true');

    editor.off('click');
  };

  const handleDrop = (e) => {
    const draggedElementType = e.dataTransfer.getData('elementType');

    if (draggedElementType === 'output' && hasDisplayElement) {
      e.preventDefault();
      return;
    }

    onDrop(e);
  };

  const handleVariableDragStart = (e, variable) => {
    const uniqueId = `id-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const formattedVariable = variable.replace(/\s+/g, '_').toLowerCase();
    // Sürükleme önizlemesi için element
    const dragPreview = document.createElement('div');
    dragPreview.style.cssText = `
      background-color: #dddddd;
      border: 1px solid #cccccc;
      padding: 2px 5px;
      display: inline-block;
      margin: 0 2px;
      border-radius: 4px;
      color: #333333;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      font-size: 14px;
      position: absolute;
      top: -9999px;
      left: -9999px;
      cursor: move;
    `;
    dragPreview.textContent = `{${formattedVariable}}`;
    document.body.appendChild(dragPreview);
    e.dataTransfer.setDragImage(dragPreview, 0, 0);

    setTimeout(() => {
      document.body.removeChild(dragPreview);
    }, 0);

    const content = `<span class="custom-label" 
      style="background-color: #dddddd; 
      border: 1px solid #cccccc; 
      padding: 2px 5px; 
      display: inline-block; 
      cursor: move; 
      margin: 0 2px; 
      border-radius: 4px; 
      text-decoration: none; 
      color: #333333; 
      cursor: move;" 
      contenteditable="false" 
      draggable="true" 
      href="#" 
      data-target="prompt" 
      
      data-key="${formattedVariable}"
      data-uid="${uniqueId}">{${formattedVariable}}</span>`;

    e.dataTransfer.setData('text/html', content);
    e.dataTransfer.setData('text/plain', `{${formattedVariable}}`);
  };

  const handleEditorDrop = (editor, e) => {
    e.preventDefault();
    e.stopPropagation();

    const content = e.dataTransfer.getData('text/html');
    const sourceUid = e.dataTransfer.getData('source-uid');

    if (content) {
      editor.selection.collapse(true);

      // Eğer aynı editör içinde taşıma yapılıyorsa
      if (sourceUid) {
        // Önce eski elementi bul ve sil
        const oldElement = editor.dom.select(`[data-uid="${sourceUid}"]`)[0];
        if (oldElement) {
          oldElement.parentNode.removeChild(oldElement);
        }
      }

      editor.insertContent(content);
    }
  };

  const getAvailableVariables = (promptIndex) => {
    const variables = [];

    // Get the current element from sorted array
    const sortedElements = [...droppedElements].sort((a, b) =>
      a.type === 'output' ? 1 : b.type === 'output' ? -1 : 0
    );
    const currentElement = sortedElements[promptIndex];
    const isDisplayElement = currentElement?.type === 'output';

    // If it's a display element, show all variables
    if (isDisplayElement) {
      droppedElements.forEach((el) => {
        // Skip the display element itself
        if (el.type === 'output') {
          return;
        }

        // Add text input variables
        if (el.type === 'input') {
          const inputKey = el.input || el.inputs?.[0]?.value;
          if (inputKey) {
            variables.push({
              key: inputKey.toLowerCase().replace(/\s+/g, '_'),
              value: inputKey,
            });
          }
        }

        // Add prompt output variables
        if (el.type === 'prompt' && el.outputName) {
          variables.push({
            key: el.outputName.toLowerCase().replace(/\s+/g, '_'),
            value: el.outputName,
          });
        }
      });
    } else {
      // For non-display elements, only include elements that come before this prompt in the workflow
      droppedElements.forEach((el, index) => {
        // Skip the current element and any elements after it
        if (index >= promptIndex) {
          return;
        }

        // Add text input variables
        if (el.type === 'input') {
          const inputKey = el.input || el.inputs?.[0]?.value;
          if (inputKey) {
            variables.push({
              key: inputKey.toLowerCase().replace(/\s+/g, '_'),
              value: inputKey,
            });
          }
        }

        // Add prompt output variables
        if (el.type === 'prompt' && el.outputName) {
          variables.push({
            key: el.outputName.toLowerCase().replace(/\s+/g, '_'),
            value: el.outputName,
          });
        }
      });
    }

    return variables;
  };

  const handleContentChange = (elementId, content) => {
    // Both prompt and output elements use onElementDetailChange
    // The main component will determine which field to update based on element type
    onElementDetailChange(elementId, content);
  };

  return (
    <Box className="workflow-creator-workflow-column">
      <Box className="workflow-column workflow-column-container">
        <Box className="inner container container--drop-area" id="drag-drop-elements">
          <Box id="drop-area" className="drop-area" onDrop={handleDrop} onDragOver={onDragOver}>
            {!droppedElements.length ? (
              <Droppable droppableId="workflow-elements-empty">
                {(provided, snapshot) => (
                  <Box
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    sx={{
                      minHeight: '400px',
                      backgroundColor: snapshot.isDraggingOver ? 'rgba(25, 118, 210, 0.05)' : 'transparent',
                      transition: 'background-color 0.2s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <EmptyDropArea />
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            ) : (
              <Droppable droppableId="workflow-elements" direction="horizontal">
                {(provided, snapshot) => (
                  <Box
                    className="drop-area-container drop-area-container-padding"
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    sx={{
                      backgroundColor: snapshot.isDraggingOver
                        ? 'rgba(25, 118, 210, 0.05)'
                        : 'transparent',
                      transition: 'background-color 0.2s ease',
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'flex-start',
                      overflowX: 'auto',
                      overflowY: 'visible',
                      minHeight: '400px',
                      gap: '16px',
                      padding: '16px',
                    }}
                  >
                    {/* Sort elements to ensure display element is always last */}
                    {[...droppedElements]
                      .sort((a, b) => (a.type === 'output' ? 1 : b.type === 'output' ? -1 : 0))
                      .map((element, index, sortedArray) => (
                        <Draggable
                          key={element.id}
                          draggableId={element.id}
                          index={index}
                          isDragDisabled={element.type === 'output'}
                        >
                          {(provided, snapshot) => (
                            <Box
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={
                                element.type === 'output'
                                  ? 'workflow-element unsortable'
                                  : 'workflow-element'
                              }
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                opacity: snapshot.isDragging ? 0.8 : 1,
                                transform:
                                  element.type === 'output'
                                    ? 'none'
                                    : snapshot.isDragging
                                      ? 'rotate(1deg) scale(1.02)'
                                      : 'none',
                                minWidth: '300px',
                                maxWidth: '300px',
                                flexShrink: 0,
                              }}
                              id={element.id}
                              type={element.type}
                            >
                              <Box
                                sx={{ display: 'flex', alignItems: 'flex-start', width: '100%' }}
                              >
                                <Box className="drop-element" sx={{ flex: 1 }}>
                                  <Box sx={{ paddingBottom: '12px' }}>
                                    <Box 
                                      className="element-headline"
                                      {...(element.type !== 'output' ? provided.dragHandleProps : {})}
                                      sx={{
                                        cursor: element.type !== 'output' ? 'grab' : 'default',
                                        '&:active': {
                                          cursor: element.type !== 'output' ? 'grabbing' : 'default',
                                        }
                                      }}
                                    >
                                      <Box
                                        component="img"
                                        className="element-icon"
                                        src={element.icon}
                                        alt={element.title}
                                      />
                                      <Typography className="element-title">
                                        {element.title}
                                      </Typography>
                                      <Box
                                        component="a"
                                        className="deleteComponent delete-button"
                                        onClick={() => onDeleteElement(element.id)}
                                        data-element-id={element.id}
                                      >
                                        <svg
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                                            fill="currentColor"
                                          />
                                        </svg>
                                      </Box>
                                    </Box>
                                    <Box className="element-inner">
                                      {element.type === 'input' && (
                                        <>
                                          <Box
                                            component="label"
                                            htmlFor={`input-key-${element.id}`}
                                            className="form-label"
                                          >
                                            <Typography className="element-title">
                                              {t('create.workflowCreator.dropArea.form.input')}
                                              <span className="drop-area-form-required-asterisk">
                                                *
                                              </span>{' '}
                                              :
                                            </Typography>
                                            <TextField
                                              fullWidth
                                              size="small"
                                              id={`input-key-${element.id}`}
                                              name="input-key"
                                              value={element.input}
                                              onChange={(e) =>
                                                onElementInputChange(element.id, e.target.value)
                                              }
                                              className="form-field form-field--input"
                                            />
                                          </Box>

                                          <Box className="drop-area-form-input-section">
                                            <Box
                                              component="label"
                                              htmlFor={`input-value-${element.id}`}
                                              className="form-label"
                                            >
                                              <Typography className="element-title">
                                                {t(
                                                  'create.workflowCreator.dropArea.form.inputDetail'
                                                )}
                                                :
                                              </Typography>
                                              <TextField
                                                fullWidth
                                                size="small"
                                                id={`input-value-${element.id}`}
                                                name="input-value"
                                                value={element.detail}
                                                onChange={(e) =>
                                                  onElementDetailChange(element.id, e.target.value)
                                                }
                                                className="form-field form-field--input"
                                              />
                                            </Box>
                                          </Box>
                                        </>
                                      )}

                                      {(element.type === 'prompt' || element.type === 'output') && (
                                        <>
                                          {element.type === 'prompt' && (
                                            <Box
                                              component="label"
                                              htmlFor={`output-key-${element.id}`}
                                              className="form-label"
                                            >
                                              <Typography className="element-title">
                                                {t(
                                                  'create.workflowCreator.dropArea.form.outputName'
                                                )}
                                                <span className="drop-area-form-required-asterisk">
                                                  *
                                                </span>{' '}
                                                :
                                              </Typography>
                                              <TextField
                                                fullWidth
                                                size="small"
                                                id={`output-key-${element.id}`}
                                                name="output-key"
                                                value={element.outputName || ''}
                                                onChange={(e) =>
                                                  onOutputNameChange(element.id, e.target.value)
                                                }
                                                className="form-field form-field--input"
                                              />
                                            </Box>
                                          )}

                                          <Box
                                            className={`drop-area-form-content-section ${element.type === 'prompt' ? 'drop-area-form-content-section--prompt' : ''}`}
                                          >
                                            <Typography className="element-title">
                                              {element.type === 'prompt'
                                                ? t(
                                                    'create.workflowCreator.dropArea.variables.promptTitle'
                                                  )
                                                : t(
                                                    'create.workflowCreator.dropArea.variables.displayTitle'
                                                  )}
                                            </Typography>
                                            {element.type === 'output' && (
                                              <Typography className="element-subtitle">
                                                {t(
                                                  'create.workflowCreator.dropArea.variables.displaySubtitle'
                                                )}
                                              </Typography>
                                            )}
                                          </Box>

                                          <Box 
                                            className="prompt-elements"
                                            onMouseDown={(e) => e.stopPropagation()}
                                            onTouchStart={(e) => e.stopPropagation()}
                                          >
                                            {getAvailableVariables(index).map((variable) => (
                                              <Box
                                                key={variable.key}
                                                component="span"
                                                href="#"
                                                draggable="true"
                                                className="prompt-label"
                                                data-target={element.type}
                                                data-key={variable.key}
                                                onDragStart={(e) => {
                                                  e.stopPropagation();
                                                  handleVariableDragStart(e, variable.value);
                                                }}
                                                onMouseDown={(e) => e.stopPropagation()}
                                                onTouchStart={(e) => e.stopPropagation()}
                                              >
                                                {`{${variable.value.replace(/\s+/g, '_').toLowerCase()}} `}
                                              </Box>
                                            ))}
                                          </Box>

                                          <Box className="drop-area-form-editor-section">
                                            <Box
                                              className="drop-area-form-editor-section"
                                              component="label"
                                              htmlFor={`${element.type}_${element.id}`}
                                            >
                                              {element.type === 'prompt' && (
                                                <Typography className="element-title drop-area-form-editor-title">
                                                  {t('create.workflowCreator.dropArea.form.prompt')}
                                                  <span className="drop-area-form-required-asterisk">
                                                    *
                                                  </span>
                                                  :
                                                </Typography>
                                              )}
                                              <Editor
                                                key={`editor-${element.id}-${droppedElements.indexOf(element)}`}
                                                id={`${element.type}_${element.id}`}
                                                tinymceScriptSrc="/tinymce/tinymce.min.js"
                                                init={{
                                                  height: 200,
                                                  branding: false,
                                                  statusbar: false,
                                                  elementpath: false,
                                                  menubar: false,
                                                  menu: false,
                                                  toolbar_mode: 'wrap',
                                                  toolbar_sticky: false,
                                                  contextmenu: false,
                                                  quickbars: false,
                                                  plugins: [
                                                    'lists',
                                                    'advlist',
                                                    'wordcount',
                                                    'noneditable',
                                                  ],
                                                  toolbar: 'bold italic | undo redo',
                                                  protect: false,
                                                  convert_urls: false,
                                                  relative_urls: false,
                                                  remove_script_host: false,
                                                  extended_valid_elements: 'a[*]',
                                                  custom_elements: '~a',
                                                  valid_elements: '*[*]',
                                                  valid_children: '+body[a]',
                                                  allow_script_urls: true,
                                                  allow_conditional_comments: true,
                                                  allow_html_in_named_anchor: true,
                                                  link_assume_external_targets: false,
                                                  target_list: false,
                                                  link_title: false,
                                                  link_context_toolbar: false,
                                                  default_link_target: '_self',
                                                  formats: {
                                                    custom_format: {
                                                      inline: 'a',
                                                      classes: 'custom-label',
                                                      attributes: {
                                                        style:
                                                          'background-color: #dddddd; border: 1px solid #cccccc; padding: 2px 5px; display: inline-block; cursor: move; margin: 0 2px; border-radius: 4px; text-decoration: none; color: #333333;',
                                                        contenteditable: 'false',
                                                        draggable: 'true',
                                                        href: '#',
                                                      },
                                                    },
                                                  },
                                                  content_style: `
                                  body { font-size: 12px; }
                                  .custom-label {
                                    background-color: #dddddd !important;
                                    border: 1px solid #cccccc !important;
                                    padding: 5px 5px !important;
                                    display: inline-block !important;
                                    cursor: move !important;
                                    margin: 2px 2px !important;
                                    border-radius: 4px !important;
                                    text-decoration: none !important;
                                    color: #333333 !important;
                                    font-size: 11px !important;
                                  }
                                `,
                                                  setup: (editor) => {
                                                    editor.on('drop', (e) =>
                                                      handleEditorDrop(editor, e)
                                                    );

                                                    editor.on('dragstart', (e) => {
                                                      if (
                                                        e.target.classList.contains('custom-label')
                                                      ) {
                                                        const dragPreview =
                                                          e.target.cloneNode(true);
                                                        document.body.appendChild(dragPreview);
                                                        e.dataTransfer.setDragImage(
                                                          dragPreview,
                                                          0,
                                                          0
                                                        );

                                                        // Taşınan elementin uid'sini sakla
                                                        const sourceUid =
                                                          e.target.getAttribute('data-uid');
                                                        if (sourceUid) {
                                                          e.dataTransfer.setData(
                                                            'source-uid',
                                                            sourceUid
                                                          );
                                                        }

                                                        // HTML içeriğini de sakla
                                                        e.dataTransfer.setData(
                                                          'text/html',
                                                          e.target.outerHTML
                                                        );

                                                        setTimeout(
                                                          () =>
                                                            document.body.removeChild(dragPreview),
                                                          0
                                                        );
                                                      }
                                                    });

                                                    editor.on('dragend', (e) => {
                                                      if (
                                                        e.target.classList.contains('custom-label')
                                                      ) {
                                                        e.preventDefault();
                                                      }
                                                    });

                                                    editor.on('click', (e) => {
                                                      if (
                                                        e.target.classList.contains('custom-label')
                                                      ) {
                                                        e.preventDefault();
                                                      }
                                                    });
                                                  },
                                                }}
                                                onInit={(_, editor) =>
                                                  handleEditorInit(editor, element.id)
                                                }
                                                value={
                                                  element.type === 'prompt'
                                                    ? element.promptContent
                                                    : element.detail
                                                }
                                                onEditorChange={(content) => {
                                                  handleContentChange(element.id, content);
                                                }}
                                              />
                                            </Box>
                                          </Box>
                                        </>
                                      )}
                                    </Box>
                                  </Box>
                                </Box>
                                {index < sortedArray.length - 1 && (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'flex-start',
                                      justifyContent: 'center',

                                      height: '100%',
                                      flexShrink: 0,
                                      marginLeft: '8px',
                                      paddingTop: '80px',
                                    }}
                                  >
                                    <ArrowIcon />
                                  </Box>
                                )}
                              </Box>
                            </Box>
                          )}
                        </Draggable>
                      ))}
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

WorkflowDropArea.propTypes = {
  droppedElements: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      icon: PropTypes.string.isRequired,
      input: PropTypes.string,
      detail: PropTypes.string,
      outputName: PropTypes.string,
      promptContent: PropTypes.string,
      content: PropTypes.string,
    })
  ).isRequired,
  onDrop: PropTypes.func.isRequired,
  onDragOver: PropTypes.func.isRequired,
  onDeleteElement: PropTypes.func.isRequired,
  onElementInputChange: PropTypes.func.isRequired,
  onElementDetailChange: PropTypes.func.isRequired,
  onOutputNameChange: PropTypes.func.isRequired,
};

export default WorkflowDropArea;
