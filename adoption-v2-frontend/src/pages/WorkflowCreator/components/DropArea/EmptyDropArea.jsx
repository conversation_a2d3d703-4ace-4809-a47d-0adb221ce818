import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const EmptyDropArea = () => {
  const { t } = useTranslation();

  return (
    <Box className="drop-area-empty">
      <svg fill="none" height="64" viewBox="0 0 64 64" width="64">
        <g clipPath="url(#clip0_916_79)">
          <path
            d="M42.6667 34.6667L61.2374 45.4987L53.3094 47.7653L58.976 57.5813L54.3574 60.248L48.6907 50.4347L42.7627 56.168L42.6667 34.6667ZM37.3334 16H42.6667V21.3333H56C56.7073 21.3333 57.3856 21.6143 57.8857 22.1144C58.3858 22.6145 58.6667 23.2928 58.6667 24V34.6667H53.3334V26.6667H26.6667V53.3333H37.3334V58.6667H24C23.2928 58.6667 22.6145 58.3857 22.1144 57.8856C21.6143 57.3855 21.3334 56.7072 21.3334 56V42.6667H16V37.3333H21.3334V24C21.3334 23.2928 21.6143 22.6145 22.1144 22.1144C22.6145 21.6143 23.2928 21.3333 24 21.3333H37.3334V16ZM10.6667 37.3333V42.6667H5.33337V37.3333H10.6667ZM10.6667 26.6667V32H5.33337V26.6667H10.6667ZM10.6667 16V21.3333H5.33337V16H10.6667ZM10.6667 5.33333V10.6667H5.33337V5.33333H10.6667ZM21.3334 5.33333V10.6667H16V5.33333H21.3334ZM32 5.33333V10.6667H26.6667V5.33333H32ZM42.6667 5.33333V10.6667H37.3334V5.33333H42.6667Z"
            fill="#677788"
          />
        </g>
        <defs>
          <clipPath id="clip0_916_79">
            <rect fill="white" height="64" width="64" />
          </clipPath>
        </defs>
      </svg>
      <Typography className="text--subtitle drop-area-empty-typography">
        {t('create.workflowCreator.dropArea.emptyState')}
      </Typography>
    </Box>
  );
};

export default EmptyDropArea;
