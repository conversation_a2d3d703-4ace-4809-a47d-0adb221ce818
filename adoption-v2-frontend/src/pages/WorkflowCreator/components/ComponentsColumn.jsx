import PropTypes from 'prop-types';
import { Box, Typography, Paper, Tooltip, IconButton } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { Droppable, Draggable } from '@hello-pangea/dnd';
import '../WorkflowCreator.scss';

const ComponentsColumn = ({ onComponentClick }) => {
  const { t } = useTranslation();

  // Mobile device check - responsive
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Component click handler
  const handleComponentClick = (template, e) => {
    // Add by clicking on mobile, drag and drop on desktop
    if (isMobile) {
      e.preventDefault();
      onComponentClick(template);
    }
  };

  // Define components data
  const components = [
    {
      id: 'text-input',
      template: 'text',
      title: t('create.workflowCreator.components.textInput'),
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/text-input.svg',
      tooltip: t('create.workflowCreator.componentsColumn.tooltips.textInput'),
    },
    {
      id: 'display',
      template: 'output',
      title: t('create.workflowCreator.components.display'),
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/output.svg',
      tooltip: t('create.workflowCreator.componentsColumn.tooltips.display'),
    },
    {
      id: 'prompt',
      template: 'prompt',
      title: t('create.workflowCreator.components.prompt'),
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
      tooltip: t('create.workflowCreator.componentsColumn.tooltips.prompt'),
    },
  ];

  return (
    <Box className="workflow-creator-components-column">
      <Box className="components-column-container">
        <Paper id="components" elevation={0} className="components-column-paper">
          <Typography className="components-column-title">
            {t('create.workflowCreator.componentsColumn.title')}
            {isMobile && (
              <Typography component="span" className="components-column-mobile-hint">
                {t('create.workflowCreator.componentsColumn.mobileHint', 'Tap to add')}
              </Typography>
            )}
          </Typography>
          <Droppable droppableId="components-area" isDropDisabled={true} renderClone={(provided, _, rubric) => {
            const component = components[rubric.source.index];
            return (
              <Box
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                component="a"
                className={`component component-element ${component.template === 'prompt' ? 'full-width' : ''}`}
                data-template={component.template}
                sx={{
                  cursor: 'grabbing',
                  opacity: 0.9,
                  transform: 'rotate(3deg) scale(1.1)',
                  zIndex: 1000,
                  position: 'relative',
                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)',
                }}
              >
                <Box
                  component="img"
                  className="component-img"
                  src={component.icon}
                  alt={component.title}
                />
                <Typography className="component-text">
                  {component.title}
                </Typography>
              </Box>
            );
          }}>
            {(provided) => (
              <Box 
                className="item-list"
                ref={provided.innerRef}
                {...provided.droppableProps}
              >
                {components.map((component, index) => (
                  <Draggable
                    key={component.id}
                    draggableId={component.id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <Box
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        component="a"
                        className={`component component-element ${component.template === 'prompt' ? 'full-width' : ''}`}
                        onClick={(e) => handleComponentClick(component.template, e)}
                        data-template={component.template}
                        sx={{
                          cursor: isMobile ? 'pointer' : 'grab',
                          opacity: snapshot.isDragging ? 0.5 : 1,
                          '&:active': {
                            cursor: isMobile ? 'pointer' : 'grabbing',
                          },
                          position: 'relative',
                        }}
                      >
                        <Box
                          component="img"
                          className="component-img"
                          src={component.icon}
                          alt={component.title}
                        />
                        <Typography className="component-text">
                          {component.title}
                        </Typography>
                        <Box sx={{ position: 'absolute', top: 0, right: 0 }}>
                          <Tooltip
                            title={component.tooltip}
                            placement="right"
                            arrow
                          >
                            <IconButton size="small" className="components-column-tooltip-icon">
                              <InfoOutlinedIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </Box>
            )}
          </Droppable>
        </Paper>
      </Box>
    </Box>
  );
};

ComponentsColumn.propTypes = {
  onComponentClick: PropTypes.func.isRequired,
};

export default ComponentsColumn;
