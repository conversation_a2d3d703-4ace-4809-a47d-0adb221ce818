@use '../../styles/abstracts/variables'as *;

// Base styles
.inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

// Common container styles
.container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  background-color: #fff;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }

  &--paper {
    padding: 24px;
  }

  &--drop-area {
    padding: 24px;
    margin-bottom: 24px;
    border-radius: 8px;
    background-color: transparent;
  }
}

// Typography styles
.text {
  &--title {
    font-size: 14px;
    font-weight: 600;
    color: #677788;
    margin-bottom: 12px;
    transition: color 0.3s ease;

    &:hover {
      color: #1976d2;
    }
  }

  &--subtitle {
    font-size: 12px;
    font-weight: 500;
    color: #677788;
    margin-bottom: 4px;
  }

  &--small {
    font-size: 11px;
    margin-bottom: 8px;
    word-break: break-word;
    white-space: normal;
  }
}

// Component styles
.component {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; // Add vertical centering
  padding: 12px;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  min-height: 80px; // Add minimum height

  // Mobile and tablet responsive
  @media (max-width: 768px) {
    padding: 10px 8px;
    min-height: 70px;

    // Click feedback on mobile
    &:active {
      transform: scale(0.95);
      box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2) !important;
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(25, 118, 210, 0.04);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
  }

  &:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(25, 118, 210, 0.3);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.full-width {
    grid-column: 1 / -1;
  }
}

.component-element {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  padding: 24px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(25, 118, 210, 0.3);
  }

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(25, 118, 210, 0.04);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
  }

  &:hover::before {
    opacity: 1;
  }
}

// Settings styles
.settings {
  &-box {
    margin-bottom: 10px;
    padding: 6px;
    border-radius: 8px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      margin-bottom: 0px;
    }
  }

  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
  }

  &-label {
    display: flex;
    align-items: center;
    cursor: help;
    font-weight: 500;
    font-size: 12px;
  }

  &-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 10px;
    border-radius: 8px;
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
  }
}

.item-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto;
  gap: 12px;
  margin-top: 12px;

  // Mobile responsive - single column
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  // Tablet responsive - 2 columns (text input and display side by side 50%)
  @media (min-width: 577px) and (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}

// Workflow column styles
.workflow-column {
  transition: all 0.3s ease;

  &:hover .drop-area {
    border-color: rgba(25, 118, 210, 0.4);
  }
}

// Drop area styles
.drop-area {
  flex: 1;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #f8f9fa;
  min-height: 500px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  transition: border-color 0.3s ease, background-color 0.3s ease;

  &:hover {
    border-color: rgba(25, 118, 210, 0.4);
    background-color: rgba(25, 118, 210, 0.01);
  }

  // Mobile responsive
  @media (max-width: 768px) {
    min-height: 300px;
    padding: 0px;
    overflow-x: visible;
    white-space: normal;
  }

  &-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #677788;
    height: 100%;
    min-height: 400px;
    transition: all 0.3s ease;

    @media (max-width: 768px) {
      min-height: 250px;
    }

    svg {
      width: 48px;
      height: 48px;
      transition: opacity 0.3s ease;
      opacity: 0.7;

      @media (max-width: 768px) {
        width: 36px;
        height: 36px;
      }
    }

    &:hover svg {
      opacity: 1;
    }
  }

  &-container {
    display: flex;
    justify-content: start;
    align-items: start;
    min-width: min-content;

    @media (max-width: 768px) {
      flex-direction: row;
      min-width: auto;
      width: 100%;
      overflow-x: auto;
    }
  }
}

// Element styles
.drop-element {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 2px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  width: 100%;
  max-width: 300px;

  &:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(25, 118, 210, 0.3);
  }

  &:last-child {
    margin-bottom: 0;
    margin-right: 0;
  }

  // Mobile responsive
  @media (max-width: 768px) {
    max-width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
  }

 

  &.dragging {
    opacity: 0.7;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  &.drag-over {
    border-top: 2px solid #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.3);
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(25, 118, 210, 0.03);
    pointer-events: none;
  }

  &:hover::before {
    opacity: 1;
  }
}

.unsortable {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  width: 100%;
  max-width: 300px;

  // Mobile responsive
  @media (max-width: 768px) {
    max-width: 100%;
  }

  &:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(25, 118, 210, 0.3);

    .element-inner {
      background-color: rgba(25, 118, 210, 0.03) !important;
    }

    .element-subtitle {
      color: #677788;
    }
  }
}

.element {
  &-headline {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    gap: 5px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba(25, 118, 210, 0.05);
    }
  }

  &-icon {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
  }

  &-title {
    font-size: 12px;
    font-weight: 500;
    color: #677788;
    transition: color 0.3s ease;

    .workflow-element:hover & {
      color: #1976d2;
    }
  }

  &-inner {
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    transition: background-color 0.3s ease;
    background-color: #fff;
  }

  &-subtitle {
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 11px !important;
    word-break: break-word;
    white-space: normal;
    transition: color 0.3s ease;

    & span {
      color: '#CE001C';
    }
  }
}

.delete-button {
  margin-left: auto;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  color: #ce001c;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;

  &:hover {
    background-color: #fff3f5 !important;
    color: #ce001c !important;
    box-shadow: 0 2px 4px rgba(206, 0, 28, 0.1);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

// Form elements
.form {
  &-field {
    width: 100%;

    &--small {
      margin-bottom: 8px;
    }

    &--input {
      & .MuiInputBase-root {
        height: 34px;
      }
    }
  }

  &-label {
    display: block;
    margin-bottom: 4px;
  }
}

.prompt-elements {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 16px;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.prompt-label {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 6px 6px;
  font-size: 11px;
  color: #495057;
  text-decoration: none;
  cursor: move;

  &:hover {
    background-color: #e9ecef;
  }
}

.numberInput {
  width: 50px;
  display: flex;
  justify-content: center;

  & input {
    padding: 2px;
    margin: auto;
    margin-left: 9px;
    text-align: center;
    font-size: 12px;
  }
}

.number-display {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 6px 0px;
  width: 50px;
  text-align: center;
  font-weight: 500;
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.04);
  transition: all 0.3s ease;
  font-size: 12px;

  &:hover {
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
  }
}

.rangeInput {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: #e0e0e0;
  outline: none;
  border-radius: 3px;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #1976d2;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
    }
  }

  &::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #1976d2;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
    }
  }
}

.parameter-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  background-color: #fafafa;
}

.component-img {
  width: 32px;
  height: 32px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  flex-shrink: 0; // Prevent image from shrinking

  // Mobile responsive
  @media (max-width: 768px) {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
  }
}

.component-text {
  font-size: 12px !important;
  text-align: center;
  font-weight: 500;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
  flex-shrink: 0; // Prevent text from shrinking
  word-wrap: break-word; // Break long words
  hyphens: auto; // Automatic hyphenation
  max-width: 100%; // Don't exceed container width

  // Mobile responsive
  @media (max-width: 768px) {
    font-size: 11px;
    line-height: 1.2;
  }
}

.component:hover .component-text {
  color: #1976d2;
}

// Workflow Header styles
.workflow-header {
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 24px;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);
  }

  &-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0px auto;

    // Mobile and tablet responsive
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      padding: 16px;
      align-items: stretch;
    }
  }

  &-input-section {
    width: 25%;
    padding: 24px;

    @media (max-width: 768px) {
      width: 100%;
      padding: 0;
    }
  }

  &-button-group {
    display: flex;
    gap: 12px;
    margin-right: 24px;

    @media (max-width: 768px) {
      margin-right: 0;
      justify-content: center;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  &-outline-button {
    color: #1976d2 !important;
    border-color: #1976d2 !important;
    transition: all 0.3s ease !important;
    padding: 4px 10px !important;
    height: 44px !important;
    text-transform: none !important;

    &:hover {
      background-color: rgba(25, 118, 210, 0.04) !important;
      box-shadow: 0 4px 8px rgba(25, 118, 210, 0.15) !important;
    }
  }

  &-contained-button {
    background-color: #1976d2 !important;
    color: #fff !important;
    border-color: #1976d2 !important;
    transition: all 0.3s ease !important;
    padding: 4px 10px !important;
    height: 44px !important;
    text-transform: none !important;
    font-size: 14px !important;

    &:hover {
      background-color: #1565c0 !important;
      box-shadow: 0 4px 8px rgba(25, 118, 210, 0.25) !important;
    }
  }
}

// Custom styles for the TextField in the header
.MuiTextField-root {
  .MuiInputBase-root {
    transition: all 0.3s ease;

    & input {
      font-size: 12px;
      padding: 8px 12px;
    }

    &:hover .MuiOutlinedInput-notchedOutline {
      border-color: rgba(25, 118, 210, 0.5);
    }

    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    }
  }
}

// Custom styles for buttons
.MuiButton-contained {
  transition: all 0.3s ease !important;
  font-size: 14px !important;
  padding: 4px 10px !important;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2) !important;
  text-transform: none !important;

  &:hover {
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.25) !important;
  }
}

.MuiButton-outlined {
  font-size: 14px !important;
  padding: 4px 10px !important;
  text-transform: none !important;
}

// Custom tooltip styles
.MuiTooltip-tooltip {
  background-color: rgba(33, 33, 33, 0.95) !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  max-width: 300px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

// Custom styles for the editor
.tox-tinymce {
  border-radius: 8px !important;
  border-color: #e0e0e0 !important;
  transition: all 0.3s ease !important;

  &:hover {
    border-color: rgba(25, 118, 210, 0.3) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  }
}

.custom-label {
  transition: all 0.2s ease !important;

  &:hover {
    background-color: #c8e1ff !important;
    border-color: #1976d2 !important;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.15) !important;
  }
}


.workflow-creator-container {
  background-color: transparent !important;
  box-shadow: none !important;
  padding-left: $spacing-3 !important;
  padding-right: $spacing-4 !important;

  @media (min-width: 769px) {
    padding-left: $spacing-4 !important;
    padding-right: $spacing-4 !important;
  }

  &>.MuiContainer-root {
    background-color: transparent !important;
    box-shadow: none !important;
  }

  .MuiPaper-root {
    background-color: $bg-paper;
  }
}

// Ensure the drop elements have the correct width
.workflow-element {
  min-width: 300px;
  box-sizing: border-box;

  // Mobile responsive
  @media (max-width: $tablet) {
    min-width: 100%;
    width: 100%;
  }
}

// Main WorkflowCreator page layout styles
.workflow-creator-main-layout {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: $spacing-4;

  @media (max-width: $tablet) {
    grid-template-columns: 1fr;
    gap: $spacing-3;
  }

  @media (min-width: 769px) and (max-width: 992px) {
    gap: 20px;
  }
}

.workflow-creator-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.workflow-creator-components-column {
  grid-column: 1 / 2;
  order: 1;

  @media (max-width: $tablet) {
    grid-column: 1;
    order: 1;
  }
}

.workflow-creator-workflow-column {
  grid-column: 2 / 7;
  order: 2;

  @media (max-width: $tablet) {
    grid-column: 1;
    order: 2;
  }
}

.workflow-creator-empty-space {
  grid-column: 1 / 2;
  order: 3;

  @media (max-width: $tablet) {
    display: none;
  }
}

.workflow-creator-parameter-settings {
  grid-column: 2 / 7;
  order: 3;

  @media (max-width: $tablet) {
    grid-column: 1;
    order: 3;
  }
}

// ComponentsColumn styles
.components-column {
  &-container {
    width: 100%;
    max-width: 100%;

    @media (min-width: 769px) {
      width: 260px;
    }
  }

  &-paper {
    background-color: $bg-paper;
    padding: $spacing-3;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: $shadow-sm;
    }

    @media (min-width: 769px) {
      padding: $spacing-3;
    }
  }

  &-title {
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    margin-bottom: $spacing-3;
    color: $primary-text-color;
  }

  &-mobile-hint {
    display: block;
    font-size: 10px;
    font-weight: $font-weight-regular;
    color: $primary-color;
    margin-top: $spacing-1;
    font-style: italic;
  }

  &-tooltip-icon {
    position: absolute;
    top: $spacing-1;
    right: $spacing-1;
    padding: 0;
    transition: color 0.2s ease;

    &:hover {
      color: $primary-color;
    }

    .MuiSvgIcon-root {
      font-size: $font-size-xs;
    }
  }
}

// WorkflowHeader styles
.workflow-header-input {
  .MuiOutlinedInput-root {
    height: 44px;

    &.Mui-error fieldset {
      border-color: #d32f2f;
    }
  }
}

.workflow-header-button {
  min-width: auto;
  font-size: $font-size-xs;

  @media (min-width: $mobile) {
    min-width: 120px;
    font-size: $font-size-sm;
  }
}

// WorkflowColumn styles
.workflow-column-container {
  width: 100%;
  transition: all 0.3s ease;
}

// ParameterSettings styles
.parameter-settings {
  &-tooltip-icon {
    margin-left: $spacing-1;
    padding: 0;
    transition: color 0.2s ease;

    &:hover {
      color: $primary-color;
    }

    .MuiSvgIcon-root {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }
}

// WorkflowDropArea styles
.drop-area-empty-typography {
  margin-top: $spacing-2;
}

.drop-area-container-padding {
  padding: $spacing-2;
}

.drop-area-form {
  &-input-section {
    margin: $spacing-3 0;
  }

  &-output-name-section {
    margin-top: $spacing-3;
    margin-bottom: $spacing-2;
  }

  &-content-section {
    margin-top: $spacing-3;
    margin-bottom: $spacing-2;

    &--prompt {
      margin-top: $spacing-3;
    }
  }

  &-editor-section {
    margin-top: $spacing-3;

  }

  &-editor-title {
    margin-bottom: $spacing-2;
  }

  &-required-asterisk {
    color: $error-color;
  }
}

// WorkflowCreator main section styles
.workflow-creator {
  overflow: hidden;
}

// Loading dialog styles
.workflow-creator-loading-dialog {
  text-align: center;
  padding: $spacing-5 0;

  .MuiCircularProgress-root {
    margin-bottom: $spacing-4;
  }
}