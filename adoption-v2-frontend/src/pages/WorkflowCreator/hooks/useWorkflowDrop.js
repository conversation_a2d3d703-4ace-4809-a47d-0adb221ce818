import { useCallback } from 'react';

const useWorkflowDrop = (droppedElements, setDroppedElements, setAlert, t) => {
  const findNearestInsertionPoint = useCallback(
    (dropY, sourceIndex = null) => {
      const workflowElements = Array.from(document.querySelectorAll('.workflow-element'));

      if (workflowElements.length === 0) return -1;

      let nearestIndex = 0;
      let minDistance = Infinity;

      workflowElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const elementCenterY = rect.top + rect.height / 2;
        const distance = Math.abs(dropY - elementCenterY);

        if (distance < minDistance) {
          minDistance = distance;
          nearestIndex = index;
        }
      });

      // Check if we should insert before or after the nearest element
      const nearestElement = workflowElements[nearestIndex];
      const nearestRect = nearestElement.getBoundingClientRect();
      const nearestCenterY = nearestRect.top + nearestRect.height / 2;

      let insertIndex = nearestIndex;
      if (dropY > nearestCenterY) {
        insertIndex = nearestIndex + 1;
      }

      // Special handling for display element positioning
      if (insertIndex >= droppedElements.length || nearestIndex === droppedElements.length - 1) {
        // If inserting at the end or nearest element is the last element
        const lastElement = droppedElements[droppedElements.length - 1];

        if (lastElement && lastElement.type === 'output') {
          // If last element is display, insert before it
          insertIndex = droppedElements.length - 1;
        } else {
          // If last element is not display, insert at the end
          insertIndex = droppedElements.length;
        }
      }

      // Adjust for source element removal if this is a reorder operation
      if (sourceIndex !== null && sourceIndex < insertIndex) {
        insertIndex--;
      }

      return insertIndex;
    },
    [droppedElements]
  );

  const handleElementReorder = useCallback(
    (e, elementId) => {
      const sourceIndex = droppedElements.findIndex((el) => el.id === elementId);
      const sourceElement = droppedElements[sourceIndex];
      const targetElement = e.target.closest('.workflow-element');

      if (targetElement) {
        const targetId = targetElement.id;
        const targetIndex = droppedElements.findIndex((el) => el.id === targetId);

        if (sourceIndex === targetIndex) return;

        const newElements = [...droppedElements];
        const [removed] = newElements.splice(sourceIndex, 1);
        newElements.splice(targetIndex, 0, removed);
        setDroppedElements(newElements);
      } else {
        // Find the nearest insertion point
        const dropY = e.clientY;
        const insertIndex = findNearestInsertionPoint(dropY, sourceIndex, sourceElement?.type);

        if (insertIndex === -1 || sourceIndex === insertIndex) return;

        const newElements = [...droppedElements];
        const [removed] = newElements.splice(sourceIndex, 1);
        newElements.splice(insertIndex, 0, removed);
        setDroppedElements(newElements);
      }
    },
    [droppedElements, setDroppedElements, findNearestInsertionPoint]
  );

  const handleNewElementDrop = useCallback(
    (e, template) => {
      const newElement = {
        id:
          template === 'output'
            ? `${template}_${999999999 + Date.now()}`
            : `${template}_${Date.now()}`,
        type: template === 'text' ? 'input' : template,
        title:
          template === 'text'
            ? t('create.workflowCreator.components.textInput')
            : template === 'output'
              ? t('create.workflowCreator.components.display')
              : t('create.workflowCreator.components.prompt'),
        icon: `https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/${
          template === 'output'
            ? 'output.svg'
            : template === 'prompt'
              ? 'prompt.svg'
              : 'text-input.svg'
        }`,
        input: '',
        detail: '',
        outputName: '',
        promptContent: '',
        inputs:
          template === 'text'
            ? [
                { inputType: 'text', name: 'input-key', value: '' },
                { inputType: 'text', name: 'input-value', value: '' },
              ]
            : [],
      };

      const targetElement = e.target.closest('.workflow-element');

      if (targetElement) {
        const targetId = targetElement.id;
        const targetIndex = droppedElements.findIndex((el) => el.id === targetId);
        const newElements = [...droppedElements];
        newElements.splice(targetIndex, 0, newElement);
        setDroppedElements(newElements);
      } else if (droppedElements.length === 0) {
        // If no elements exist yet, allow adding the first element
        setDroppedElements([newElement]);
      } else {
        // If elements exist, find the nearest insertion point
        const dropY = e.clientY;
        const elementType = template === 'text' ? 'input' : template;
        const insertIndex = findNearestInsertionPoint(dropY, null, elementType);

        if (insertIndex === -1) {
          setDroppedElements([newElement]);
          return;
        }

        const newElements = [...droppedElements];
        newElements.splice(insertIndex, 0, newElement);
        setDroppedElements(newElements);
      }
    },
    [droppedElements, setDroppedElements, findNearestInsertionPoint, t]
  );

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      const template = e.dataTransfer.getData('template');
      const elementId = e.dataTransfer.getData('elementId');

      // Check if there's already a display element
      const hasDisplayElement = droppedElements.some((element) => element.type === 'output');

      if (template === 'output' && hasDisplayElement) {
        setAlert({
          open: true,
          message: t('create.workflowCreator.alerts.maxDisplay'),
          severity: 'warning',
        });
        return;
      }

      if (elementId) {
        handleElementReorder(e, elementId);
        return;
      }

      if (template) {
        handleNewElementDrop(e, template);
      }
    },
    [droppedElements, setAlert, t, handleElementReorder, handleNewElementDrop]
  );

  return { handleDrop };
};

export default useWorkflowDrop;
