import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const useWorkflowDataLoader = ({
  isEditMode,
  workflowData,
  setAppName,
  setDroppedElements,
  setSettings,
}) => {
  const { t } = useTranslation();

  useEffect(() => {
    if (isEditMode && workflowData?.data) {
      // Set app name
      setAppName(workflowData.data.title || '');
      localStorage.setItem('appName', JSON.stringify(workflowData.data.title || ''));

      // Transform workflow_form to droppedElements format
      const elements = workflowData.data.workflow_form.map((item, index) => {
        const elementType = item.type;
        const elementId = `${elementType}_${Date.now() + index}`;

        return {
          id: elementId,
          type: elementType,
          title:
            elementType === 'input'
              ? t('create.workflowCreator.components.textInput')
              : elementType === 'output'
                ? t('create.workflowCreator.components.display')
                : t('create.workflowCreator.components.prompt'),
          icon: `https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/${
            elementType === 'output'
              ? 'output.svg'
              : elementType === 'prompt'
                ? 'prompt.svg'
                : 'text-input.svg'
          }`,
          input: item.label || '',
          detail: elementType === 'input' || elementType === 'output' ? item.value || '' : '',
          outputName: elementType === 'prompt' ? item.name || '' : '',
          promptContent: elementType === 'prompt' ? item.value || '' : '',
          inputs:
            elementType === 'input'
              ? [
                  { inputType: 'text', name: 'input-key', value: item.label || '' },
                  { inputType: 'text', name: 'input-value', value: item.value || '' },
                ]
              : [],
        };
      });

      setDroppedElements(elements);
      localStorage.setItem('dropElementsData', JSON.stringify(elements));

      // Set settings if available
      if (
        workflowData.data.temperature ||
        workflowData.data.top_p ||
        workflowData.data.presence_penalty ||
        workflowData.data.frequency_penalty
      ) {
        setSettings({
          temperature: workflowData.data.temperature || '1',
          topP: workflowData.data.top_p || '1',
          presencePenalty: workflowData.data.presence_penalty || '0',
          frequencyPenalty: workflowData.data.frequency_penalty || '0',
        });
      }
    }
  }, [isEditMode, workflowData, setDroppedElements, setSettings, t, setAppName]);
};

export default useWorkflowDataLoader;