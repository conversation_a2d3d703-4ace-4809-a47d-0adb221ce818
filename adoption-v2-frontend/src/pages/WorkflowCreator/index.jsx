import { useState, useEffect, useCallback } from 'react';
import { Box, Container, Dialog, DialogContent, CircularProgress, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { initGlobalLinkHandler } from './utils/linkHandler';
import { useSelector } from 'react-redux';
import { useGetJourneyTrackingQuery } from '../../redux/services/journey-api';
import useUpdateJourneyTracking from '../../domains/journey/utils/updateJourneyTracking';
import { useTranslation } from 'react-i18next';
import { DragDropContext } from '@hello-pangea/dnd';
import useWorkflowDrop from './hooks/useWorkflowDrop';
import useWorkflowDataLoader from './hooks/useWorkflowDataLoader';

import './WorkflowCreator.scss';
import WorkflowHeader from './components/WorkflowHeader';
import ComponentsColumn from './components/ComponentsColumn';
import WorkflowDropArea from './components/DropArea/WorkflowDropArea';
import ParameterSettings from './components/ParameterSettings';
import AlertSnackbar from './components/AlertSnackbar';

import { useWorkflowState } from '../../hooks/useWorkflowState';
import {
  useSaveAndRunWorkflowMutation,
  useGetWorkflowByIdQuery,
} from '../../redux/services/create-workflow';
import { getSampleElements } from './sample-workflow';
import { developmentLogs } from '../../utils/developmentLogs';

const WorkflowCreatorPage = () => {
  const { droppedElements, setDroppedElements, settings, setSettings } = useWorkflowState();
  const location = useLocation();
  const { t, i18n } = useTranslation();

  const user = useSelector((state) => state.auth.user);

  const journeyCardId = location.state?.cardId;

  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const { data: trackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  // Initialize the global link handler when the component mounts
  useEffect(() => {
    initGlobalLinkHandler();
  }, []);

  const queryParams = new URLSearchParams(location.search);
  const isEditMode = queryParams.get('workflow_action') === 'edit';
  const workflowName = queryParams.get('workflow_name');
  const workflowId = queryParams.get('workflow_id');

  // Fetch workflow data if in edit mode
  const { data: workflowData, isLoading: isLoadingWorkflow } = useGetWorkflowByIdQuery(
    workflowName,
    {
      skip: !isEditMode || !workflowName,
    }
  );

  const [alert, setAlert] = useState({
    open: false,
    message: '',
    severity: 'warning',
  });

  // Initialize the workflow drop hook
  const { handleDrop } = useWorkflowDrop(droppedElements, setDroppedElements, setAlert, t);

  const [appName, setAppName] = useState(() => {
    const saved = localStorage.getItem('appName');
    return saved ? JSON.parse(saved) : '';
  });

  const [saveAndRunWorkflow] = useSaveAndRunWorkflowMutation();

  const [isCreating, setIsCreating] = useState(false);

  // Use the workflow data loader hook
  useWorkflowDataLoader({
    isEditMode,
    workflowData,
    setAppName,
    setDroppedElements,
    setSettings,
  });

  useEffect(() => {
    if (Array.isArray(droppedElements)) {
      localStorage.setItem('dropElementsData', JSON.stringify(droppedElements));
    }
  }, [droppedElements]);

  useEffect(() => {
    if (!isEditMode) {
      localStorage.setItem('appName', JSON.stringify(''));
    }
  }, [isEditMode]);


  // Function to add elements by clicking for mobile
  const handleComponentClick = useCallback(
    (template) => {
      // Check if there's already a display element
      const hasDisplayElement = droppedElements.some((element) => element.type === 'output');

      if (template === 'output' && hasDisplayElement) {
        setAlert({
          open: true,
          message: t('create.workflowCreator.alerts.maxDisplay'),
          severity: 'warning',
        });
        return;
      }

      const newElement = {
        id:
          template === 'output'
            ? `${template}_${999999999 + Date.now()}`
            : `${template}_${Date.now()}`,
        type: template === 'text' ? 'input' : template,
        title:
          template === 'text'
            ? t('create.workflowCreator.components.textInput')
            : template === 'output'
              ? t('create.workflowCreator.components.display')
              : t('create.workflowCreator.components.prompt'),
        icon: `https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/${
          template === 'output'
            ? 'output.svg'
            : template === 'prompt'
              ? 'prompt.svg'
              : 'text-input.svg'
        }`,
        input: '',
        detail: '',
        outputName: '',
        promptContent: '',
        inputs:
          template === 'text'
            ? [
                { inputType: 'text', name: 'input-key', value: '' },
                { inputType: 'text', name: 'input-value', value: '' },
              ]
            : [],
      };

      // Add element to list with display positioning logic
      setDroppedElements((prev) => {
        if (prev.length === 0) {
          return [newElement];
        }

        const lastElement = prev[prev.length - 1];
        if (lastElement && lastElement.type === 'output') {
          // If last element is display, insert before it
          const newElements = [...prev];
          newElements.splice(prev.length - 1, 0, newElement);
          return newElements;
        } else {
          // If last element is not display, insert at the end
          return [...prev, newElement];
        }
      });

      // Scroll to newly added element on mobile
      setTimeout(() => {
        const newElementDom = document.getElementById(newElement.id);
        if (newElementDom) {
          newElementDom.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest',
          });
        }
      }, 100); // Wait until DOM is updated
    },
    [droppedElements, setDroppedElements, t]
  );

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDeleteElement = (id) => {
    const newElements = droppedElements.filter((element) => element.id !== id);
    setDroppedElements(newElements);
  };

  const handleElementInputChange = (id, value) => {
    const newElements = droppedElements.map((element) =>
      element.id === id ? { ...element, input: value } : element
    );
    setDroppedElements(newElements);
  };

  const handleElementDetailChange = (id, value) => {
    const newElements = droppedElements.map((element) =>
      element.id === id
        ? {
            ...element,
            [element.type === 'prompt' ? 'promptContent' : 'detail']: value,
          }
        : element
    );
    setDroppedElements(newElements);
  };

  // Add new handler for output name changes
  const handleOutputNameChange = (id, value) => {
    const newElements = droppedElements.map((element) =>
      element.id === id ? { ...element, outputName: value } : element
    );
    setDroppedElements(newElements);
  };

  // Add handlers for new buttons
  const handleLoadSample = () => {
    // Prevent loading samples in edit mode
    if (isEditMode) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.noEditSample'),
        severity: 'warning',
      });
      return;
    }

    if (Array.isArray(droppedElements) && droppedElements.length > 0) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.removeExisting'),
        severity: 'warning',
      });
      return;
    }

    clearStorage();

    try {
      // Get sample workflow according to current language
      const currentLanguage = i18n.language;
      const elements = getSampleElements(currentLanguage);

      // Save to localStorage
      localStorage.setItem('appName', JSON.stringify('Sample Workflow'));
      localStorage.setItem('tinymce-custom-colors-forecolor', JSON.stringify([]));
      localStorage.setItem('tinymce-custom-colors-hilitecolor', JSON.stringify([]));
      localStorage.setItem('workflow-tour', JSON.stringify(true));
      localStorage.setItem('dropElementsData', JSON.stringify(elements));

      // Update state
      setDroppedElements(elements);
    } catch (error) {
      console.error('Error loading sample:', error);
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.sampleLoadError'),
        severity: 'error',
      });
    }
  };

  const handleSaveDraft = () => {
    if (droppedElements.length === 0) {
      // Show error message: "You must enter the workflow name and create a flow before you can save it."
      return;
    }
    // Implement save draft logic
    // Show success message: "Saved as a draft."
  };

  // Add handler for workflow name change
  const handleWorkflowNameChange = (value) => {
    setAppName(value);
    localStorage.setItem('appName', JSON.stringify(value));
  };

  // Add helper function to check for variables in prompt content
  const hasVariablesInPrompt = (promptContent) => {
    if (!promptContent) return false;

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = promptContent;
    return tempDiv.querySelectorAll('.custom-label').length > 0;
  };

  // Add helper function to check for empty input fields
  const hasEmptyInputs = (elements) => {
    return elements.some(
      (element) => element.type === 'input' && (!element.input || element.input.trim() === '')
    );
  };

  // Add helper function to check for empty output names in prompts
  const hasEmptyPromptOutputNames = (elements) => {
    return elements.some(
      (element) =>
        element.type === 'prompt' && (!element.outputName || element.outputName.trim() === '')
    );
  };

  // Update handleSaveAndRun to check for empty inputs
  const handleSaveAndRun = async () => {
    if (!appName.trim()) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.enterAppName'),
        severity: 'warning',
      });
      return;
    }

    if (droppedElements.length === 0) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.createWorkflow'),
        severity: 'error',
      });
      return;
    }

    // Check if there's a Display component at the end of the workflow
    const hasDisplayElement = droppedElements.some((element) => element.type === 'output');
    if (!hasDisplayElement) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.includeDisplay'),
        severity: 'warning',
      });
      return;
    }

    // Check for empty input fields
    if (hasEmptyInputs(droppedElements)) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.fillInputs'),
        severity: 'warning',
      });
      return;
    }

    // Check for empty output names in prompts
    if (hasEmptyPromptOutputNames(droppedElements)) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.fillOutputNames'),
        severity: 'warning',
      });
      return;
    }

    // Check if any Prompt component is empty or has no variables
    const invalidPrompt = droppedElements.find(
      (element) =>
        element.type === 'prompt' &&
        (!element.promptContent ||
          element.promptContent.trim() === '' ||
          !hasVariablesInPrompt(element.promptContent))
    );

    if (invalidPrompt) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.includeVariable'),
        severity: 'warning',
      });
      return;
    }

    try {
      setIsCreating(true); // Start loading

      // Transform droppedElements into workflow_form format
      const workflow_form = droppedElements.map((element) => ({
        type: element.type,
        label: element.input || element.title,
        name: element.outputName || element.input?.toLowerCase().replace(/\s+/g, '_'),
        value: element.detail || element.promptContent,
      }));

      const payload = {
        title: appName,
        workflow_form,
        temperature: settings.temperature || '1',
        top_p: settings.topP || '1',
        presence_penalty: settings.presencePenalty || '0',
        frequency_penalty: settings.frequencyPenalty || '0',
        userId: user._id,
      };

      // If in edit mode, include the workflow ID
      if (isEditMode) {
        // First priority: use the _id from the fetched data
        if (workflowData?.data?._id) {
          payload.id = workflowData.data._id;
        }
        // Second priority: use the workflowId from query params if available
        else if (workflowId) {
          payload.id = workflowId;
        }

        if (!payload.id) {
          throw new Error('No workflow ID available for update operation');
        }
      }

      const response = await saveAndRunWorkflow(payload).unwrap();

      // Update journey tracking - if cardId exists
      if (journeyCardId && user?._id && user?.journeyLevel?.name && trackingData) {
        try {
          // Use user's journey level
          const userLevel = user.journeyLevel.name.toLowerCase();

          // Update journey tracking
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: trackingData,
            userLevel: userLevel,
            cardId: journeyCardId,
          });

          // Refresh Redux cache - we save a timestamp to localStorage,
          // Footer component will detect this and refetch
          localStorage.setItem('journey_tracking_updated', Date.now().toString());
        } catch (trackingError) {
          developmentLogs('Failed to update journey tracking:', trackingError);

          // Continue even if journey update fails
        }
      }

      // Add artificial delay
      await new Promise((resolve) => setTimeout(resolve, 3000));

      if (response && !response.error) {
        clearStorage();
        window.location.href = `/ai_workflows/${response.data.slug}`;
      } else {
        throw new Error(response?.error || 'Failed to create workflow');
      }
    } catch (error) {
      console.error('Error details:', error);
      setAlert({
        open: true,
        message: error?.data?.message || error.message || 'Failed to save and run workflow',
        severity: 'error',
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Add handler for settings changes
  const handleSettingChange = (setting, value) => {
    setSettings((prev) => ({
      ...prev,
      [setting]: value,
    }));
  };


  // Add close handler for alert
  const handleCloseAlert = () => {
    setAlert((prev) => ({ ...prev, open: false }));
  };

  // Handle drag end for element reordering and new component drops
  const handleDragEnd = (result) => {
    if (!result.destination) {
      return;
    }

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    // Handle reordering of existing workflow elements
    if (result.source.droppableId === 'workflow-elements' && 
        result.destination.droppableId === 'workflow-elements') {
      
      if (sourceIndex === destinationIndex) {
        return;
      }

      // Create new array with sorted elements (display always last)
      const sortedElements = [...droppedElements].sort((a, b) => 
        a.type === 'output' ? 1 : b.type === 'output' ? -1 : 0
      );

      // Get the element being moved
      const [removed] = sortedElements.splice(sourceIndex, 1);
      
      // Don't allow display elements to be moved from last position
      if (removed.type === 'output') {
        return;
      }

      // Insert at new position
      sortedElements.splice(destinationIndex, 0, removed);

      // Ensure display element stays at the end
      const reorderedElements = sortedElements.sort((a, b) => 
        a.type === 'output' ? 1 : b.type === 'output' ? -1 : 0
      );

      setDroppedElements(reorderedElements);
    }
    
    // Handle new component drops from components area
    else if (result.source.droppableId === 'components-area' && 
             (result.destination.droppableId === 'workflow-elements' || 
              result.destination.droppableId === 'workflow-elements-empty')) {
      
      // Get the component template from draggableId
      const componentMap = {
        'text-input': 'text',
        'display': 'output',
        'prompt': 'prompt'
      };
      
      const template = componentMap[result.draggableId];
      
      // Check if there's already a display element
      const hasDisplayElement = droppedElements.some((element) => element.type === 'output');

      if (template === 'output' && hasDisplayElement) {
        setAlert({
          open: true,
          message: t('create.workflowCreator.alerts.maxDisplay'),
          severity: 'warning',
        });
        return;
      }

      const newElement = {
        id:
          template === 'output'
            ? `${template}_${999999999 + Date.now()}`
            : `${template}_${Date.now()}`,
        type: template === 'text' ? 'input' : template,
        title:
          template === 'text'
            ? t('create.workflowCreator.components.textInput')
            : template === 'output'
              ? t('create.workflowCreator.components.display')
              : t('create.workflowCreator.components.prompt'),
        icon: `https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/${
          template === 'output'
            ? 'output.svg'
            : template === 'prompt'
              ? 'prompt.svg'
              : 'text-input.svg'
        }`,
        input: '',
        detail: '',
        outputName: '',
        promptContent: '',
        inputs:
          template === 'text'
            ? [
                { inputType: 'text', name: 'input-key', value: '' },
                { inputType: 'text', name: 'input-value', value: '' },
              ]
            : [],
      };

      const newElements = [...droppedElements];
      
      // If dropping to empty area, just add to the end
      if (result.destination.droppableId === 'workflow-elements-empty') {
        newElements.push(newElement);
      } else {
        newElements.splice(destinationIndex, 0, newElement);
      }
      
      // Ensure display element stays at the end
      const sortedElements = newElements.sort((a, b) => 
        a.type === 'output' ? 1 : b.type === 'output' ? -1 : 0
      );

      setDroppedElements(sortedElements);
    }
  };

  const clearStorage = () => {
    // In edit mode, we don't want to clear everything
    if (isEditMode) {
      // Only clear specific items
      localStorage.removeItem('dropElementsData');
      return;
    }

    // In create mode, clear everything
    localStorage.removeItem('dropElementsData');
    localStorage.removeItem('appName');
    localStorage.removeItem('tinymce-custom-colors-forecolor');
    localStorage.removeItem('tinymce-custom-colors-hilitecolor');
    localStorage.removeItem('workflow-tour');
    setDroppedElements([]);
  };

  const workflowHeaderProps = {
    onLoadSample: handleLoadSample,
    onSaveDraft: handleSaveDraft,
    onSaveAndRun: handleSaveAndRun,
    workflowName: appName,
    onWorkflowNameChange: handleWorkflowNameChange,
    isEditMode: isEditMode,
  };

  const workflowColumnProps = {
    droppedElements: Array.isArray(droppedElements) ? droppedElements : [],
    onDrop: handleDrop,
    onDragOver: handleDragOver,
    onDeleteElement: handleDeleteElement,
    onElementInputChange: handleElementInputChange,
    onElementDetailChange: handleElementDetailChange,
    onOutputNameChange: handleOutputNameChange,
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Box>
        <WorkflowHeader {...workflowHeaderProps} />
        {/* Workflow Creator Main Section */}
        {isLoadingWorkflow ? (
          <Box className="workflow-creator-loading-container">
            <CircularProgress />
          </Box>
        ) : (
          <Box component="section" className="workflow-creator">
            <Container maxWidth="xl" className="workflow-creator-container">
              <Box className="workflow-creator-main-layout">
                <ComponentsColumn
                  onComponentClick={handleComponentClick}
                />

                <WorkflowDropArea {...workflowColumnProps} />

                <Box className="workflow-creator-empty-space">
                  {/* Empty space - only visible on desktop */}
                </Box>

                <ParameterSettings settings={settings} onSettingChange={handleSettingChange} />
              </Box>
            </Container>
          </Box>
        )}

        {/* Loading Dialog */}
      <Dialog open={isCreating} fullWidth maxWidth="sm">
        <DialogContent className="workflow-creator-loading-dialog">
          <CircularProgress size={60} />
          <Typography variant="h6" gutterBottom>
            {isEditMode
              ? t('create.workflowCreator.dialog.updating')
              : t('create.workflowCreator.dialog.creating')}
          </Typography>
          <Typography color="text.secondary">
            {isEditMode
              ? t('create.workflowCreator.dialog.updatingMessage')
              : t('create.workflowCreator.dialog.buildingMessage')}
          </Typography>
        </DialogContent>
      </Dialog>

      <AlertSnackbar
        open={alert.open}
        message={alert.message}
        severity={alert.severity}
        onClose={handleCloseAlert}
      />
      </Box>
    </DragDropContext>
  );
};

export default WorkflowCreatorPage;
