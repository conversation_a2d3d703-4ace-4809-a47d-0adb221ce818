import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Carousel from '../../components/Carousel/Carousel.jsx';
import { CardContent } from '@/components/CourseCard/CourseCard';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import './Apply.scss';
import PropTypes from 'prop-types';
import CardWithIcon from '../../components/CardWithIcon/CardWithIcon';
import AppCard from '@/components/AppCard/AppCard';
import { useState, useEffect, Suspense, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useGetUseCasesQuery } from '@/redux/services/use-case-api';
import { useGetWorkflowsQuery } from '@/redux/services/workflow-api';
import { useGetUserShortcutsQuery } from '@/redux/services/shortcuts-api';

// App icon import
import appIconSrc from '../../assets/images/app-icon.svg';

// Importing mock data
import { techCompanies } from '../../mockData/techCompanies';
import { aiPlaygrounds } from '../../mockData/aiPlaygrounds.js';

import { personalizeApi } from '../../middleware/personalize-api';
import { platformSettingsApi } from '../../redux/services/platform-settings-api';
import { segmentEvaluator } from '../../middleware/segment-evaluator';
import { checkAndLockCards } from '../../utils/cardUtils';

const { useGetUserSegmentationQuery } = platformSettingsApi;

const ApplyContent = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);
  const [showCompletion, setShowCompletion] = useState(false);

  const { data: useCases, isLoading: isUseCasesLoading } = useGetUseCasesQuery({
    limit: 6,
    function_label: user?.onboarding?.function_label,
  });

  const { data: workflows, isLoading: isWorkflowsLoading } = useGetWorkflowsQuery({
    limit: 6,
    function_label: user?.onboarding?.function_label,
  });

  const { data: managementUseCases, isLoading: isManagementUseCasesLoading } = useGetUseCasesQuery({
    limit: 6,
    function_label: 'management-management-support',
  });

  const { data: itUseCases, isLoading: isItUseCasesLoading } = useGetUseCasesQuery(
    {
      limit: 6,
      function_label: 'it-ai-data',
    },
    { skip: user?.onboarding?.technical_background_label?.slug !== 'it-specialist' }
  );

  const { data: userShortcuts, isLoading: isShortcutsLoading } = useGetUserShortcutsQuery(
    {
      userId: user?._id,
      shortcutType: '',
    },
    { skip: !user?._id }
  );

  const capitalizeFirstLetter = (string) => {
    return string ? string.charAt(0).toUpperCase() + string.slice(1) : '';
  };

  const getFunctionLabel = () => {
    try {
      const label = user?.onboarding?.function_label;
      if (!label) return '';
      if (typeof label === 'string') return label;
      return label.translations?.[i18n.language] || label.translations?.en || label.slug || '';
    } catch (error) {
      console.warn('Error getting function label:', error);
      return '';
    }
  };

  const functionLabel = capitalizeFirstLetter(getFunctionLabel());

  useEffect(() => {
    if (location.hash) {
      const id = location.hash.replace('#', '');
      const element = document.getElementById(id);
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }, 100);
      }
    }
  }, [location]);

  const handleStepComplete = () => {
    setShowCompletion(true);
  };

  useEffect(() => {
    handleStepComplete();
  }, []);

  const handleCardClickTechCompany = (url) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  let filteredPlaygrounds = [];
  let filteredCompanies = [];

  const { data: userSegmentation } = useGetUserSegmentationQuery();

  const userSegment = useMemo(
    () =>
      segmentEvaluator.findUserSegment(userSegmentation?.data?.segments, {
        function_label: user?.onboarding?.function_label,
        technical_background_label: user?.onboarding?.technical_background_label,
        ai_knowledge_label: user?.onboarding?.ai_knowledge_label,
        job_role_label: user?.onboarding?.job_role_label,
        management_role_label: user?.onboarding?.management_role_label,
        industry_label: user?.onboarding?.industry_label,
      }),
    [userSegmentation?.data?.segments, user?.onboarding]
  );
  if (user?.onboarding) {
    filteredCompanies = useMemo(() => {
      const filteredContent = personalizeApi.filterPageContent(
        techCompanies.map((company) => ({
          type: 'CompanyCard',
          props: { ...company },
        })),
        {
          ...user?.onboarding,
          segment: userSegment,
        }
      );

      // Then lock and check based on user role
      return checkAndLockCards(filteredContent, user, t);
    }, [techCompanies, user?.onboarding, userSegment, user?.role, user?.accessScheme, t]);

    filteredPlaygrounds = useMemo(() => {
      const filteredContent = personalizeApi.filterPageContent(
        aiPlaygrounds.map((playground) => ({
          type: 'PlaygroundCard',
          props: { ...playground },
        })),
        { ...user?.onboarding, segment: userSegment }
      );

      // Then lock and check based on user role
      return checkAndLockCards(filteredContent, user, t);
    }, [aiPlaygrounds, user?.onboarding, userSegment, user?.role, user?.accessScheme, t]);
  }
  const getShortcutUrl = (shortcutData) => {
    if (!shortcutData.apiData) return '#';

    switch (shortcutData.shortcutType) {
      case 'usecase':
        return `/usecase/${shortcutData.apiData.slug}`;
      case 'ai_app':
        return `/ai_apps/${shortcutData.apiData.slug}`;
      case 'workflow':
        return `/ai_workflows/${shortcutData.apiData.slug}`;
      default:
        return '#';
    }
  };

  const getShortcutIcon = (shortcutData) => {
    if (shortcutData.apiData && shortcutData.apiData.usecase_icon_url)
      return shortcutData.apiData.usecase_icon_url;

    // Use app-icon.svg for each shortcut type
    return appIconSrc;
  };

  const handleShortcutClick = (shortcutData) => {
    if (!shortcutData || !shortcutData.apiData) {
      console.warn('Shortcut data is incomplete:', shortcutData);
      return;
    }

    const url = getShortcutUrl(shortcutData);
    navigate(url);
  };

  const validShortcuts = userShortcuts
    ? userShortcuts.filter((shortcut) => {
        if (!shortcut.apiData) {
          return false;
        }

        if (shortcut.apiError) {
          return false;
        }

        if (!shortcut.apiData.title) {
          return false;
        }

        return true;
      })
    : [];

  const showFavorites = validShortcuts.length > 0;

  const getTranslatedTitle = (useCase) => {
    let title = useCase.title;
    let languageKey = 'english';

    if (i18n.language === 'de' || i18n.language === 'german' || i18n.language === 'deutsch') {
      languageKey = 'german';
    }

    if (useCase.translations && useCase.translations[languageKey]) {
      title = useCase.translations[languageKey].title;
    } else if (useCase.translations && useCase.translations.english) {
      title = useCase.translations.english.title;
    }

    return title;
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={0}>
        <Grid item xs={12} mb={4}>
          <WelcomeHeader
            isPageView={true}
            title={t('apply.title')}
            description={t('apply.subtitle')}
            showProgress={false}
          />
        </Grid>
      </Grid>
      <Grid container className="apply-container" spacing={0}>
        <Grid item xs={12}>
          <WhiteContainer
            title={t('apply.discoverMore.useCases', {
              role: getFunctionLabel() || '',
            })}
            subtitle={t('apply.discoverMore.specialSelection')}
            showNavigation={false}
            showMore={true}
            showMoreText={t('apply.discoverMore.showAll')}
            onShowMoreClick={() => navigate('/ai-usecases')}
          >
            <Grid container className="app-card-wrapper" spacing={2}>
              {!isUseCasesLoading &&
                useCases?.map((useCase) => (
                  <Grid item xs={12} sm={6} md={4} key={useCase._id}>
                    <AppCard
                      title={getTranslatedTitle(useCase)}
                      usecase_icon_url={useCase.usecase_icon_url}
                      appType="usecase"
                      id={useCase._id}
                      onClick={() => navigate(`/usecase/${useCase.slug}`)}
                    />
                  </Grid>
                ))}
            </Grid>
          </WhiteContainer>
        </Grid>
        {user?.onboarding?.technical_background_label?.slug === 'it-specialist' &&
          (typeof user?.onboarding?.function_label === 'string'
            ? user?.onboarding?.function_label !== 'it-ai-data'
            : user?.onboarding?.function_label?.slug !== 'it-ai-data') && (
            <Grid item xs={12}>
              <WhiteContainer
                title={t('apply.it.title')}
                subtitle={t('apply.it.subtitle')}
                showNavigation={false}
                showMore={true}
                showMoreText={t('apply.discoverMore.showAll')}
                onShowMoreClick={() => navigate('/it-usecases')}
              >
                <Grid container className="app-card-wrapper" spacing={2}>
                  {!isItUseCasesLoading &&
                    itUseCases?.map((useCase) => (
                      <Grid item xs={12} sm={6} md={4} key={useCase._id}>
                        <AppCard
                          title={getTranslatedTitle(useCase)}
                          usecase_icon_url={useCase.usecase_icon_url}
                          appType="usecase"
                          id={useCase._id}
                          onClick={() => navigate(`/usecase/${useCase.slug}`)}
                        />
                      </Grid>
                    ))}
                </Grid>
              </WhiteContainer>
            </Grid>
          )}
        {user?.onboarding?.management_role_label?.slug === 'yes' &&
          (typeof user?.onboarding?.function_label === 'string'
            ? user?.onboarding?.function_label !== 'management-management-support'
            : user?.onboarding?.function_label?.slug !== 'management-management-support') && (
            <Grid item xs={12}>
              <WhiteContainer
                title={t('apply.management.title', { defaultValue: 'Management Use Cases' })}
                subtitle={t('apply.management.subtitle', {
                  defaultValue: 'Special selection for management roles',
                })}
                showNavigation={false}
                showMore={true}
                showMoreText={t('apply.discoverMore.showAll')}
                onShowMoreClick={() => navigate('/management-usecases')}
              >
                <Grid container className="app-card-wrapper" spacing={2}>
                  {!isManagementUseCasesLoading &&
                    managementUseCases?.map((useCase) => (
                      <Grid item xs={12} sm={6} md={4} key={useCase._id}>
                        <AppCard
                          title={getTranslatedTitle(useCase)}
                          usecase_icon_url={useCase.usecase_icon_url}
                          appType="usecase"
                          id={useCase._id}
                          onClick={() => navigate(`/usecase/${useCase.slug}`)}
                        />
                      </Grid>
                    ))}
                </Grid>
              </WhiteContainer>
            </Grid>
          )}
        {workflows && workflows.length > 0 && (
          <Grid item xs={12}>
            <WhiteContainer
              title={t('apply.workflows.title', {
                role: getFunctionLabel() || '',
              })}
              subtitle={t('apply.workflows.subtitle')}
              showNavigation={false}
              showMore={false}
            >
              <Grid container className="app-card-wrapper" spacing={2}>
                {!isWorkflowsLoading &&
                  workflows?.map((workflow) => (
                    <Grid item xs={12} md={4} key={workflow._id}>
                      <AppCard
                        title={workflow.title}
                        usecase_icon_url={appIconSrc}
                        appType="workflow"
                        id={workflow._id}
                        onClick={() => navigate(`/ai_workflows/${workflow.slug}`)}
                      />
                    </Grid>
                  ))}
              </Grid>
            </WhiteContainer>
          </Grid>
        )}
        <Grid item xs={12} mb={4}>
          <WhiteContainer
            title={t('apply.aiPlaygrounds.title')}
            subtitle={t('apply.aiPlaygrounds.subtitle')}
            showNavigation={true}
            variant="transparent"
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 1, spaceBetween: 24 },
                768: { slidesPerView: 2, spaceBetween: 24 },
                1024: { slidesPerView: 3, spaceBetween: 24 },
                1440: { slidesPerView: 4, spaceBetween: 24 },
              }}
            >
              {filteredPlaygrounds.map((playground) => (
                <CardWithIcon
                  key={playground.props.id}
                  icon={
                    <img
                      src={playground.props.icon}
                      alt={playground.props.iconAlt}
                      style={playground.props.iconStyle}
                    />
                  }
                  title={
                    playground.props.translations?.title?.[i18n.language] ||
                    playground.props.translations?.title?.['en'] ||
                    playground.props.title
                  }
                  description={
                    playground.props.translations?.description?.[i18n.language] ||
                    playground.props.translations?.description?.['en'] ||
                    playground.props.description
                  }
                  variant={playground.props.variant}
                  locked={playground.props.locked}
                  tooltipText={
                    playground.props.locked
                      ? playground.props.tooltipText || t('common.lockedTooltip')
                      : undefined
                  }
                  buttonURL={playground.props.buttonURL}
                  buttonText={
                    playground.props.translations?.buttonText?.[i18n.language] ||
                    playground.props.translations?.buttonText?.['en'] ||
                    playground.props.buttonText
                  }
                  buttonType={playground.props.buttonType}
                  newTab={playground.props.newTab}
                  onClick={() => {
                    if (playground.props.locked) return; // Do nothing if locked

                    if (playground.props.newTab) {
                      window.open(playground.props.buttonURL, '_blank');
                    } else {
                      navigate(playground.props.buttonURL);
                    }
                  }}
                />
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>
        <Grid item xs={12} mb={4}>
          <WhiteContainer
            title={t('apply.techCompanies.title')}
            subtitle={t('apply.techCompanies.subtitle')}
            showNavigation={true}
            variant="transparent"
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 3, spaceBetween: 24 },
                768: { slidesPerView: 4, spaceBetween: 24 },
                1024: { slidesPerView: 5, spaceBetween: 24 },
                1440: { slidesPerView: 5, spaceBetween: 24 },
              }}
            >
              {filteredCompanies.map((company) => (
                <CardWithIcon
                  key={company.props.id}
                  image={company.props.image}
                  title={company.props.title}
                  description=""
                  locked={company.props.locked}
                  tooltipText={
                    company.props.locked
                      ? company.props.tooltipText || t('common.lockedTooltip')
                      : undefined
                  }
                  buttonType={company.props.buttonType}
                  variant={company.props.variant}
                  buttonURL={company.props.buttonURL}
                  class={company.props.class}
                  onClick={() => {
                    if (company.props.locked) return; // Do nothing if locked
                    handleCardClickTechCompany(company.props.buttonURL);
                  }}
                />
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>
        <Grid
          item
          className={user?.journeyLevel?.name !== 'beginner' ? 'favoriteFirst' : ''}
          xs={12}
        >
          {showFavorites && (
            <WhiteContainer
              id="my-favorites"
              title={t('apply.favorites.title')}
              subtitle={t('apply.favorites.subtitle')}
              showNavigation={false}
            >
              {isShortcutsLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Grid container className="app-card-wrapper" spacing={2}>
                  {validShortcuts.map((shortcut) => {
                    let title = shortcut.apiData.title;
                    if (shortcut.shortcutType === 'usecase' && shortcut.apiData.translations) {
                      let languageKey = 'english';

                      if (
                        i18n.language === 'de' ||
                        i18n.language === 'german' ||
                        i18n.language === 'deutsch'
                      ) {
                        languageKey = 'german';
                      }

                      if (shortcut.apiData.translations[languageKey]) {
                        title = shortcut.apiData.translations[languageKey].title;
                      } else if (shortcut.apiData.translations.english) {
                        title = shortcut.apiData.translations.english.title;
                      }
                    }

                    return (
                      <Grid item xs={12} md={4} key={shortcut._id}>
                        <AppCard
                          title={title}
                          usecase_icon_url={getShortcutIcon(shortcut)}
                          appType={shortcut.shortcutType}
                          id={shortcut.shortcutID}
                          onClick={() => handleShortcutClick(shortcut)}
                        />
                      </Grid>
                    );
                  })}
                </Grid>
              )}
            </WhiteContainer>
          )}
        </Grid>
      </Grid>
    </Container>
  );
};

CardContent.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  isLocked: PropTypes.bool,
  isCompleted: PropTypes.bool,
  buttonText: PropTypes.string.isRequired,
  buttonURL: PropTypes.string.isRequired,
  newTab: PropTypes.bool,
  hideButton: PropTypes.bool,
  onClick: PropTypes.func,
  journeyType: PropTypes.string,
};

const ApplyPage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)',
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <ApplyContent />
    </Suspense>
  );
};

export default ApplyPage;
