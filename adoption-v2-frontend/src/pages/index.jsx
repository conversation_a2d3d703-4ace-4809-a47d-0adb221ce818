export { default as HomePage } from './Home';
export { default as TrainingPage } from './Training';
export { default as ApplyPage } from './Apply';
export { default as CreatePage } from './Create';
export { default as InnovatePage } from './Innovate';
export { default as CockpitPage } from './Cockpit';
export { default as CoursePage } from './Course';
export { default as ProfilePage } from './Account';
export { default as LoginPage } from './Login';
export { default as ToolkitPage } from './Toolkits';
export { default as AccountSettingsPage } from './Account';
export { default as WorkflowCreatorPage } from './WorkflowCreator';
export { default as MicrosoftCopilotPage } from './MicrosoftCopilot';
export { default as AiUseCasesPage } from './AIUseCases';
export { default as ManagementUseCasesPage } from './ManagementUseCases';
export { default as ITUseCasesPage } from './ITUseCases';
export { default as PromptLibraryPage } from './PromptLibrary';
export { default as PlaygroundsPage } from './Playgrounds';
export { default as ChatGPTPlayground } from './Playgrounds/ChatGPT';
export { default as DALLEPlayground } from './Playgrounds/DALL-E';
export { default as HeygenPlayground } from './Playgrounds/Heygen';
export { default as ChatGPTDeveloperToolsPlayground } from './Playgrounds/ChatGPT-Developer-Tools';
export { default as AdvancedGenAICreator } from './Playgrounds/AdvancedGenAICreator';
export { default as SimpleAIAppCreatorPlayground } from './Playgrounds/SimpleAIAppCreator';
export { default as ToolsPage } from './Tools';
export { default as OpenAIPage } from './Tools/OpenAI';
export { default as MicrosoftPage } from './Tools/Microsoft';
export { default as GithubCopilot } from './Tools/GithubCopilot';
export { default as AIPlanetPage } from './Tools/AIPlanet';
export { default as AIBusinessSchoolPage } from './Tools/AIBusinessSchool';
export { default as AgenticWorkflowCreatorPlayground } from './Playgrounds/AgenticWorkflowCreator';
export { default as CheatSheetsPage } from './Sheets';
export { default as ChatGPTCheatSheetPage } from './Sheets/ChatGPT';
export { default as DALLECheatSheetPage } from './Sheets/DALL-E';
export { default as HeygenVideoCreatorPage } from './HeygenVideoCreator';
export { default as WorkflowRun } from './WorkflowRun';
export { default as ShareYourIdeaPage } from './ShareYourIdea';
export { default as ConditionPage } from './ConditionPage';
export { default as GenAIPage } from './GenAI';
export { default as PrivacyPolicyPage } from './PrivacyPolicy';
export { default as TermsAndConditionsPage } from './TermsAndConditions';
export { default as SingleAIAppCreator } from './SingleAIAppCreator';
export { default as SimpleAIAppCreatorTutorialPage } from './Tutorials/SimpleAIAppCreator';
export { default as AgenticWorkflowCreatorTutorialPage } from './Tutorials/AgenticWorkflowCreator';
export { default as HeygenTutorialPage } from './Tutorials/Heygen';
export { default as MicrosoftToolkits } from './Toolkits/Microsoft';
export { default as Top10CopilotFeatures } from './Toolkits/Microsoft/Top10CopilotFeatures';
export { default as PromptingCheatSheet } from './Toolkits/Microsoft/PromptingCheatSheet';
export { default as Microsoft365ToolGuidelinesForCopilot } from './Toolkits/Microsoft/Microsoft365ToolGuidelinesForCopilot';
export { default as ITCheatSheet } from './Sheets/IT';
export { default as AdvancedGenAICreatorTutorialPage } from './Tutorials/AdvancedGenAICreator';
export { default as SpesificSalesUseCasesPage } from './Tutorials/SpesificSalesUseCases';
export { default as NotFoundPage } from '../components/404';
export { default as MaintenancePage } from './Maintenance';
export { default as AiChatPage } from './ai-chat';
