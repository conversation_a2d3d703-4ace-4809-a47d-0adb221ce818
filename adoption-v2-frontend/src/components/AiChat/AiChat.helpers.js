// Helper functions for AiChat.jsx

export const handleSidebarToggle = (setIsSidebarCollapsed) => () => {
  setIsSidebarCollapsed((prev) => !prev);
};

export const handleModelChange = (setSelectedModel, setIsModelSelectorOpen, setHoveredModel) => (modelId) => {
  setSelectedModel(modelId);
  setIsModelSelectorOpen(false);
  setHoveredModel(null);
};

export const toggleModelSelector = (isModelSelectorOpen, setIsModelSelectorOpen, setHoveredModel) => () => {
  setIsModelSelectorOpen(!isModelSelectorOpen);
  setHoveredModel(null);
};

export const handleModelHover = (setHoveredModel) => (modelId) => {
  setHoveredModel(modelId);
};

export const handleModelLeave = (setHoveredModel) => () => {
  setHoveredModel(null);
};

export const handlePromptChange = (setPrompt) => (event) => {
  setPrompt(event.target.value);
};

export const handlePromptInput = (autoResizeTextarea) => () => {
  autoResizeTextarea();
};

export const handleFileChange = (setFile) => (event) => {
  if (event.target.files && event.target.files[0]) {
    setFile(event.target.files[0]);
  }
};

export const handlePromptPaste = (setFile) => (event) => {
  if (event.clipboardData && event.clipboardData.files.length > 0) {
    setFile(event.clipboardData.files[0]);
  }
};

export const handleRemoveFile = (setFile) => () => {
  setFile(null);
};

export const handleSendMessage = (prompt, selectedModel, file, setPrompt, setFile, autoResizeTextarea) => () => {
  // Handle sending message logic here
  console.log('Sending prompt:', prompt);
  console.log('Selected model:', selectedModel);
  console.log('Attached file:', file);
  setPrompt('');
  setFile(null);
  autoResizeTextarea();
};

export const autoResizeTextarea = (textareaRef) => () => {
  const textarea = textareaRef.current;
  if (textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 180) + 'px';
  }
};

export const handleCloseSupport = (setIsSupportVisible) => () => {
  setIsSupportVisible(false);
};

export const handleToggleFullscreen = (isFullscreen) => () => {
  if (!isFullscreen) {
    if (document.body.requestFullscreen) {
      document.body.requestFullscreen();
    } else if (document.body.webkitRequestFullscreen) {
      document.body.webkitRequestFullscreen();
    } else if (document.body.msRequestFullscreen) {
      document.body.msRequestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }
}; 