.ai-chat-root {
  display: flex;
  height: calc(100dvh - 100px);
  background: #f7f8fa;
}

.ai-chat-root main {
  margin-bottom: 0 !important;
}

body.ai-chat-active main {
  margin-bottom: 0 !important;
}

body.ai-chat-fake-fullscreen {
  overflow: hidden !important;
}

body.ai-chat-fake-fullscreen .ai-chat-root {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 99999;
  background: #f7f8fa;
  margin: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

body.ai-chat-fake-fullscreen .ai-chat-maincontainer {
  height: 100vh !important;
  max-width: 100vw !important;
  padding: 0 !important;
}

body.ai-chat-fake-fullscreen .ai-chat-prompt-block {
  margin: 0px 20px 20px 20px !important;
}

// --- MODERN SIDEBAR DESIGN ---
.ai-chat-sidebar {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 280px;
  min-width: 280px;
  max-width: 280px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  margin: 20px 0 20px 20px;
  padding: 10px;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.4s cubic-bezier(0.4, 0, 0.2, 1), max-width 0.4s cubic-bezier(0.4, 0, 0.2, 1), margin 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-chat-sidebar.collapsed {
  width: 64px;
  min-width: 64px;
  max-width: 64px;
  margin-left: 8px;
}

.ai-chat-sidebar-header {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 20px 12px 20px;
}
.collapsed .ai-chat-sidebar-header{
  padding-left: 15px;
}
.ai-chat-sidebar-logo {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: #f7f8fa;
}
.ai-chat-sidebar-titleblock {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.ai-chat-sidebar-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #222;
  line-height: 1.1;
}
.ai-chat-sidebar-subtitle {
  font-size: 0.85rem;
  color: #888;
  font-weight: 400;
  line-height: 1.1;
}
.ai-chat-sidebar .MuiIconButton-root.ai-chat-sidebar-toggle {
  position: absolute;
  z-index: 9999999;
  background: #fcfcfc;
  /* top: 0; */
  right: -20px;
  color: #a7a7a7;
  padding: 10px;
  width: 12px;
  height: 12px;
  border: solid 1px #e4e4e4;
  border-radius: 20%;
  display: flex
;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.ai-chat-sidebar .MuiIconButton-root.ai-chat-sidebar-toggle:hover {
  background: #e5e7eb;
  color: #6c47ff;
}

.ai-chat-sidebar-project-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f7f8fa;
  border-radius: 12px;
  margin: 0 16px 18px 16px;
  padding: 10px 14px;
  min-height: 56px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.ai-chat-sidebar-project-avatar {
  width: 36px;
  height: 36px;
  border-radius: 8px;
}
.ai-chat-sidebar-project-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.ai-chat-sidebar-project-title {
  font-size: 1rem;
  font-weight: 600;
  color: #222;
}
.ai-chat-sidebar-project-desc {
  font-size: 0.85rem;
  color: #888;
  font-weight: 400;
}
.ai-chat-sidebar-project-arrow {
  margin-left: auto;
  color: #888;
  background: #fff;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
}
.ai-chat-sidebar-project-arrow:hover {
  background: #e5e7eb;
  color: #6c47ff;
}

.ai-chat-sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0;
  align-items: stretch;
  background: transparent;
  overflow-y: auto;
  overflow-x: hidden;
}
.ai-chat-sidebar-menu.collapsed {
  overflow-y: hidden !important;
}

.ai-chat-sidebar-group-title {
  font-size: 0.92rem;
  color: #b0b3b8;
  font-weight: 400;
  margin: 18px 0 4px 24px;
  letter-spacing: 0.01em;
  background: transparent;
  border: none;
  padding: 0;
  cursor: default;
  pointer-events: none;
}

.ai-chat-sidebar-menuitem {
  display: flex;
  align-items: center;
  gap: 14px;
  border-radius: 10px;
  padding: 5px 16px 5px 20px;
  height: 44px;
  cursor: pointer;
  position: relative;
  transition: background 0.15s;
  outline: none;
  font-weight: 400;
  font-size: 1rem;
  color: #222;
  background: transparent;
  margin-bottom: 2px;
  border: none;
}

.ai-chat-sidebar-menuitem.collapsed {
  padding: 5px;
}

.ai-chat-sidebar-menuitem:hover {
  background: #f4f4f4;
}
.ai-chat-sidebar-menuitem.active {
  background: #f4f3ff;
  font-weight: 500;
}
.ai-chat-sidebar-menuitem-bar {
  position: absolute;
  left: 0;
  top: 8px;
  bottom: 8px;
  width: 4px;
  border-radius: 4px;
  background: linear-gradient(180deg, #6c47ff 0%, #a259c6 50%, #4285f4 100%);
  content: '';
  display: block;
}
.ai-chat-sidebar-menuitem-arrow {
  margin-left: auto;
  color: #6c47ff;
  font-size: 1.2rem !important;
}
.ai-chat-sidebar-icon {
  width: 24px;
  height: 24px;
  color: #888;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-right: 0;
  transition: color 0.18s;
}
.ai-chat-sidebar-menuitem.active .ai-chat-sidebar-icon {
  color: #6c47ff;
}
.ai-chat-sidebar-label {
  font-size: 1rem;
  font-weight: 400;
  color: #222;
  white-space: nowrap;
  transition: color 0.18s, opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1), width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  width: auto;
}
.ai-chat-sidebar-menuitem.history  {
  color: #888;
  font-size: 0.9rem;
  background: transparent;
  cursor: pointer;
  font-weight: 400;
  padding-left: 38px;
}
.collapsed .ai-chat-sidebar-group-title {
  display: none !important;
}
.ai-chat-sidebar-menuitem.history .ai-chat-sidebar-label {
  color: #888;
}

.ai-chat-sidebar.collapsed .ai-chat-sidebar-label {
  opacity: 0;
  width: 0;
  margin: 0;
  pointer-events: none;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1), width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-chat-sidebar-support {
  background: linear-gradient(135deg, #f7f8fa 60%, #edeaff 100%);
  border-radius: 16px;
  margin: 18px 16px 18px 16px;
  padding: 18px 16px 16px 16px;
  box-shadow: 0 2px 8px rgba(108,71,255,0.06);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-height: 110px;
}
.ai-chat-sidebar-support-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(108,71,255,0.08);
}
.ai-chat-sidebar-support-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.ai-chat-sidebar-support-title {
  font-size: 1rem;
  font-weight: 700;
  color: #222;
}
.ai-chat-sidebar-support-desc {
  font-size: 0.92rem;
  color: #888;
  font-weight: 400;
}
.ai-chat-sidebar-support-actions {
  display: flex;
  gap: 8px;
  margin-top: 6px;
  align-items: center;
}
.ai-chat-sidebar-support-btn {
  font-size: 0.95rem !important;
  text-transform: none !important;
  border-radius: 8px !important;
  min-width: 0 !important;
  padding: 4px 14px !important;
  box-shadow: none !important;
}

.ai-chat-sidebar.collapsed .ai-chat-sidebar-support-content {
  display: none;
}

.ai-chat-sidebar-support-icon img {
  width: 32px;
  height: 32px;
  display: block;
  margin: 0 auto;
}

.ai-chat-sidebar-logo, .ai-chat-sidebar-project-avatar {
  border-radius: 12px;
  object-fit: cover;
}

@media (max-width: 700px) {
  .ai-chat-sidebar.collapsed {
    width: 0px !important;
    min-width: 0px !important;
    max-width: 0px !important;
    padding: 0 !important;
    position: relative;
    margin: 0px !important;
    .ai-chat-sidebar-menu,
    .ai-chat-sidebar-support {
      display: none !important;
    }
    .ai-chat-sidebar-toggle {
      position: absolute !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      display: flex !important;
    }
  }
  .ai-chat-sidebar {  
    .ai-chat-sidebar-toggle {
      right: 30px !important;
    }
  }
  .ai-chat-center-content {
    margin: 0px 20px 20px 20px !important;
  }
}

.ai-chat-mainarea {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #f7f8fa;
  background-position: -1px -1px, -1px -1px, -1px -1px, -1px -1px;
  background-size: 20px 20px, 20px 20px, 10px 10px, 10px 10px;
  background-image: linear-gradient(rgba(80, 80, 80, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(80, 80, 80, 0.05) 1px, transparent 1px), linear-gradient(rgba(80, 80, 80, 0.025) 0.5px, transparent 0.5px), linear-gradient(90deg, rgba(80, 80, 80, 0.025) 0.5px, transparent 0.5px);
}

.ai-chat-maincontainer {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 100vh;
  padding: 0 24px;
}

.ai-chat-model-selector-bar {
  width: 100%;
  margin-top: 20px;
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 100;
}

.ai-chat-model-selector-header {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff;
  border-radius: 20px;
  padding: 8px 16px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s;
  max-width: 220px;
  margin: 0 auto;

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  }
}


.ai-chat-model-name {
  font-weight: 500;
  color: #222;
  flex: 1;
}

.ai-chat-model-dropdown-icon {
  color: #888;
  font-size: 16px;
}

.ai-chat-model-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  width: 480px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  border-radius: 8px;
  overflow: hidden;
  z-index: 1000;
  background: #fff;
}

.ai-chat-model-dropdown-content .css-rizt0-MuiTypography-root {
  font-size: 14px !important;
}

.ai-chat-model-dropdown-content {
  display: flex;
  flex-direction: row;
  height: 400px;
  max-height: 70vh;
}

.ai-chat-model-list {
  width: 230px;
  border-right: 1px solid #eee;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.ai-chat-model-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.selected {
    background-color: #f0f4fa;
  }
}

.ai-chat-model-item-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #6c47ff;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.ai-chat-model-item-name {
  font-size: 0.95rem;
  font-weight: 500;
  color: #222;
  flex: 1;
}

.ai-chat-model-item-dot {
  width: 16px;
  height: 16px;
  color: #2684ff;
  margin-left: auto;
}

.ai-chat-model-list-footer {
  margin-top: auto;
  padding: 16px;
  border-top: 1px solid #eee;
}

.ai-chat-model-list-footer-text {
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;

  &:hover {
    color: #2684ff;
    text-decoration: underline;
  }
}

.ai-chat-model-details {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.ai-chat-model-details-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.ai-chat-model-details-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #6c47ff;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.ai-chat-model-details-name {
  font-weight: 500;
  color: #222;
}

.ai-chat-model-details-scores {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.ai-chat-model-details-score {
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 12px 20px;
  flex: 1;
  text-align: center;
}

.ai-chat-model-details-score-value {
  font-weight: 600;
  color: #222;
}

.ai-chat-model-details-score-label {
  color: #666;
  margin-top: 2px;
}

.ai-chat-model-details-subtitle {
  font-weight: 500;
  color: #222;
  margin-bottom: 20px !important;
}

.ai-chat-model-details-capabilities {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ai-chat-model-details-capability {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-chat-model-details-capability-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a1a1a1;
}

.ai-chat-model-details-capability-name {
  font-size: 0.85rem;
  color: #222;
  flex: 1;
}

.ai-chat-model-details-capability-status {
  font-size: 0.85rem;
  
  &.supported {
    color: #0D904F;
  }
  
  &.not-supported {
    color: #888;
  }
}

.ai-chat-model-details-footer {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.ai-chat-model-details-footer-text {
  font-size: 0.85rem;
  color: #888;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.ai-chat-center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ai-chat-center-logo {
  width: 72px;
  height: 72px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.ai-chat-center-title {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(90deg, #4285f4 0%, #a259c6 50%, #ea4335 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: -1px;
  margin-top: 12px;
}

.ai-chat-prompt-block {
  width: 100%;
  max-width: 600px;
  margin: 0 auto 10% auto;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1.5px solid #e5e7eb;
  min-height: 72px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  padding: 16px;
}

.ai-chat-prompt-inner {
  display: flex;
  align-items: center;
  width: 100%;
}

.ai-chat-prompt-textarea {
  border: none;
  outline: none;
  resize: none;
  background: transparent;
  font-size: 1.08rem;
  color: #222;
  min-height: 40px;
  max-height: 300px;
  line-height: 1.6;
  font-family: inherit;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  box-sizing: border-box;
  border-radius: 0;
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb #fff;
}

.ai-chat-prompt-textarea::placeholder {
  color: #b0b3b8;
  opacity: 1;
  font-size: 1.08rem;
}

.ai-chat-prompt-textarea::-webkit-scrollbar {
  width: 6px;
  background: #fff;
}
.ai-chat-prompt-textarea::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 6px;
}

.ai-chat-prompt-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  width: 100%;
}

.ai-chat-file-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f5f6fa;
  border-radius: 12px;
  padding: 8px 14px;
  margin-bottom: 10px;
  margin-top: 4px;
  position: relative;
}

.ai-chat-file-preview.appearing {
  opacity: 0;
  transform: scale(0.85);
  animation: fileAppearAnim 0.35s forwards;
}

@keyframes fileAppearAnim {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.ai-chat-file-preview.removing {
  opacity: 0;
  transform: scale(0.85);
  transition: opacity 0.35s, transform 0.35s;
  pointer-events: none;
}

.ai-chat-file-thumb {
  width: 54px;
  height: 54px;
  object-fit: cover;
  border-radius: 8px;
  background: #e5e7eb;
}

.ai-chat-file-name {
  font-size: 0.98rem;
  color: #222;
  flex: 1;
  word-break: break-all;
}

.ai-chat-file-remove {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f44336;
  color: #fff;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.75rem;
  z-index: 2;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(244,67,54,0.12);
}
.ai-chat-file-remove:hover, .ai-chat-file-remove:focus-visible {
  background: #d32f2f;
  box-shadow: 0 4px 16px rgba(244,67,54,0.18);
  outline: none;
}

.ai-chat-inputbar {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  background: #fff;
  border-radius: 32px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  border: 1.5px solid #e5e7eb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  min-height: 56px;
}

.ai-chat-inputbar-file-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #fff;
  color: #888;
  border: 1.2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s, border 0.2s;
  box-shadow: none;
  font-size: 1.1rem;
  flex-shrink: 0;
  margin-right: 4px;
  cursor: pointer;
  outline: none;
}

.ai-chat-inputbar-file-btn:hover {
  background: #f0f4fa;
  color: #1976d2;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px #e3eaff;
}

.ai-chat-inputbar-file-btn:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
  border-color: #1976d2;
  background: #f0f4fa;
}

.ai-chat-inputbar-send-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #888;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
  font-size: 1.1rem;
  flex-shrink: 0;
  margin-left: 4px;
}

.ai-chat-inputbar-send-btn:enabled {
  background: #1976d2;
  color: #fff;
}

.ai-chat-inputbar-send-btn:disabled {
  background: #e5e7eb;
  color: #b0b3b8;
  cursor: not-allowed;
}

@media (max-width: 700px) {
  .ai-chat-prompt-block {
    max-width: 85%;
    margin: 0px 20px 20px 20px !important;
  }
  .ai-chat-inputbar-file-btn,
  .ai-chat-inputbar-send-btn {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }
  .ai-chat-prompt-textarea {
    padding: 0;
  }
  .ai-chat-prompt-actions {
    margin-top: 6px;
  }
  .ai-chat-sidebar {
    width: 98vw;
    min-width: 98vw;
    max-width: 98vw;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }
}

// Fullscreen button style
.ai-chat-fullscreen-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1200;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  border-radius: 50%;
  transition: background 0.2s, color 0.2s;
}
.ai-chat-fullscreen-btn:hover {
  background: #e5e7eb;
  color: #6c47ff;
}

// File preview row (yatayda sıralama)
.ai-chat-file-preview-row {
  display: flex;
  flex-direction: row;
  gap: 16px;
  margin-bottom: 8px;
  align-items: flex-start;
}

// Office dosyası kutusu
.ai-chat-file-docbox {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-width: 180px;
  min-height: 56px;
  background: #f5f6fa;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(80,80,80,0.04);
  position: relative;
  gap: 5px;
}

.ai-chat-file-docinfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
}

.ai-chat-file-docname {
  font-size: 0.85rem;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
  word-break: break-all;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ai-chat-file-docdesc {
  font-size: 0.75rem;
  color: #888;
  font-weight: 400;
}

.ai-chat-file-docicon {
  display: flex;
  align-items: center;
  font-size: 2rem;
} 