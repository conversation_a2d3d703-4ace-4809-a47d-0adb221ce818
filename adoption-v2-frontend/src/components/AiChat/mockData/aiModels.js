const aiModels = [
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    quality: 4,
    qualityMax: 5,
    speed: 4,
    speedMax: 5,
    icon: 'gpt', 
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: true }
    ],
    knowledgeCutoff: 'April 2023',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'gpt-4.1',
    name: 'GPT-4.1',
    quality: 4,
    qualityMax: 5,
    speed: 3,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: false }
    ],
    knowledgeCutoff: 'October 2023',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'gpt-4.1-canvas',
    name: 'GPT-4.1 with Canvas',
    quality: 4,
    qualityMax: 5,
    speed: 3,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: true }
    ],
    knowledgeCutoff: 'October 2023',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'gpt-4.1-nano',
    name: 'GPT-4.1 nano',
    quality: 3,
    qualityMax: 5,
    speed: 5,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: false }
    ],
    knowledgeCutoff: 'June 2024',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'gpt-4o-canvas',
    name: 'GPT-4o with Canvas',
    quality: 3,
    qualityMax: 5,
    speed: 5,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: true }
    ],
    knowledgeCutoff: 'June 2024',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    quality: 3,
    qualityMax: 5,
    speed: 5,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: false }
    ],
    knowledgeCutoff: 'December 2023',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    quality: 2,
    qualityMax: 5,
    speed: 5,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: false },
      { name: 'Canvas', supported: false }
    ],
    knowledgeCutoff: 'January 2024',
    host: 'Hosted in the EU by Microsoft Azure'
  },
  {
    id: 'o1',
    name: 'o1',
    quality: 5,
    qualityMax: 5,
    speed: 2,
    speedMax: 5,
    icon: 'gpt',
    capabilities: [
      { name: 'Documents', supported: true },
      { name: 'Image analysis', supported: true },
      { name: 'Canvas', supported: true }
    ],
    knowledgeCutoff: 'June 2024',
    host: 'Hosted in the EU by Microsoft Azure'
  }
];

export default aiModels; 