import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Container,
  Tooltip,
  Typography,
  Paper,
  IconButton,
  Button,
} from '@mui/material';
import {
  Search as SearchIcon,
  Close as CloseIcon,
  Add as AddIcon,
  ArrowUpward as ArrowUpwardIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Description as DescriptionIcon,
  KeyboardArrowRight as ArrowRightIcon,
  Widgets as WidgetsIcon,
  EditNote as EditNoteIcon,
  Diversity3 as Diversity3Icon,
  MenuBook as MenuBookIcon,
  Image as ImageIcon,
  FormatPaint as FormatPaintIcon,
  FiberManualRecord as DotIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Check as CheckIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  TableChart as TableChartIcon,
  Article as ArticleIcon,
} from '@mui/icons-material';
import aiModels from './mockData/aiModels';
import './AiChat.scss';
import copilotLogo from '@/assets/images/logos/ms-copilot.svg';
import PropTypes from 'prop-types';
import {
  handleSidebarToggle,
  handleModelChange,
  toggleModelSelector,
  handleModelHover,
  handleModelLeave,
  handlePromptChange,
  handlePromptInput,
  handleFileChange,
  handlePromptPaste,
  handleRemoveFile,
  handleSendMessage,
  autoResizeTextarea,
  handleCloseSupport,
  handleToggleFullscreen,
} from './AiChat.helpers';
import menuGroupsRaw from './mockData/menuGroups';

// Convert icon string to JSX
const iconMap = {
  '<EditNoteIcon />': <EditNoteIcon />,
  '<SearchIcon />': <SearchIcon />,
  '<Diversity3Icon />': <Diversity3Icon />,
  '<MenuBookIcon />': <MenuBookIcon />,
  '<WidgetsIcon />': <WidgetsIcon />,
};

const menuGroups = menuGroupsRaw.map(group => ({
  ...group,
  items: group.items.map(item => ({
    ...item,
    icon: item.icon && iconMap[item.icon] ? iconMap[item.icon] : undefined,
  })),
}));

// Helper: dosya uzantısına göre ikon döndür
const getFileIconByExtension = (file) => {
  if (!file || !file.name) return <DescriptionIcon fontSize="large" />;
  const ext = file.name.split('.').pop().toLowerCase();
  if (['pdf'].includes(ext)) return <DescriptionIcon fontSize="large" color="error" />;
  if (['xls', 'xlsx', 'csv'].includes(ext)) return <TableChartIcon fontSize="large" color="success" />;
  if (['doc', 'docx'].includes(ext)) return <ArticleIcon fontSize="large" color="primary" />;
  return <DescriptionIcon fontSize="large" />;
};

const AiChat = ({ hideSidebar = false, hideFullscreen = false, hideModelSelector = false, hideFileUpload = false, sidebarCollapsed = false, startFullscreen = false }) => {
  const [selectedModel, setSelectedModel] = useState(() => {
    return localStorage.getItem('aiChatSelectedModel') || 'gpt-4.1-nano';
  });
  const [prompt, setPrompt] = useState('');
  const [files, setFiles] = useState([]);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(sidebarCollapsed);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [hoveredModel, setHoveredModel] = useState(null);
  const [isSupportVisible, setIsSupportVisible] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const textareaRef = useRef(null);
  const aiChatRootRef = useRef(null);
  const [removingFiles, setRemovingFiles] = useState([]);
  const [appearingFiles, setAppearingFiles] = useState([]);
  const prevFilesRef = useRef([]);

  const selectedModelData = aiModels.find((model) => model.id === selectedModel);
  const displayedModelData = aiModels.find((model) => model.id === (hoveredModel || selectedModel));

  // Fullscreen event listener
  useEffect(() => {
    if (isFullscreen) {
      document.body.classList.add('ai-chat-fake-fullscreen');
    } else {
      document.body.classList.remove('ai-chat-fake-fullscreen');
    }
    return () => {
      document.body.classList.remove('ai-chat-fake-fullscreen');
    };
  }, [isFullscreen]);

  // Model seçimi değiştiğinde localStorage'a kaydet
  useEffect(() => {
    if (selectedModel) {
      localStorage.setItem('aiChatSelectedModel', selectedModel);
    }
  }, [selectedModel]);

  // Dosya yüklendiğinde appearing efekti uygula
  useEffect(() => {
    const prevFiles = prevFilesRef.current;
    if (files.length > prevFiles.length) {
      // Sadece yeni eklenen dosyaların index'leri
      const newIndexes = [];
      files.forEach((file, idx) => {
        if (!prevFiles.includes(file)) {
          newIndexes.push(idx);
        }
      });
      if (newIndexes.length > 0) {
        setAppearingFiles(prev => [...prev, ...newIndexes]);
        setTimeout(() => {
          setAppearingFiles(prev => prev.filter(idx => !newIndexes.includes(idx)));
        }, 350);
      }
    }
    prevFilesRef.current = files;
  }, [files]);

  // startFullscreen prop'u ile başlat
  useEffect(() => {
    if (startFullscreen) {
      setIsFullscreen(true);
    }
  }, [startFullscreen]);

  const handleRemoveFileWithEffect = (idx) => {
    setRemovingFiles((prev) => [...prev, idx]);
    setTimeout(() => {
      setFiles((prev) => prev.filter((_, i) => i !== idx));
      setRemovingFiles((prev) => prev.filter((i) => i !== idx));
    }, 350); // 350ms animasyon süresi
  };

  const handleFakeFullscreenToggle = () => {
    setIsFullscreen((prev) => !prev);
  };

  return (
    <Box ref={aiChatRootRef} className={`ai-chat-root${isSidebarCollapsed ? ' collapsed' : ''}`}>
      {/* Sidebar */}
      {!hideSidebar && (
        <Box className={`ai-chat-sidebar${isSidebarCollapsed ? ' collapsed' : ''}`}>
          {/* Header */}
          <Box className="ai-chat-sidebar-header">
            <img className="ai-chat-sidebar-logo" src={copilotLogo} alt="Copilot Logo" />
            {!isSidebarCollapsed && (
              <Box className="ai-chat-sidebar-titleblock">
                <Typography className="ai-chat-sidebar-title" variant="subtitle1">COPILOT</Typography>
                <Typography className="ai-chat-sidebar-subtitle" variant="caption">Microsoft 365 Copilot Chat</Typography>
              </Box>
            )}
            <IconButton
              className="ai-chat-sidebar-toggle"
              onClick={handleSidebarToggle(setIsSidebarCollapsed)}
              aria-label={isSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
              tabIndex={0}
              onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') handleSidebarToggle(setIsSidebarCollapsed); }}
              size="large"
            >
              {isSidebarCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
          </Box>
          {/* Menu */}
          <ul className="ai-chat-sidebar-menu">
            {menuGroups.map((group, gIdx) => (
              <React.Fragment key={gIdx}>
                {group.title && (
                  <li className="ai-chat-sidebar-group-title" aria-disabled="true">{group.title}</li>
                )}
                {group.items.map((item, idx) => (
                  isSidebarCollapsed ? (
                    <Tooltip title={item.label} placement="right" arrow key={item.label}>
                      <li
                        className={`ai-chat-sidebar-menuitem${item.active ? ' active' : ''}${item.isHistory ? ' history' : ''}`}
                        tabIndex={0}
                        aria-label={item.label}
                        role="menuitem"
                      >
                        {item.icon && <span className="ai-chat-sidebar-icon">{item.icon}</span>}
                        <span className="ai-chat-sidebar-label">{item.label}</span>
                        {item.active && <span className="ai-chat-sidebar-menuitem-bar" />}
                        {item.active && <ArrowRightIcon className="ai-chat-sidebar-menuitem-arrow" fontSize="small" />}
                      </li>
                    </Tooltip>
                  ) : (
                    <li
                      className={`ai-chat-sidebar-menuitem${item.active ? ' active' : ''}${item.isHistory ? ' history' : ''}`}
                      key={item.label}
                      tabIndex={0}
                      aria-label={item.label}
                      role="menuitem"
                    >
                      {item.icon && <span className="ai-chat-sidebar-icon">{item.icon}</span>}
                      <span className="ai-chat-sidebar-label">{item.label}</span>
                      {item.active && <span className="ai-chat-sidebar-menuitem-bar" />}
                      {item.active && <ArrowRightIcon className="ai-chat-sidebar-menuitem-arrow" fontSize="small" />}
                    </li>
                  )
                ))}
              </React.Fragment>
            ))}
          </ul>
          {/* Support Card */}
          {!isSidebarCollapsed && isSupportVisible && (
          <Box className="ai-chat-sidebar-support">          
              <Box className="ai-chat-sidebar-support-content">
                <Typography className="ai-chat-sidebar-support-title" variant="subtitle2">Need Support?</Typography>
                <Typography className="ai-chat-sidebar-support-desc" variant="caption">
                Watch the help video to get to know Ai chat better and get detailed information.
                </Typography>
                <Box className="ai-chat-sidebar-support-actions">
                  <Button variant="contained" size="small" className="ai-chat-sidebar-support-btn">Watch Tutorials</Button>
                  <Button 
                    variant="text" 
                    size="small" 
                    className="ai-chat-sidebar-support-btn"
                    onClick={handleCloseSupport(setIsSupportVisible)}
                    aria-label="Close support card"
                  >Close</Button>
                </Box>
              </Box>          
          </Box>
        )}
        </Box>
      )}
      {/* Main Area */}
      <Box className="ai-chat-mainarea">
        {!hideFullscreen && (
          <IconButton
            className="ai-chat-fullscreen-btn"
            onClick={handleFakeFullscreenToggle}
            aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            tabIndex={0}
            sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1200 }}
          >
            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </IconButton>
        )}
        <Container maxWidth="md" className="ai-chat-maincontainer">
          {/* Model Selector */}
          {!hideModelSelector && (
            <Box className="ai-chat-model-selector-bar">
              <Box className="ai-chat-model-selector-header" onClick={() => toggleModelSelector(isModelSelectorOpen, setIsModelSelectorOpen, setHoveredModel)()}>
                <Typography className="ai-chat-model-name">
                  {displayedModelData?.name || 'Select Model'}
                </Typography>
                <ArrowDownIcon fontSize="small" className="ai-chat-model-dropdown-icon" />
              </Box>

              {isModelSelectorOpen && (
                <Paper className="ai-chat-model-dropdown">
                  <Box className="ai-chat-model-dropdown-content">
                    <Box className="ai-chat-model-list">
                      {aiModels.map((model) => (
                        <Box 
                          key={model.id}
                          className={`ai-chat-model-item${model.id === selectedModel ? ' selected' : ''}`}
                          onClick={() => handleModelChange(setSelectedModel, setIsModelSelectorOpen, setHoveredModel)(model.id)}
                          onMouseEnter={() => handleModelHover(setHoveredModel)(model.id)}
                          onMouseLeave={() => handleModelLeave(setHoveredModel)()}
                        >
                          <Typography className="ai-chat-model-item-name">
                            {model.name}
                          </Typography>
                          <DotIcon className="ai-chat-model-item-dot" fontSize="small" />
                        </Box>
                      ))}
                      <Box className="ai-chat-model-list-footer">
                        <Typography className="ai-chat-model-list-footer-text">
                          Edit available models
                        </Typography>
                      </Box>
                    </Box>

                    <Box className="ai-chat-model-details">
                      <Box className="ai-chat-model-details-header">
                        <Typography className="ai-chat-model-details-name">
                          {displayedModelData?.name || 'Model'}
                        </Typography>
                      </Box>
                      
                      <Box className="ai-chat-model-details-scores">
                        <Box className="ai-chat-model-details-score">
                          <Typography className="ai-chat-model-details-score-value">
                            {displayedModelData?.quality || 0} / {displayedModelData?.qualityMax || 5}
                          </Typography>
                          <Typography className="ai-chat-model-details-score-label">
                            Quality
                          </Typography>
                        </Box>
                        <Box className="ai-chat-model-details-score">
                          <Typography className="ai-chat-model-details-score-value">
                            {displayedModelData?.speed || 0} / {displayedModelData?.speedMax || 5}
                          </Typography>
                          <Typography className="ai-chat-model-details-score-label">
                            Speed
                          </Typography>
                        </Box>
                      </Box>

                      <Typography className="ai-chat-model-details-subtitle">
                        Capabilities
                      </Typography>
                      
                      <Box className="ai-chat-model-details-capabilities">
                        {displayedModelData?.capabilities.map((capability, index) => (
                          <Box key={index} className="ai-chat-model-details-capability">
                            <Box className="ai-chat-model-details-capability-icon">
                              {capability.name === 'Documents' && <DescriptionIcon fontSize="small" />}
                              {capability.name === 'Image analysis' && <ImageIcon fontSize="small" />}
                              {capability.name === 'Canvas' && <FormatPaintIcon fontSize="small" />}
                            </Box>
                            <Typography className="ai-chat-model-details-capability-name">
                              {capability.name}
                            </Typography>
                            <Typography className={`ai-chat-model-details-capability-status${capability.supported ? ' supported' : ' not-supported'}`}>
                              {capability.supported ? 'Supported' : 'Not supported'}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                      
                      <Box className="ai-chat-model-details-footer">
                        <Typography className="ai-chat-model-details-footer-text">
                          Knowledge cutoff: {displayedModelData?.knowledgeCutoff}
                        </Typography>
                        <Typography className="ai-chat-model-details-footer-text">
                          {displayedModelData?.host}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Paper>
              )}
            </Box>
          )}
          {/* Centered Logo/Welcome */}
          <Box className="ai-chat-center-content">
            <Typography variant="h5" className="ai-chat-center-title">Hello Sukru, what can I help you with?</Typography>
          </Box>
          {/* Prompt Block: textarea above, actions row below */}
          <Box className="ai-chat-prompt-block">
            {/* File Preview */}
            {files.length > 0 && (
              <Box className="ai-chat-file-preview-row">
                {files.map((file, idx) => (
                  <Box className={`ai-chat-file-preview${removingFiles.includes(idx) ? ' removing' : ''}${appearingFiles.includes(idx) ? ' appearing' : ''}`} key={file.name + idx}>
                    {file.type.startsWith('image/') ? (
                      <img
                        src={URL.createObjectURL(file)}
                        alt={file.name}
                        className="ai-chat-file-thumb"
                      />
                    ) : (
                      <Box className="ai-chat-file-docbox">
                        <span className="ai-chat-file-docicon">{getFileIconByExtension(file)}</span>
                        <span className="ai-chat-file-docinfo">
                          <span className="ai-chat-file-docname">{file.name}</span>
                          <span className="ai-chat-file-docdesc">Document</span>
                        </span>
                      </Box>
                    )}
                    <button className="ai-chat-file-remove" onClick={() => handleRemoveFileWithEffect(idx)} aria-label="Remove file" disabled={removingFiles.includes(idx)}>
                      <CloseIcon fontSize="inherit" />
                    </button>
                  </Box>
                ))}
              </Box>
            )}
            <textarea
              ref={textareaRef}
              className="ai-chat-prompt-textarea"
              placeholder="Type your prompt..."
              value={prompt}
              onChange={handlePromptChange(setPrompt)}
              onInput={handlePromptInput(autoResizeTextarea(textareaRef))}
              onPaste={handlePromptPaste(setFiles)}
              rows={1}
              style={{ overflowY: 'auto' }}
            />
            <div className="ai-chat-prompt-actions">
              {!hideFileUpload && (
                <>
                  <input
                    accept="*/*"
                    id="file-input"
                    type="file"
                    multiple
                    onChange={e => {
                      if (e.target.files) {
                        setFiles(prev => ([...prev, ...Array.from(e.target.files)]));
                      }
                    }}
                    style={{ display: 'none' }}
                  />
                  <Tooltip title="Add file">
                    <label htmlFor="file-input" className="ai-chat-inputbar-file-btn" aria-label="Add file" tabIndex={0}>
                      <AddIcon />
                    </label>
                  </Tooltip>
                </>
              )}
              <Tooltip title="Send">
                <span>
                  <button
                    className="ai-chat-inputbar-send-btn"
                    type="button"
                    onClick={handleSendMessage(prompt, selectedModel, files, setPrompt, setFiles, autoResizeTextarea(textareaRef))}
                    disabled={!prompt.trim() && files.length === 0}
                    aria-label="Send"
                  >
                    <ArrowUpwardIcon />
                  </button>
                </span>
              </Tooltip>
            </div>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

AiChat.propTypes = {
  hideSidebar: PropTypes.bool,
  hideFullscreen: PropTypes.bool,
  hideModelSelector: PropTypes.bool,
  hideFileUpload: PropTypes.bool,
  sidebarCollapsed: PropTypes.bool,
  startFullscreen: PropTypes.bool,
};

export default AiChat; 