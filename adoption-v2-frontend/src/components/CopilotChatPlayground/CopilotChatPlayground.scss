@use 'sass:color';
@use '../../styles/abstracts/variables' as *;

// Colors for Microsoft Copilot Chat theme
$copilot-primary: #0078d4;
$copilot-secondary: #106ebe;
$copilot-accent: #005a9e;
$copilot-light: #deecf9;
$copilot-bg: #f3f2f1;

// Custom styles for CopilotChatPlayground
.copilot-modal__chat {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #fff 0%, #ffffff 100%);
  height: auto;
  border: 1px solid #e1dfdd;
  margin-top: $spacing-4;
  border-radius: 16px;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 120, 212, 0.1);

  &-container {
    display: flex;
    flex: 1;
    gap: $spacing-4;
    padding: $spacing-4;
    position: relative;

    @media (max-width: $tablet) {
      flex-direction: column;
      padding: $spacing-3;
    }

    .settings-toggle-button {
      position: absolute;
      top: $spacing-2;
      right: $spacing-2;
      color: $copilot-primary;
      background-color: rgba(#fff, 0.9);
      border: 1px solid $copilot-light;
      z-index: 10;
      overflow: hidden;
      transition: all 0.3s ease;

      // Continuous ripple effect
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba($copilot-primary, 0) 0%, rgba($copilot-primary, 0.2) 50%, rgba($copilot-primary, 0) 100%);
        transform: scale(0);
        border-radius: 50%;
        animation: permanent-ripple 3s linear infinite;
      }

      @keyframes permanent-ripple {
        0% {
          transform: scale(0.3);
          opacity: 0;
        }
        35% {
          transform: scale(1);
          opacity: 0.3;
        }
        100% {
          transform: scale(2);
          opacity: 0;
        }
      }

      // Click ripple effect
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba($copilot-primary, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%, -50%);
        transform-origin: 50% 50%;
      }

      &:active::after {
        animation: ripple 1.5s ease-out;
      }

      &:focus::after {
        animation: ripple 1.5s ease-out;
      }

      @keyframes ripple {
        0% {
          transform: scale(0, 0);
          opacity: 0.8;
        }
        20% {
          transform: scale(25, 25);
          opacity: 0.6;
        }
        100% {
          opacity: 0;
          transform: scale(50, 50);
        }
      }

      &:hover {
        background-color: rgba($copilot-primary, 0.1);
        box-shadow: 0 0 8px rgba($copilot-primary, 0.3);
      }

      &.pulse {
        animation: pulse 1.5s infinite;
        box-shadow: 0 0 0 rgba($copilot-primary, 0.6);

        &:hover {
          animation: none;
          box-shadow: 0 0 8px rgba($copilot-primary, 0.3);
        }
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba($copilot-primary, 0.6);
        }
        70% {
          box-shadow: 0 0 0 15px rgba($copilot-primary, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba($copilot-primary, 0);
        }
      }
    }
  }

  &-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 500px;
    max-height: 700px;
    position: relative;
    &.promptLibrary{
      height: 500px !important;
    }

    @media (max-width: $tablet) {
      height: 400px;
    }
  }

  &-welcome {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    img {
      margin-bottom: $spacing-2;
      filter: drop-shadow(0 2px 8px rgba($copilot-primary, 0.2));
    }

    .MuiTypography-h6 {
      font-weight: $font-weight-medium;
      color: $copilot-primary;
    }

    .MuiTypography-body2 {
      color: $copilot-secondary;
    }

    @media (max-width: $tablet) {
      img {
        width: 32px;
        height: 32px;
      }

      .MuiTypography-h6 {
        font-size: $font-size-lg;
      }
    }
  }

  &-input {
    margin-top: auto;
    position: relative;
    padding: $spacing-3;
    background-color: transparent;

    .copilot-modal__chat-textfield {
      .MuiOutlinedInput-root {
        background-color: #ffffff;
        border-radius: 28px;
        font-family: inherit;
        font-size: $font-size-md;
        line-height: 1.5;
        color: $text-primary;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        height: 56px;
        min-height: 56px;
        max-height: 56px;
        box-shadow: 0 2px 8px rgba($copilot-primary, 0.1);
        border: 1px solid $copilot-light;

        &.Mui-focused .MuiOutlinedInput-notchedOutline {
          border-color: $copilot-primary;
          border-width: 2px;
          box-shadow: 0 0 0 3px rgba($copilot-primary, 0.1);
        }

        &:hover .MuiOutlinedInput-notchedOutline {
          border-color: $copilot-secondary;
        }

        .MuiOutlinedInput-input {
          padding: $spacing-2 $spacing-3;
          height: 40px !important;
          max-height: 40px !important;
          overflow-y: auto !important;
          display: flex;
          align-items: center;
          font-size: 16px !important;
          font-weight: 400;
        }

        fieldset.MuiOutlinedInput-notchedOutline {
          border-width: 1px;
          border-radius: 28px;
        }

        @media (max-width: $tablet) {
          font-size: $font-size-sm;
          height: 48px;
          min-height: 48px;
          max-height: 48px;

          .MuiOutlinedInput-input {
            padding: $spacing-1 $spacing-2;
            height: 32px !important;
            max-height: 32px !important;
            font-size: 15px !important;
          }
        }
      }
    }

    .copilot-modal__chat-add {
      color: $copilot-primary;
      background-color: transparent !important;
      margin-left: $spacing-1;

      &:hover {
        background-color: rgba($copilot-primary, 0.1) !important;
      }
    }

    .copilot-modal__chat-mic {
      color: $copilot-primary;
      background-color: transparent !important;

      &:hover {
        background-color: rgba($copilot-primary, 0.1) !important;
      }
    }

    .copilot-modal__chat-send {
      color: #ffffff;
      background-color: $copilot-primary !important;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      margin-right: $spacing-1;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background-color: $copilot-secondary !important;
        transform: scale(1.05);
      }

      &:disabled {
        background-color: #c8c6c4 !important;
        color: #ffffff;
      }
    }
  }

  &-settings {
    width: 200px;
    background-color: #ffffff;
    padding: $spacing-4;
    border-radius: 12px;
    border: 1px solid $copilot-light;
    box-shadow: 0 4px 16px rgba($copilot-primary, 0.1);
    transition: transform 0.5s ease, opacity 0.5s ease;
    transform-origin: top right;

    &.closing {
      transform: scale(0.8);
      opacity: 0;
    }

    .settings-close-button {
      color: $copilot-primary;

      &:hover {
        background-color: rgba($copilot-primary, 0.1);
      }
    }

    .MuiTypography-h6 {
      margin-bottom: $spacing-4;
      color: $copilot-primary;
      font-weight: 600;
    }

    @media (max-width: $tablet) {
      width: calc(100% - ($spacing-3 * 2));
      padding: $spacing-3;
      margin-top: $spacing-3;

      .MuiTypography-h6 {
        font-size: $font-size-lg;
        margin-bottom: $spacing-3;
      }
    }
  }

  &-setting {
    margin-bottom: $spacing-3;

    .MuiTypography-root:not(.copilot-modal__setting-label) {
      color: $copilot-secondary;
      margin-bottom: $spacing-1;
      font-size: $font-size-sm;
    }

    .MuiIconButton-root {
      &:hover {
        background-color: rgba($copilot-primary, 0.1);
      }
    }

    .MuiSlider-root {
      color: $copilot-primary;

      .MuiSlider-thumb {
        background-color: $copilot-primary;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 8px rgba($copilot-primary, 0.3);

        &:hover {
          box-shadow: 0 0 0 8px rgba($copilot-primary, 0.16);
        }
      }

      .MuiSlider-track {
        background-color: $copilot-primary;
      }

      .MuiSlider-rail {
        background-color: $copilot-light;
      }
    }

    .MuiSelect-select {
      background-color: #ffffff !important;
    }

    .MuiOutlinedInput-root {
      background-color: #ffffff;
      border-radius: 8px;

      &:hover .MuiOutlinedInput-notchedOutline {
        border-color: $copilot-primary;
      }

      &.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: $copilot-primary;
        border-width: 2px;
      }
    }

    @media (max-width: $tablet) {
      margin-bottom: $spacing-2;

      .MuiTypography-root:not(.copilot-modal__setting-label) {
        font-size: $font-size-xs;
      }

      .MuiSlider-root {
        margin-bottom: $spacing-1;
      }
    }
  }

  &-footer {
    padding: $spacing-4;
    border-top: 1px solid $copilot-light;
    text-align: center;
    background-color: rgba($copilot-light, 0.3);

    @media (max-width: $tablet) {
      padding: $spacing-3;
      font-size: $font-size-xs;
    }
  }

  &-back {
    position: absolute;
    top: $spacing-4;
    left: $spacing-4;
    z-index: 1;

    &-button {
      color: $copilot-primary !important;
      text-transform: none !important;
      background-color: rgba(#ffffff, 0.9) !important;
      font-weight: $font-weight-medium !important;
      padding: $spacing-1 $spacing-2 !important;
      transition: all 0.3s ease !important;
      overflow: hidden !important;
      white-space: nowrap !important;
      border: 1px solid $copilot-light;
      border-radius: 8px;

      .MuiButton-startIcon {
        margin-right: 0;
        transition: margin-right 0.3s ease;
      }

      .back-text {
        display: inline-block;
        transition: opacity 0.2s ease, transform 0.3s ease;
        margin-left: $spacing-1;
      }

      &.collapsed {
        min-width: 32px;
        .back-text {
          opacity: 0;
          transform: translateX(-20px);
          width: 0;
          margin-left: 0;
        }
      }

      &:hover {
        background-color: rgba($copilot-primary, 0.1) !important;
        border-color: $copilot-primary;

        &.collapsed {
          padding-right: $spacing-2 !important;

          .back-text {
            opacity: 1;
            transform: translateX(0);
            width: max-content;
            margin-left: $spacing-1;
          }

          .MuiButton-startIcon {
            margin-right: $spacing-1;
          }
        }
      }
    }

    @media (max-width: $tablet) {
      position: static;
      padding: $spacing-3 $spacing-3 0 $spacing-3;

      &-button {
        font-size: $font-size-xs;
      }
    }
  }

  &-messages {
    display: flex;
    flex-direction: column;
    gap: $spacing-2;
    flex: 1;
    overflow-y: auto;
    padding: $spacing-3;
    padding-right: $spacing-4;
    height: calc(100% - 120px);

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba($copilot-primary, 0.3);
      border-radius: 3px;
    }

    // Styles for code blocks
    .code-block {
      position: relative;
      margin-bottom: $spacing-3;

      .copy-button {
        position: absolute;
        top: $spacing-1;
        right: $spacing-1;
        z-index: 1;
        background-color: rgba($copilot-primary, 0.1);
        color: $copilot-primary;

        &:hover {
          background-color: rgba($copilot-primary, 0.2);
        }
      }

      pre {
        margin: 0 !important;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid $copilot-light;
      }
    }
  }

  &-message {
    display: flex;
    align-items: flex-start;
    padding: $spacing-3;
    border-radius: 16px;
    max-width: 85%;
    word-break: break-word;

    &.user {
      background: linear-gradient(135deg, $copilot-primary 0%, $copilot-secondary 100%);
      color: #ffffff;
      margin-left: auto;
      border-radius: 16px 16px 4px 16px;
    }

    &.assistant {
      background-color: #ffffff;
      margin-right: auto;
      border: 1px solid $copilot-light;
      display: flex;
      align-items: flex-start;
      gap: $spacing-2;
      border-radius: 16px 16px 16px 4px;
      box-shadow: 0 2px 8px rgba($copilot-primary, 0.05);
    }
  }

  .assistant-avatar {
    margin-top: 2px;
    border-radius: 6px;
    flex-shrink: 0;
    filter: drop-shadow(0 1px 4px rgba($copilot-primary, 0.2));
  }
}

// Custom styles for Material UI ripple effect
.settings-ripple-visible {
  opacity: 0.8 !important;
  animation-duration: 850ms !important;
}

.settings-ripple-child {
  background-color: rgba($copilot-primary, 0.7) !important;
}

.settings-ripple-child-leaving {
  opacity: 0 !important;
  animation-duration: 850ms !important;
}

.copilot-modal__setting-label {
  font-size: $font-size-xs !important;
  font-weight: $font-weight-medium !important;
  color: $copilot-primary !important;

  @media (max-width: $tablet) {
    font-size: $font-size-xs !important;
  }
}
