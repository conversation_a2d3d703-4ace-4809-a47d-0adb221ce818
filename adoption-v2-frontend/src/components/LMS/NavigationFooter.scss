@use '../../styles/abstracts/variables' as *;

.navigation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: $spacing-3;
  position: relative !important;

  /* Styles for span element used with tooltip */
  > span {
    display: inline-flex;

    /* Style for span containing disabled button */
    &:has(button:disabled) {
      cursor: not-allowed;
    }
  }

  // Mobile tooltip improvements
  @media (max-width: 768px) {
    // Ensure tooltips are visible on mobile
    .MuiTooltip-tooltip {
      font-size: 14px !important;
      padding: 8px 12px !important;
      background-color: rgba(0, 0, 0, 0.9) !important;
      border-radius: 4px !important;
      max-width: 250px !important;
      z-index: 1500 !important;
    }

    .MuiTooltip-arrow {
      color: rgba(0, 0, 0, 0.9) !important;
    }
  }

  &__button {
    padding: $spacing-2 $spacing-3 !important;
    border-radius: $border-radius-sm !important;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    text-transform: none !important;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-1;
    .MuiButton-icon{
        margin-left:0 !important;
        margin-right:0 !important;
    }

    // Icon styles
    .MuiSvgIcon-root {
      font-size: 20px;
      transition: transform 0.3s ease;
    }

    &--previous {
      border: 1px solid $primary-color !important;
      color: $primary-color !important;
      background-color: transparent !important;
      padding: $spacing-2 $spacing-3 $spacing-2 $spacing-2 !important;

      &:hover {
        border-color: $primary-color !important;
        background-color: rgba($primary-color, 0.04);

        .MuiSvgIcon-root {
          transform: translateX(-2px);
        }
      }

      &:disabled {
        border-color: $border-color !important;
        color: $text-disabled !important;
        background-color: $bg-light !important;
        cursor: not-allowed;

        .MuiSvgIcon-root {
          opacity: 0.5 !important;
        }
      }

      &:active:not(:disabled) {
        transform: scale(0.98);
      }
    }

    &--next {
      border:1px solid $primary-color !important;
      background-color: $primary-color !important;
      color: $text-light !important;
      padding: $spacing-2 $spacing-2 $spacing-2 $spacing-3 !important;
      box-shadow: $shadow-sm;

      &:hover {
        background-color: $primary-color-dark !important;
        border-color: $primary-color-dark !important;
        box-shadow: $shadow-md !important;

        .MuiSvgIcon-root {
          transform: translateX(2px) !important;
        }
      }

      &:disabled {
        background-color: $primary-color !important;
        color: $bg-light !important;
        box-shadow: none !important;
        cursor: not-allowed !important;
        pointer-events: none !important; /* To prevent hover effect */

        .MuiSvgIcon-root {
          transform: none !important; /* Prevent hover animation in disabled state */
        }
      }

      &:active:not(:disabled) {
        transform: scale(0.98);
      }

      &.complete-course {
        min-width: 180px;
        text-transform: none !important;
        font-weight: $font-weight-semibold;
        background-color: $success-color;

        &:hover {
          background-color: $success-color-dark;
        }

        &:disabled {
          background-color: $bg-light-dark !important;
        }
      }
    }
  }
}
