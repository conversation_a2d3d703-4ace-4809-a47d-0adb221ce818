import { Box, Button, Tooltip } from '@mui/material';
import { NavigateBefore, NavigateNext } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import './NavigationFooter.scss';

const NavigationFooter = ({
  hasPrevious = false,
  hasNext = false,
  onPrevious = () => console.log('onPrevious not provided'),
  onNext = () => console.log('onNext not provided'),
  courseProgress = 0,
  isContentRequired = false,
  isContentCompleted = false,
  quizScore = null,
  quizPassingScore = 70,
  nextButtonProps = {
    text: 'Next',
    variant: 'contained',
    color: 'primary',
    disabled: false,
    tooltip: '',
    sx: { minWidth: '100px' },
  },
}) => {
  const { t } = useTranslation();

  // nextButtonProps.className içinde 'complete-course' sınıfı var mı kontrol et
  // veya nextButtonProps.text, çeviriden bağımsız olarak 'COMPLETE COURSE' değerine sahip mi
  const isCompleteCourseButton =
    (nextButtonProps.className && nextButtonProps.className.includes('complete-course')) ||
    nextButtonProps.text === t('course.navigation.completeCourse', 'COMPLETE COURSE') ||
    nextButtonProps.text === 'COMPLETE COURSE';

  const showIncompleteTooltip = isCompleteCourseButton && courseProgress < 100;

  const tooltipTitle = () => {
    if (nextButtonProps.disabled && nextButtonProps.tooltip) {
      return nextButtonProps.tooltip;
    }
    if (showIncompleteTooltip) {
      return t('course.navigation.tooltips.completeAllTopics', 'You must complete all topics');
    }
    if (isContentRequired && !isContentCompleted) {
      return t(
        'course.navigation.tooltips.interactContent',
        'You must interact with this content before proceeding'
      );
    }
    // Quiz geçme kontrolü - sadece passingScore 0'dan büyükse kontrol et
    // ve içerik tamamlanmamışsa göster
    const isQuizFailed =
      quizScore !== null &&
      quizPassingScore > 0 &&
      quizScore < quizPassingScore &&
      !isContentCompleted;
    if (isQuizFailed) {
      return t(
        'course.navigation.tooltips.passQuiz',
        'You must score at least ' + quizPassingScore + '% on the quiz to proceed'
      );
    }
    return '';
  };

  // İçerik tamamlanma kontrolü - En öncelikli kontrol
  const isContentComplete = !isContentRequired || isContentCompleted;

  // Quiz kontrolü:
  // 1. Quiz yoksa (quizScore === null) - pass olarak kabul et
  // 2. Quiz'in passingScore değeri 0 ise - HER ZAMAN pass olarak kabul et
  // 3. Quiz puanı passingScore'dan yüksekse - pass olarak kabul et
  // 4. İçerik tamamlanmışsa (isContentCompleted true ise) - quiz sonucuna bakılmaksızın pass olarak kabul et
  const isQuizPassed =
    quizScore === null || // Quiz yoksa geçti sayılır
    quizPassingScore === 0 || // Quiz geçiş notu 0 ise her zaman geçti sayılır (puandan bağımsız)
    quizScore >= quizPassingScore || // Quiz puanı geçiş notundan yüksekse geçti sayılır
    isContentCompleted; // İçerik tamamlanmışsa quiz sonucuna bakılmaksızın geçti sayılır

  // Kurs tamamlanma kontrolü
  const isCourseComplete = !isCompleteCourseButton || courseProgress >= 100;

  // Buton durumu - tüm kontrollerin AND ile birleştirilmesi
  const isButtonDisabled =
    nextButtonProps.disabled ||
    !hasNext ||
    !isContentComplete ||
    !isQuizPassed ||
    !isCourseComplete;

  // Previous butonu için tıklama işleyicisi
  const handlePreviousClick = () => {
    // hasPrevious true ise veya hiç kontrol etmek istemiyorsak her zaman çalıştır
    if (typeof onPrevious === 'function') {
      onPrevious();
    }
  };

  return (
    <Box className="navigation-footer">
      {/* Previous Button with Tooltip */}
      <Tooltip
        title={
          !hasPrevious ? t('course.navigation.tooltips.firstTopic', 'This is the first topic') : ''
        }
        placement="top"
        arrow
        disableHoverListener={hasPrevious}
        disableTouchListener={false}
        enterTouchDelay={0}
        leaveTouchDelay={1500}
      >
        <span>
          <Button
            className={`navigation-footer__button navigation-footer__button--previous`}
            variant="outlined"
            onClick={handlePreviousClick}
            disabled={!hasPrevious}
            startIcon={<NavigateBefore />}
          >
            {t('course.navigation.previous', 'Previous')}
          </Button>
        </span>
      </Tooltip>

      <Tooltip
        title={tooltipTitle()}
        placement="top"
        arrow
        disableHoverListener={!isButtonDisabled && !showIncompleteTooltip}
        disableTouchListener={false}
        enterTouchDelay={0}
        leaveTouchDelay={1500}
      >
        <span>
          <Button
            className={`navigation-footer__button navigation-footer__button--next ${
              nextButtonProps.className || (isCompleteCourseButton ? 'complete-course' : '')
            }`}
            variant={nextButtonProps.variant || 'contained'}
            color={nextButtonProps.color || 'primary'}
            onClick={onNext}
            disabled={isButtonDisabled}
            endIcon={<NavigateNext />}
            sx={nextButtonProps.sx}
          >
            {nextButtonProps.text}
          </Button>
        </span>
      </Tooltip>
    </Box>
  );
};

NavigationFooter.propTypes = {
  hasPrevious: PropTypes.bool,
  hasNext: PropTypes.bool,
  onPrevious: PropTypes.func,
  onNext: PropTypes.func,
  courseProgress: PropTypes.number,
  isContentRequired: PropTypes.bool,
  isContentCompleted: PropTypes.bool,
  quizScore: PropTypes.number,
  quizPassingScore: PropTypes.number,
  nextButtonProps: PropTypes.shape({
    text: PropTypes.string,
    variant: PropTypes.string,
    color: PropTypes.string,
    disabled: PropTypes.bool,
    tooltip: PropTypes.string,
    className: PropTypes.string,
    sx: PropTypes.object,
  }),
};

export default NavigationFooter;
