@use '../../../styles/abstracts/variables' as *;

/*
 * Topic Sidebar Main Component
 * --------------------------
 */
.topic-sidebar {
  width: 330px;
  min-width: 330px;
  background-color: #f8f9fa;
  box-shadow: inset -36px 0 40px -32px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease-in-out;
  border-right: 1px solid rgba(0, 0, 0, 0.04);
  display: none;
  position: sticky;
  top: 0;
  min-height: 100vh;

  // Mobile: Hidden by default, shown as overlay when open
  @media (max-width: calc($tablet - 1px)) {
    position: fixed;
    top: 0; // Start from top
    left: 0;
    z-index: 1300;
    height: 100vh; // Full height
    transform: translateX(-100%);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

    &--mobile-open {
      display: block;
      transform: translateX(0);
    }
  }

  // Show on tablet and larger screens
  @media (min-width: $tablet) {
    display: block;
    transform: none;
  }



  /* Mobile Backdrop */
  &__backdrop {
    position: fixed;
    top: 0; // Start from top
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1200;

    @media (min-width: $tablet) {
      display: none;
    }
  }

  /* Mobile Close Button */
  &__mobile-close {
    position: absolute !important;
    top: $spacing-3 !important;
    right: $spacing-3 !important;
    color: #5a6a7e !important;
    z-index: 10 !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;

    &:hover {
      background-color: rgba(255, 255, 255, 1) !important;
      color: #2c3e50 !important;
    }

    @media (min-width: $tablet) {
      display: none !important;
    }
  }
  
  /*
   * Course Information Area
   */
  &__course-info {
    padding: $spacing-3;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
  }
  
  &__course-name {
    font-size: $font-size-xxl !important;
    font-weight: $font-weight-semibold !important;
    color: #2c3e50 !important;
    padding: $spacing-2 !important;
    margin-bottom: $spacing-3 !important;
    @media (max-width:768px){
      padding-right:calc($spacing-4 * 2) !important;
      font-size: $font-size-xl !important;
    }
  }
  
  /*
   * Progress Section
   */
  &__progress-section {
    margin: 0 0 $spacing-2;
    background-color: #f5f8fa;
    border-radius: 6px;
    padding: $spacing-2;
  }
  
  &__progress-bar {
    margin-bottom: 0.5rem;
    
    &-container {
      position: relative;
      height: 4px;
      border-radius: 2px;
      background-color: #e9ecf1;
      overflow: hidden;
    }
    
    &-fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 2px;
      background-color: $success-color;
      transition: width 0.3s ease;
    }
  }
  
  &__progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-1;
  }
  
  &__progress-complete {
    font-size: $font-size-xs !important;
    font-weight: 500 !important;
    color: #516273 !important;
  }
  
  &__progress-topics {
    font-size: $font-size-xs !important;
    font-weight: 500 !important;
    color: #516273 !important;
  }
  
  /*
   * Chapters Area
   */
  &__chapters {
    height: 100%;
    overflow-y: auto;
  }
  
  &__chapter {
    &-title {
      display: flex;
      align-items: center;
      padding: $spacing-3 $spacing-4;
      cursor: pointer;
      color: #5a6a7e !important;
      transition: all 0.2s ease;
      gap: $spacing-2;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
      
      /* Active Chapter Title */
      &--active {
        background-color: #e6eef5;
        
        &:hover {
          background-color: #e6eef5;
        }
        
        /* Active Chapter Icons and Text */
        .topic-sidebar__chapter-icon {
          color: #4a6583 !important;
        }
        
        .topic-sidebar__chapter-text {
          color: #2c3e50 !important;
        }
        
        .icon {
          color: #4a6583 !important;
        }
      }
    }
    
    &-icon {
      font-size: $font-size-xl !important;
      color: #8c99a5 !important;
      
      /* Completed Chapter Icon */
      &--completed {
        color: #68b984 !important;
      }
      
      /* Active Chapter Icon */
      &--active {
        color: #4a6583 !important;
      }
    }
    
    &-text {
      flex: 1;
      color: #5a6a7e !important;
      font-size: calc($font-size-md - 1px) !important;
      font-weight: $font-weight-medium !important;
    }
    
    .icon {
      font-size: $font-size-xl !important;
      color: #8c99a5 !important;
    }
  }
  
  /*
   * Topics Area
   */
  &__topics {
    padding: $spacing-2 0;
    background-color: rgba(0, 0, 0, 0.01);
  }
  
  &__topic {
    padding: $spacing-2 $spacing-4 $spacing-2 $spacing-6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: $spacing-1;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    /* Active Topic */
    &--active {
      background-color: #e6eef5 !important;
      
      /* All Elements Inside Active Topic */
      .topic-sidebar__topic-icon {
        color: #4a6583 !important;
      }
      
      .topic-sidebar__topic-title {
        color: #2c3e50 !important;
        font-weight: $font-weight-medium !important;
      }
      
      .topic-sidebar__topic-type-icon,
      .topic-sidebar__topic-type-text {
        color: #5a6a7e !important;
      }
    }
    
    /* Topic Header Row */
    &-header {
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }
    
    /* Topic Icon */
    &-icon {
      font-size: $font-size-lg !important;
      color: #8c99a5 !important;
      
      &--active {
        color: #4a6583 !important;
      }
      
      &--completed {
        color: #68b984 !important;
      }
    }
    
    /* Topic Title */
    &-title {
      flex: 1;
      color: #5a6a7e !important;
      font-size: $font-size-sm !important;
      font-weight: $font-weight-regular !important;
    }
    
    /* Topic Content */
    &-content {
      margin-left: calc($spacing-4 + $spacing-1);
    }
    
    /* Topic Meta Information */
    &-meta {
      display: flex;
      gap: $spacing-3;
    }
    
    /* Topic Type */
    &-type {
      display: flex;
      align-items: center;
      gap: $spacing-1;
      
      &-icon {
        font-size: $font-size-md !important;
        color: #a0aab5 !important;
      }
      
      &-text {
        color: #a0aab5 !important;
        font-size: $font-size-xs !important;
      }
    }
  }
} 