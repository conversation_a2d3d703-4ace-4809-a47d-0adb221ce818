import { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Box, CircularProgress, Alert, Paper, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import {
  fetchCourseDetails,
  updateProgress,
  fetchProgress,
  selectCurrentCourse,
} from '../../../redux/features/courses/courseSlice';

// Components
import TopicSidebar from './TopicSidebar';
import TopicContent from './TopicContent';
import TopicHeader from './TopicHeader';
import NavigationFooter from '../NavigationFooter';
import { toast } from 'react-toastify';
import './TopicView.scss';
import useCourseProgress from '../../../hooks/useCourseProgress';

const TopicView = ({ courseId, chapterId, topicId, onCourseComplete }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const user = useSelector((state) => state.auth.user);

  // Instead of directly fetching from state.courses, we use selector
  const course = useSelector(selectCurrentCourse);
  const { loading, error, currentProgress } = useSelector((state) => state.courses);

  // Calculate progress using useCourseProgress hook
  const {
    percentage: progress,
    isLastTopic: isLastTopicFn,
    isTopicCompleted: hookIsTopicCompleted,
    updateProgress: updateProgressHook,
  } = useCourseProgress(course, user?._id, currentProgress);

  const [currentTopic, setCurrentTopic] = useState(null);
  const [localizedCourse, setLocalizedCourse] = useState(null);
  const [transcript, setTranscript] = useState([]);
  const [duration, setDuration] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [slideDirection, setSlideDirection] = useState('left');
  const [isCourseCompleteModalOpen, setIsCourseCompleteModalOpen] = useState(false);
  const [isUpdatingProgress, setIsUpdatingProgress] = useState(false);
  const [quizScore, setQuizScore] = useState(null);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [isContentCompleted, setIsContentCompleted] = useState(false);
  // New state: Checks if there is an interactive playground in the content
  const [hasInteractivePlayground, setHasInteractivePlayground] = useState(false);
  // State to track video completion status
  const [completedVideos, setCompletedVideos] = useState({});
  const [videoPlayerKey, setVideoPlayerKey] = useState(0);
  // Video position tracking
  const [currentTime, setCurrentTime] = useState(0);
  // Add a new state to check if the topic contains a form
  const [hasForm, setHasForm] = useState(false);
  // 1. First, add a state to detect topics containing DALL-E usecase
  const [hasDallEUsecase, setHasDallEUsecase] = useState(false);
  // 1. Add a new state
  const [isDallECompleted, setIsDallECompleted] = useState(false);
  // Add state for quiz passing score
  const [quizPassingScore, setQuizPassingScore] = useState(70);
  // Add new states to track video and quiz completion status
  const [isVideoCompleted, setIsVideoCompleted] = useState(false);
  const [isQuizCompleted, setIsQuizCompleted] = useState(false);

  // More flexible structure to track completion status of content blocks
  const [completedBlocks, setCompletedBlocks] = useState({
    // We keep an ID->boolean/object map for each content type
    video: {}, // { videoId1: true, videoId2: true }
    quiz: {}, // { quizId1: {score: 80, isPassing: true}, quizId2: {score: 60, isPassing: false} }
    form: {}, // { formId1: true, formId2: true }
    usecase: {}, // { usecaseId1: true }
    interactive: {}, // { interactiveId1: true }
    text: {}, // { textId1: true } Text content is automatically completed
    pdf: {}, // { pdfId1: true } PDF content is automatically completed
  });

  // Key used to re-render TopicSidebar
  const [sidebarKey, setSidebarKey] = useState(0);

  // Mobile sidebar state
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Reference variable to load course only once
  const hasLoadedCourseRef = useRef(false);
  const prevCourseIdRef = useRef(courseId);

  // Quiz completion flag
  const quizCompletionProcessingRef = useRef(false);

  // Helper function to check content completion status
  const checkContentCompletion = useCallback(() => {
    if (!currentTopic) return false;

    // Group content blocks by type
    const contentBlocksByType = {};

    // Return false if there are no content blocks or if they are empty
    if (!currentTopic.contentBlocks || currentTopic.contentBlocks.length === 0) {
      return false;
    }

    // Group all content blocks by type
    (currentTopic.contentBlocks || []).forEach((block) => {
      // Normalize block type (some blocks use special fields like usecase_slug or playground_type)
      let blockType = block.type;

      // Check for usecase type
      if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
        blockType = 'usecase';
      }

      // Check for interactive type
      if (
        block.playground_type &&
        block.playground_type !== 'none' &&
        blockType !== 'interactive'
      ) {
        blockType = 'interactive';
      }

      if (!contentBlocksByType[blockType]) {
        contentBlocksByType[blockType] = [];
      }

      contentBlocksByType[blockType].push(block);
    });

    // If there is only Text and PDF content, consider it automatically completed
    const blockTypes = Object.keys(contentBlocksByType);
    const hasOnlyTextAndPdf =
      blockTypes.length > 0 && blockTypes.every((type) => type === 'text' || type === 'pdf');

    if (hasOnlyTextAndPdf) {
      return true;
    }

    // Check completion for each group
    for (const [blockType, blocks] of Object.entries(contentBlocksByType)) {
      if (blocks.length === 0) continue;

      // Skip Text and PDF content
      if (blockType === 'text' || blockType === 'pdf') {
        continue; // We don't check Text and PDF content
      }

      // Check for each block type
      switch (blockType) {
        case 'video':
          // Are all videos completed?
          const allVideosCompleted = blocks.every(
            (block) => completedBlocks.video[block._id] === true
          );
          if (!allVideosCompleted) return false;
          break;

        case 'quiz':
          // Are all quizzes completed?
          const allQuizzesCompleted = blocks.every((block) => {
            // Check quiz passingScore
            const passingScore =
              block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;

            // If passingScore is 0, always pass
            if (passingScore === 0) return true;

            // Check if quiz is completed
            const quizStatus = completedBlocks.quiz[block._id];
            const isScorePassing = quizStatus?.score >= passingScore;
            const isQuizPassing = quizStatus?.isPassing === true || isScorePassing;

            return isQuizPassing;
          });
          if (!allQuizzesCompleted) return false;
          break;

        case 'form':
          // Are all forms completed?
          const allFormsCompleted = blocks.every(
            (block) => completedBlocks.form[block._id] === true
          );
          if (!allFormsCompleted) return false;
          break;

        case 'usecase':
          // Are all usecases completed?
          const allUsecasesCompleted = blocks.every(
            (block) => completedBlocks.usecase[block._id] === true
          );
          if (!allUsecasesCompleted) return false;
          break;

        case 'interactive':
          // Are all interactive contents completed?
          const allInteractivesCompleted = blocks.every(
            (block) => completedBlocks.interactive[block._id] === true
          );
          if (!allInteractivesCompleted) return false;
          break;

        default:
          // Checkpoint for unrecognized content types (new types may be added in the future)
          return false;
      }
    }

    // If all checks pass, content is completed
    return true;
  }, [currentTopic, completedBlocks]);

  // Helper function: URL creation (we no longer add cardId)
  const getTopicUrl = useCallback((courseId, chapterId, topicId) => {
    return `/course/learn/${courseId}/${chapterId}/${topicId}`;
  }, []);

  // Mobile sidebar toggle function
  const toggleMobileSidebar = useCallback(() => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  }, [isMobileSidebarOpen]);

  // Helper function to process translations in course data and apply translations for current language
  const processTranslations = useCallback(
    (courseData) => {
      if (!courseData || !courseData.translations) return courseData;

      // First check if there is a translation for the current language
      const translatedContent =
        courseData.translations[currentLanguage] || courseData.translations.en || {};

      // Use all data from translation - no data will be taken from original, including ID
      return {
        ...translatedContent,
      };
    },
    [currentLanguage]
  );

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial loading check

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Process translations when course data or language changes
  useEffect(() => {
    if (course) {
      const processedCourse = processTranslations(course);
      setLocalizedCourse(processedCourse);
    }
  }, [course, processTranslations, currentLanguage]);

  // Memoize topic completion status
  const isTopicCompleted = useMemo(() => {
    return hookIsTopicCompleted(chapterId, topicId);
  }, [hookIsTopicCompleted, chapterId, topicId, currentProgress]); // Add currentProgress as dependency

  useEffect(() => {
    if (localizedCourse && currentTopic) {
      try {
        let currentTranscript = [];

        if (
          currentTopic.video &&
          localizedCourse.transcripts &&
          Array.isArray(localizedCourse.transcripts)
        ) {
          const transcriptData = localizedCourse.transcripts.find(
            (t) => t.chapterId === chapterId && t.topicId === topicId
          );
          if (transcriptData && transcriptData.transcript) {
            currentTranscript = transcriptData.transcript;
          }
        }

        setTranscript(currentTranscript);
      } catch (error) {}
    }
  }, [localizedCourse, currentTopic, chapterId, topicId]);

  useEffect(() => {
    setIsUpdatingProgress(false);
    setIsContentCompleted(false);
    setQuizScore(null);
    setQuizPassingScore(70);
    setHasInteractivePlayground(false);

    // Reset the completion status of all content blocks
    const initialCompletedBlocks = {
      video: {},
      quiz: {},
      form: {},
      usecase: {},
      interactive: {},
      text: {},
      pdf: {},
    };

    // If there is no topic, consider the content already completed
    if (!currentTopic || !currentTopic.contentBlocks) {
      setCompletedBlocks(initialCompletedBlocks);
      return;
    }

    // Classify content blocks by type
    const contentTypes = new Set();

    // Mark text and PDF content as automatically completed
    currentTopic.contentBlocks.forEach((block) => {
      // Normalize block type (some blocks use special fields like usecase_slug or playground_type)
      let blockType = block.type;

      // Check for usecase type
      if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
        blockType = 'usecase';
        contentTypes.add('usecase');
      }

      // Check for interactive type
      if (
        block.playground_type &&
        block.playground_type !== 'none' &&
        blockType !== 'interactive'
      ) {
        blockType = 'interactive';
        contentTypes.add('interactive');
        setHasInteractivePlayground(true);
      }

      // Check for form content
      if (blockType === 'form') {
        contentTypes.add('form');
        setHasForm(true);
      }

      // Check for quiz content
      if (blockType === 'quiz') {
        contentTypes.add('quiz');
      }

      // Check for video content
      if (blockType === 'video') {
        contentTypes.add('video');
      }

      // Register text and PDF content as content types but mark as automatically completed
      if (blockType === 'text') {
        contentTypes.add('text');
        initialCompletedBlocks.text[block._id] = true; // Automatically complete text content
      }

      if (blockType === 'pdf') {
        contentTypes.add('pdf');
        initialCompletedBlocks.pdf[block._id] = true; // Automatically complete PDF content
      }
    });

    // If there is only text and PDF content, automatically consider the content completed
    const hasOnlyTextAndPdf =
      contentTypes.size > 0 && [...contentTypes].every((type) => type === 'text' || type === 'pdf');

    if (hasOnlyTextAndPdf) {
      setIsContentCompleted(true);
    }

    // Update states
    setCompletedBlocks(initialCompletedBlocks);

    // Check content types - no content will be automatically completed anymore
    setIsContentCompleted(false);

    // Reset video player key
    const timestamp = new Date().getTime();
    setVideoPlayerKey(timestamp);
  }, [topicId, currentTopic]);

  useEffect(() => {
    if (prevCourseIdRef.current !== courseId || !hasLoadedCourseRef.current) {
      prevCourseIdRef.current = courseId;

      hasLoadedCourseRef.current = false;

      const loadCourseData = async () => {
        try {
          try {
            await dispatch(fetchCourseDetails(courseId)).unwrap();
            hasLoadedCourseRef.current = true;
          } catch (error) {}

          try {
            await dispatch(fetchProgress(courseId)).unwrap();
          } catch (error) {}
        } catch (error) {}
      };

      loadCourseData();
    }
  }, [courseId, dispatch]);

  useEffect(() => {
    if (!localizedCourse?.chapters || !chapterId || !topicId) {
      return;
    }

    const chapter = localizedCourse.chapters.find((ch) => ch._id === chapterId) || null;

    if (!chapter) {
      // First try to reload course data
      dispatch(fetchCourseDetails(courseId))
        .unwrap()
        .then(() => {})
        .catch((error) => {
          toast.error('Course data could not be loaded. Redirecting to course page...');
          navigate(`/course/${courseId}`);
        });

      return;
    }

    const topic = chapter?.topics?.find((t) => t._id === topicId);

    if (!topic) {
      navigate(`/course/${courseId}`);
      return;
    }

    setCurrentTopic(topic);
    setDuration(topic.video?.duration || 0);
  }, [localizedCourse, chapterId, topicId, courseId, navigate, dispatch]);

  // useEffect to reset completion status when topicId changes
  useEffect(() => {
    if (!currentTopic) return; // If topic is not loaded, do nothing

    // First reset all states
    setCompletedVideos({});
    setIsContentCompleted(false);
    setIsVideoCompleted(false);
    setIsQuizCompleted(false);

    // Reset the completion status of all content blocks
    const initialCompletedBlocks = {
      video: {},
      quiz: {},
      form: {},
      usecase: {},
      interactive: {},
      text: {},
      pdf: {},
    };

    // Check content blocks
    if (currentTopic.contentBlocks && currentTopic.contentBlocks.length > 0) {
      // Classify content blocks by type
      const contentTypes = new Set();

      // Collect text and PDF content
      currentTopic.contentBlocks.forEach((block) => {
        // Normalize block type (some blocks use special fields like usecase_slug or playground_type)
        let blockType = block.type;

        // Check for usecase type
        if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
          blockType = 'usecase';
          contentTypes.add('usecase');
        }

        // Check for interactive type
        if (
          block.playground_type &&
          block.playground_type !== 'none' &&
          blockType !== 'interactive'
        ) {
          blockType = 'interactive';
          contentTypes.add('interactive');
          setHasInteractivePlayground(true);
        }

        // Check for form content
        if (blockType === 'form') {
          contentTypes.add('form');
          setHasForm(true);
        }

        // Check for quiz content
        if (blockType === 'quiz') {
          contentTypes.add('quiz');
        }

        // Check for video content
        if (blockType === 'video') {
          contentTypes.add('video');
        }

        // Always mark text content as automatically completed
        if (blockType === 'text') {
          contentTypes.add('text');
          initialCompletedBlocks.text[block._id] = true; // Automatically complete text content
        }

        // Add PDFs to content types set, will check completion below
        if (blockType === 'pdf') {
          contentTypes.add('pdf');
        }
      });

      // Convert content types list to array
      const contentTypesArray = [...contentTypes];

      // If there is only text and PDF content, mark PDFs as completed automatically
      // If there are other content types (video, quiz, etc.), PDFs are not automatically completed
      const hasOnlyTextAndPdf =
        contentTypesArray.length > 0 &&
        contentTypesArray.every((type) => type === 'text' || type === 'pdf');

      if (hasOnlyTextAndPdf) {
        setIsContentCompleted(true);
      } else {
        // In mixed content types, do not automatically complete PDFs
      }
    }

    // Update states
    setCompletedBlocks(initialCompletedBlocks);

    // Reset video player key
    const timestamp = new Date().getTime();
    setVideoPlayerKey(timestamp);
  }, [chapterId, topicId, currentTopic]);

  // useEffect to monitor topic content completion status
  useEffect(() => {
    // Do not process if topic is already completed or update is in progress
    if (isTopicCompleted || isUpdatingProgress || !currentTopic) return;

    // Check if content is completed
    const allContentCompleted = checkContentCompletion();

    // Check if content is completed and mark if not previously completed
    if (allContentCompleted && !isContentCompleted) {
      setIsContentCompleted(true);
    } else if (!allContentCompleted && isContentCompleted) {
      setIsContentCompleted(false);
    }
  }, [
    currentTopic,
    topicId,
    chapterId,
    isTopicCompleted,
    isUpdatingProgress,
    completedBlocks,
    isContentCompleted,
    checkContentCompletion,
  ]);

  // Re-render TopicSidebar when currentProgress changes
  useEffect(() => {
    if (currentProgress) {
      // Increment key to re-render sidebar
      setSidebarKey((prevKey) => prevKey + 1);
    }
  }, [currentProgress]);

  // useEffect to automatically update progress when content is completed
  useEffect(() => {
    // When content is completed and topic is not yet marked
    if (isContentCompleted && !isTopicCompleted && !isUpdatingProgress) {
      // IMPORTANT CHANGE: Automatic progress update without waiting for Next button click
      const updateTopicProgress = async () => {
        try {
          setIsUpdatingProgress(true);

          const progressData = {
            chapterId,
            topicId,
            completed: true,
            progress: 1,
            courseComplateStatus: 'completed',
          };

          // Update progress with Redux thunk
          const result = await dispatch(updateProgress({ courseId, progressData })).unwrap();

          // Refresh progress data - this only updates progress data without changing course content
          await dispatch(fetchProgress(courseId)).unwrap();
        } catch (error) {
          toast.error(
            t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
            {
              position: 'bottom-right',
              autoClose: 3000,
            }
          );
        } finally {
          setIsUpdatingProgress(false);
        }
      };

      // Make API call
      updateTopicProgress();
    }
  }, [
    isContentCompleted,
    isTopicCompleted,
    isUpdatingProgress,
    chapterId,
    topicId,
    courseId,
    dispatch,
    t,
  ]);

  // Monitor state changes
  useEffect(() => {
    // Monitor state changes
  }, [isContentCompleted, isTopicCompleted, completedBlocks, hasInteractivePlayground]);

  const handleNextClick = async () => {
    // Check content types to detect if there is only PDF and Text content
    const contentTypes = new Set();
    let hasOnlyTextAndPdf = false;

    if (currentTopic?.contentBlocks) {
      currentTopic.contentBlocks.forEach((block) => {
        let blockType = block.type;

        // Check usecase type
        if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
          blockType = 'usecase';
          contentTypes.add('usecase');
        }

        // Check interactive type
        if (
          block.playground_type &&
          block.playground_type !== 'none' &&
          blockType !== 'interactive'
        ) {
          blockType = 'interactive';
          contentTypes.add('interactive');
        }

        if (blockType === 'form') contentTypes.add('form');
        if (blockType === 'quiz') contentTypes.add('quiz');
        if (blockType === 'video') contentTypes.add('video');
        if (blockType === 'text') contentTypes.add('text');
        if (blockType === 'pdf') contentTypes.add('pdf');
      });

      // If there is only text and/or pdf content and no other content types
      hasOnlyTextAndPdf =
        contentTypes.size > 0 &&
        [...contentTypes].every((type) => type === 'text' || type === 'pdf');
    }

    // Update progress if content is completed but topic is not yet marked as completed
    // We bypass isContentCompleted check for topics with PDF/Text content
    if (!isTopicCompleted && (isContentCompleted || hasOnlyTextAndPdf)) {
      try {
        setIsUpdatingProgress(true);

        const progressData = {
          chapterId,
          topicId,
          completed: true,
          progress: 1,
          courseComplateStatus: 'completed',
        };

        // Update progress with Redux thunk
        await dispatch(updateProgress({ courseId, progressData })).unwrap();

        // Refresh progress data - this only updates progress data without changing course content
        await dispatch(fetchProgress(courseId)).unwrap();

        // Show success message
        toast.success(t('courseContent.topicCompleted', 'Topic completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      } catch (error) {
        toast.error(
          t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      } finally {
        setIsUpdatingProgress(false);
      }
    }

    // Check if this is the last topic in the course
    const isLastTopicInCourse = isLastTopicFn?.(chapterId, topicId) || false;

    // If it's the last topic, trigger the course completion handler from props
    if (isLastTopicInCourse) {
      if (onCourseComplete) {
        onCourseComplete();
      }
    } else {
      // Clear states - before moving to next topic
      setCompletedVideos({});
      setIsContentCompleted(false);
      setIsVideoCompleted(false);
      setIsQuizCompleted(false);
      setCompletedBlocks({
        video: {},
        quiz: {},
        form: {},
        usecase: {},
        interactive: {},
        text: {},
        pdf: {},
      });

      // First clear states, then do navigation
      // This will prevent state transfer
      setTimeout(() => {
        navigateToNextContent();
      }, 50);
    }
  };

  // Function to complete topic directly when both form and quiz are completed in the last topic
  const checkFormAndQuizCompletion = useCallback(() => {
    // Only work for the last topic
    const isLastTopicInCourse = isLastTopicFn?.(chapterId, topicId) || false;
    if (!isLastTopicInCourse || isTopicCompleted || isUpdatingProgress || !currentTopic) return;

    // Check content types
    const contentTypes = new Set();
    const completedContentTypes = new Set();

    // Find all blocks containing form and quiz
    const formBlocks = [];
    const quizBlocks = [];

    if (currentTopic?.contentBlocks) {
      // Detect content types and completion status
      currentTopic.contentBlocks.forEach((block) => {
        let blockType = block.type;

        // Check form content
        if (blockType === 'form') {
          contentTypes.add('form');
          formBlocks.push(block);
          // Check completion status for each form block
          if (completedBlocks.form[block._id]) {
            // Add to list if each form is completed
            formBlocks.push(block._id);
          }
        }

        // Check quiz content
        if (blockType === 'quiz') {
          contentTypes.add('quiz');
          quizBlocks.push(block);
          // Check quiz passingScore
          const passingScore =
            block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;
          // If quiz is passed
          if (completedBlocks.quiz[block._id]?.isPassing || passingScore === 0) {
            // Add to list if each quiz is completed
            quizBlocks.push(block._id);
          }
        }
      });
    }

    // Form and quiz check - check if all form and quiz blocks are completed
    const hasForm = contentTypes.has('form');
    const hasQuiz = contentTypes.has('quiz');

    // Form check - is there at least one form and are all forms completed?
    const isFormCompleted =
      !hasForm ||
      (formBlocks.length > 0 &&
        formBlocks.every((block) =>
          typeof block === 'string' ? true : completedBlocks.form[block._id]
        ));

    // Quiz check - is there at least one quiz and are all quizzes passed?
    const isQuizCompleted =
      !hasQuiz ||
      (quizBlocks.length > 0 &&
        quizBlocks.every((block) => {
          if (typeof block === 'string') return true;

          const passingScore =
            block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;
          // Always pass if passingScore is 0
          if (passingScore === 0) return true;

          // Check quiz completion status
          return completedBlocks.quiz[block._id]?.isPassing === true;
        }));

    // If both are completed, automatically complete the topic
    if (isFormCompleted && isQuizCompleted) {
      // Automatically complete the topic
      const updateLastTopicProgress = async () => {
        try {
          setIsUpdatingProgress(true);

          const progressData = {
            chapterId,
            topicId,
            completed: true,
            progress: 1,
            courseComplateStatus: 'completed',
          };

          // Update progress with Redux thunk
          const result = await dispatch(updateProgress({ courseId, progressData })).unwrap();

          // Refresh progress data
          await dispatch(fetchProgress(courseId)).unwrap();

          // Show success message
          toast.success(
            t(
              'courseContent.courseReadyToComplete',
              'Course ready to complete! Click Complete Course button.'
            ),
            {
              position: 'bottom-right',
              autoClose: 5000,
            }
          );
        } catch (error) {
          // Continue silently in case of error
        } finally {
          setIsUpdatingProgress(false);
        }
      };

      // Complete the topic
      updateLastTopicProgress();
    }
  }, [
    isLastTopicFn,
    chapterId,
    topicId,
    isTopicCompleted,
    isUpdatingProgress,
    currentTopic,
    completedBlocks,
    courseId,
    dispatch,
    t,
  ]);

  // Function to handle form submission
  const handleFormSubmit = (formId) => {
    // Check if form is already completed
    if (completedBlocks.form[formId]) {
      return;
    }

    // Update completedBlocks state - save completed forms
    setCompletedBlocks((prev) => {
      const updatedState = {
        ...prev,
        form: {
          ...prev.form,
          [formId]: true,
        },
      };

      return updatedState;
    });

    // Show notification
    toast.success(t('courseContent.formSubmitted', 'Form submitted successfully!'), {
      position: 'bottom-right',
      autoClose: 3000,
    });

    // Wait for state update to complete and check content completion status
    setTimeout(() => {
      // Re-check content completion status
      const isAllContentCompleted = checkContentCompletion();

      if (isAllContentCompleted) {
        setIsContentCompleted(true);

        // Show notification when all content is completed
        toast.success(t('courseContent.contentCompleted', 'All content completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      }

      // IMPORTANT: Check form+quiz completion in the last topic
      checkFormAndQuizCompletion();
    }, 100);
  };

  // Handler function to be called when quiz is completed
  const handleQuizComplete = useCallback(
    async (quizId, score, totalPoints, passingScore = 70) => {
      try {
        if (isUpdatingProgress || !quizId) return;

        // Calculate isPassing - clear conditions
        let isPassing = false;

        // Always pass if passingScore is 0
        if (passingScore === 0) {
          isPassing = true;
        } else {
          // Otherwise check if score is greater than or equal to passingScore
          isPassing = score >= passingScore;
        }

        // Update quiz score
        setQuizScore(score);
        setQuizPassingScore(passingScore);

        // Save completion status to completedBlocks state
        setCompletedBlocks((prev) => {
          // First prepare updated state
          const updatedState = {
            ...prev,
            quiz: {
              ...prev.quiz,
              [quizId]: {
                score,
                isPassing,
                totalPoints,
                passingScore,
                completedAt: new Date().toISOString(),
              },
            },
          };

          return updatedState;
        });

        // Save quiz progress to API - they can use this if they want
        try {
          // API call can be made here
        } catch (error) {
          // Continue silently in case of error
        }

        // Check progress status if quiz is passed
        if (isPassing) {
          // Show notification
          toast.success(t('courseContent.quizPassed', 'Quiz passed successfully!'), {
            position: 'bottom-right',
            autoClose: 3000,
          });

          // Wait for state update to complete and check content completion status
          setTimeout(() => {
            // Re-check content completion status
            const isAllContentCompleted = checkContentCompletion();

            if (isAllContentCompleted) {
              setIsContentCompleted(true);

              // Show notification when all content is completed
              toast.success(
                t('courseContent.contentCompleted', 'All content completed successfully!'),
                {
                  position: 'bottom-right',
                  autoClose: 3000,
                }
              );
            }

            // IMPORTANT: Check form+quiz completion in the last topic
            checkFormAndQuizCompletion();
          }, 500);
        } else {
          // Don't show warning if topic was previously completed
          const isTopicPreviouslyCompleted =
            isTopicCompleted ||
            (progress && topicId && progress.completedTopics?.includes(topicId));

          if (!isTopicPreviouslyCompleted) {
            // Show notification if quiz is not passed and topic was not previously completed
            toast.warning(t('courseContent.quizFailed', 'You did not pass the quiz. Try again!'), {
              position: 'bottom-right',
              autoClose: 3000,
            });
          }
        }
      } catch (error) {
        toast.error(
          t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      }
    },
    [
      t,
      isUpdatingProgress,
      checkContentCompletion,
      checkFormAndQuizCompletion,
      currentProgress,
      chapterId,
    ]
  );

  const navigateToNextContent = async () => {
    if (!localizedCourse || !chapterId || !topicId) return;

    const currentChapterIndex = localizedCourse.chapters.findIndex((ch) => ch._id === chapterId);
    if (currentChapterIndex === -1) return;

    const currentChapter = localizedCourse.chapters[currentChapterIndex];
    if (!currentChapter || !currentChapter.topics) return;

    const currentTopicIndex = currentChapter.topics.findIndex((t) => t._id === topicId);
    if (currentTopicIndex === -1) return;

    let nextUrl = '';

    if (currentTopicIndex < currentChapter.topics.length - 1) {
      // Go to next topic
      const nextTopic = currentChapter.topics[currentTopicIndex + 1];
      nextUrl = getTopicUrl(courseId, chapterId, nextTopic._id);
    } else if (currentChapterIndex < localizedCourse.chapters.length - 1) {
      // Go to first topic of next chapter
      const nextChapter = localizedCourse.chapters[currentChapterIndex + 1];
      if (nextChapter.topics && nextChapter.topics.length > 0) {
        const firstTopic = nextChapter.topics[0];
        nextUrl = getTopicUrl(courseId, nextChapter._id, firstTopic._id);
      } else {
        nextUrl = getTopicUrl(courseId, courseId, courseId);
      }
    }
    const isLastChapter = currentChapterIndex === localizedCourse.chapters.length - 1;
    const isLastTopicInChapter = currentTopicIndex === currentChapter.topics.length - 1;

    // Redirect if URL is created
    if (nextUrl) {
      navigate(nextUrl);
    }
  };

  const navigateToPreviousContent = async () => {
    try {
      if (!localizedCourse || !chapterId || !topicId) {
        return;
      }

      // Check if chapters array exists - using localizedCourse
      if (
        !localizedCourse.chapters ||
        !Array.isArray(localizedCourse.chapters) ||
        localizedCourse.chapters.length === 0
      ) {
        return;
      }

      const currentChapterIndex = localizedCourse.chapters.findIndex(
        (ch) => ch && ch._id === chapterId
      );
      if (currentChapterIndex === -1) {
        return;
      }

      const currentChapter = localizedCourse.chapters[currentChapterIndex];
      if (!currentChapter || !currentChapter.topics || !Array.isArray(currentChapter.topics)) {
        return;
      }

      const currentTopicIndex = currentChapter.topics.findIndex((t) => t && t._id === topicId);
      if (currentTopicIndex === -1) {
        return;
      }

      let prevUrl = '';

      if (currentTopicIndex > 0) {
        // Go to previous topic
        const previousTopic = currentChapter.topics[currentTopicIndex - 1];
        if (previousTopic && previousTopic._id) {
          prevUrl = getTopicUrl(courseId, chapterId, previousTopic._id);
        }
      } else if (currentChapterIndex > 0) {
        // Go to last topic of previous chapter
        const previousChapter = localizedCourse.chapters[currentChapterIndex - 1];
        if (
          previousChapter &&
          previousChapter.topics &&
          Array.isArray(previousChapter.topics) &&
          previousChapter.topics.length > 0
        ) {
          const lastTopic = previousChapter.topics[previousChapter.topics.length - 1];
          if (lastTopic && lastTopic._id) {
            prevUrl = getTopicUrl(courseId, previousChapter._id, lastTopic._id);
          }
        } else {
          prevUrl = getTopicUrl(courseId, courseId, courseId);
        }
      }

      // Redirect if URL is created
      if (prevUrl) {
        navigate(prevUrl);
      }
    } catch (error) {}
  };

  // Function to be called when usecase is generated
  const handleImageGenerate = useCallback(
    async (usecaseId) => {
      try {
        // Update usecase completion status
        setCompletedBlocks((prev) => ({
          ...prev,
          usecase: {
            ...prev.usecase,
            [usecaseId]: true,
          },
        }));

        // Check content completion status
        const isAllContentCompleted = checkContentCompletion();
        if (isAllContentCompleted) {
          setIsContentCompleted(true);

          // Show general notification when all content is completed
          toast.success(
            t('courseContent.contentCompleted', 'All content completed successfully!'),
            {
              position: 'bottom-right',
              autoClose: 3000,
            }
          );
        } else {
          // Show notification for UseCase - even if content is not fully completed
          toast.success(t('courseContent.usecaseCompleted', 'Use case completed successfully!'), {
            position: 'bottom-right',
            autoClose: 3000,
          });
        }
      } catch (error) {
        toast.error(
          t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      }
    },
    [t, checkContentCompletion]
  );

  // Function to be called when interactive content is completed
  const handleInteractiveContentComplete = useCallback(
    (interactiveId) => {
      // Update interactive completion status
      setCompletedBlocks((prev) => ({
        ...prev,
        interactive: {
          ...prev.interactive,
          [interactiveId]: true,
        },
      }));

      // Check content completion status
      const isAllContentCompleted = checkContentCompletion();
      if (isAllContentCompleted) {
        setIsContentCompleted(true);

        // Show general notification when all content is completed
        toast.success(t('courseContent.contentCompleted', 'All content completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      } else {
        // If all content is not completed, show notification only for interactive
        toast.success(
          t('courseContent.interactiveCompleted', 'Interactive exercise completed successfully!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      }
    },
    [t, checkContentCompletion]
  );

  // Function to be called when video is completed
  const handleVideoEnd = useCallback(
    (videoId) => {
      // Update video completion status
      setCompletedBlocks((prev) => ({
        ...prev,
        video: {
          ...prev.video,
          [videoId]: true,
        },
      }));

      // Check content completion status
      const isAllContentCompleted = checkContentCompletion();
      if (isAllContentCompleted) {
        setIsContentCompleted(true);

        // Show general notification when all content is completed
        toast.success(t('courseContent.contentCompleted', 'All content completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      } else {
        // Show notification for video
        toast.success(t('courseContent.videoCompleted', 'Video completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      }
    },
    [t, checkContentCompletion]
  );

  // New useEffect to monitor location changes and reset states
  useEffect(() => {
    // Reset states
    setCompletedVideos({});
    setIsContentCompleted(false);
    setIsVideoCompleted(false);
    setIsQuizCompleted(false);
    setHasInteractivePlayground(false);
    setHasForm(false);
    setHasDallEUsecase(false);
    setIsDallECompleted(false);
    setQuizScore(null);

    // Reset completion status of all content blocks
    const cleanCompletedBlocks = {
      video: {},
      quiz: {},
      form: {},
      usecase: {},
      interactive: {},
      text: {},
      pdf: {},
    };

    setCompletedBlocks(cleanCompletedBlocks);

    // Reset video player key
    const timestamp = new Date().getTime();
    setVideoPlayerKey(timestamp);
  }, [location.key]);

  if (loading && !course) {
    return (
      <Box className="topic-view__loading">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="topic-view__error">
        <Alert severity="error">
          {t('course.errors.loading', 'Error loading course')}: {error}
        </Alert>
      </Box>
    );
  }

  if (!currentTopic) {
    return (
      <Box className="topic-view__not-found">
        <Alert severity="info">{t('course.errors.noTopic', 'Topic not found')}</Alert>
      </Box>
    );
  }

  // Calculate if Previous button should be enabled
  // Use localizedCourse instead of course (like other navigation functions do)
  const hasValidNavigation =
    localizedCourse?.chapters &&
    Array.isArray(localizedCourse.chapters) &&
    localizedCourse.chapters.length > 0;

  const alternativeHasPrevious =
    hasValidNavigation &&
    (() => {
      // Find current chapter
      const chapterIndex = localizedCourse.chapters.findIndex((ch) => ch._id === chapterId);
      if (chapterIndex === -1) return false;

      // Find current topic
      const chapter = localizedCourse.chapters[chapterIndex];
      if (!chapter?.topics || !Array.isArray(chapter.topics)) return false;

      const topicIndex = chapter.topics.findIndex((t) => t._id === topicId);
      if (topicIndex === -1) return false;

      // Has previous if not first topic of first chapter
      return !(chapterIndex === 0 && topicIndex === 0);
    })();

  // Navigation Footer button text and style
  const getNextButtonProps = () => {
    // Check if this is the last topic using hook's function
    const isLastTopicInCourse = isLastTopicFn?.(chapterId, topicId) || false;

    // Disable button during progress update
    if (isUpdatingProgress) {
      return {
        text: t('common.updating', 'Updating...'),
        variant: 'contained',
        color: 'primary',
        disabled: true,
        tooltip: t('courseContent.pleaseWait', 'Please wait while we update your progress...'),
        sx: { minWidth: '100px' },
      };
    }

    // Allow direct transition if topic is completed
    if (isTopicCompleted) {
      return {
        text: isLastTopicInCourse
          ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
          : t('course.navigation.next', 'Next'),
        variant: 'contained',
        color: 'primary',
        disabled: false,
        tooltip: '',
        sx: isLastTopicInCourse
          ? {
              minWidth: '180px',
              textTransform: 'uppercase',
              fontWeight: 600,
            }
          : { minWidth: '100px' },
      };
    }

    // IMPORTANT: Check form and quiz in the last topic
    // Check form and quiz completion status - we'll be more flexible in the last topic
    if (isLastTopicInCourse) {
      // Check content types
      const contentTypes = new Set();

      // Find all blocks containing form and quiz
      const formBlocks = [];
      const quizBlocks = [];

      if (currentTopic?.contentBlocks) {
        // Detect content types and completion status
        currentTopic.contentBlocks.forEach((block) => {
          let blockType = block.type;

          // Check usecase type
          if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
            blockType = 'usecase';
            contentTypes.add('usecase');
          }

          // Check interactive type
          if (
            block.playground_type &&
            block.playground_type !== 'none' &&
            blockType !== 'interactive'
          ) {
            blockType = 'interactive';
            contentTypes.add('interactive');
          }

          // Check form content
          if (blockType === 'form') {
            contentTypes.add('form');
            formBlocks.push(block);
          }

          // Check quiz content
          if (blockType === 'quiz') {
            contentTypes.add('quiz');
            quizBlocks.push(block);
          }

          if (blockType === 'video') {
            contentTypes.add('video');
          }

          if (blockType === 'text') contentTypes.add('text');
          if (blockType === 'pdf') contentTypes.add('pdf');
        });
      }

      // Form and quiz check - check if all form and quiz blocks are completed
      const hasForm = contentTypes.has('form');
      const hasQuiz = contentTypes.has('quiz');

      // Form check - is there at least one form and are all forms completed?
      const isFormCompleted =
        !hasForm ||
        (formBlocks.length > 0 &&
          formBlocks.every((block) => completedBlocks.form[block._id] === true));

      // Quiz check - is there at least one quiz and are all quizzes passed?
      const isQuizCompleted =
        !hasQuiz ||
        (quizBlocks.length > 0 &&
          quizBlocks.every((block) => {
            const passingScore =
              block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;
            // Always pass if passingScore is 0
            if (passingScore === 0) return true;

            // Check quiz completion status
            return completedBlocks.quiz[block._id]?.isPassing === true;
          }));

      // Enable button if there are no forms and quizzes or if they are completed
      if (isFormCompleted && isQuizCompleted) {
        return {
          text: t('course.navigation.completeCourse', 'COMPLETE COURSE'),
          variant: 'contained',
          color: 'primary',
          disabled: false,
          tooltip: '',
          sx: {
            minWidth: '180px',
            textTransform: 'uppercase',
            fontWeight: 600,
          },
        };
      }
    }

    // Check if content is completed - if isContentCompleted is TRUE, user can click Next button
    if (isContentCompleted) {
      return {
        text: isLastTopicInCourse
          ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
          : t('course.navigation.next', 'Next'),
        variant: 'contained',
        color: 'primary',
        disabled: false,
        tooltip: '',
        sx: isLastTopicInCourse
          ? {
              minWidth: '180px',
              textTransform: 'uppercase',
              fontWeight: 600,
            }
          : { minWidth: '100px' },
      };
    }

    // Special case check: Enable button if there is only PDF and Text content
    // Group content blocks by type
    const contentTypes = new Set();

    if (currentTopic?.contentBlocks) {
      currentTopic.contentBlocks.forEach((block) => {
        let blockType = block.type;

        // Check usecase type
        if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
          blockType = 'usecase';
          contentTypes.add('usecase');
        }

        // Check interactive type
        if (
          block.playground_type &&
          block.playground_type !== 'none' &&
          blockType !== 'interactive'
        ) {
          blockType = 'interactive';
          contentTypes.add('interactive');
        }

        if (blockType === 'form') contentTypes.add('form');
        if (blockType === 'quiz') contentTypes.add('quiz');
        if (blockType === 'video') contentTypes.add('video');
        if (blockType === 'text') contentTypes.add('text');
        if (blockType === 'pdf') contentTypes.add('pdf');
      });
    }

    // Special case: If there is only text and/or pdf content and no other content types
    const hasOnlyTextAndPdf =
      contentTypes.size > 0 && [...contentTypes].every((type) => type === 'text' || type === 'pdf');

    if (hasOnlyTextAndPdf) {
      return {
        text: isLastTopicInCourse
          ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
          : t('course.navigation.next', 'Next'),
        variant: 'contained',
        color: 'primary',
        disabled: false,
        tooltip: '',
        sx: isLastTopicInCourse
          ? {
              minWidth: '180px',
              textTransform: 'uppercase',
              fontWeight: 600,
            }
          : { minWidth: '100px' },
      };
    }

    // Check incomplete content status for tooltip message
    let tooltipMessage = t(
      'course.navigation.tooltips.completeAllContent',
      'You must complete all required content to proceed.'
    );

    return {
      text: isLastTopicInCourse
        ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
        : t('course.navigation.next', 'Next'),
      variant: 'contained',
      color: 'primary',
      disabled: true,
      tooltip: tooltipMessage,
      sx: isLastTopicInCourse
        ? {
            minWidth: '180px',
            textTransform: 'uppercase',
            fontWeight: 600,
          }
        : { minWidth: '100px' },
    };
  };

  return (
    <Box className={`topic-view ${isTransitioning ? `sliding-${slideDirection}` : ''}`}>
      <Box className="topic-view__sidebar">
        <TopicSidebar
          key={sidebarKey}
          course={course}
          currentChapterIndex={chapterId}
          currentTopicIndex={topicId}
          progress={progress}
          currentProgress={currentProgress}
          isMobileSidebarOpen={isMobileSidebarOpen}
          onToggleSidebar={toggleMobileSidebar}
          onTopicClick={(chIdx, tIdx) => {
            const nextUrl = getTopicUrl(courseId, chIdx, tIdx);
            navigate(nextUrl);
          }}
        />
      </Box>

      <Box
        className={`topic-view__main ${isMobileSidebarOpen ? 'topic-view__main--sidebar-open' : ''}`}
      >
        <Box
          className={`topic-view__title-container ${isMobileSidebarOpen ? 'topic-view__title-container--sidebar-open' : ''}`}
        >
          {/* Completion icon - left side */}
          {isTopicCompleted && (
            <Paper className="topic-view__completion-message">
              <span className="completion-message__icon">✓</span>
            </Paper>
          )}

          {/* TopicHeader with title - center */}
          <Box className="topic-view__title-wrapper">
            <TopicHeader topic={currentTopic} onToggleSidebar={toggleMobileSidebar} />
          </Box>

          {/* Navigation actions - right side */}
          <Box className="topic-view__actions">
            <Box className="topic-view__actions-wrapper"></Box>
            <NavigationFooter
              hasPrevious={alternativeHasPrevious}
              hasNext={true}
              onPrevious={navigateToPreviousContent}
              onNext={handleNextClick}
              courseProgress={progress}
              isContentRequired={hasInteractivePlayground && !isTopicCompleted}
              isContentCompleted={isContentCompleted}
              quizScore={quizScore}
              quizPassingScore={quizPassingScore}
              nextButtonProps={{
                text: getNextButtonProps().text,
                variant: getNextButtonProps().variant,
                color: getNextButtonProps().color,
                disabled: getNextButtonProps().disabled,
                tooltip: getNextButtonProps().tooltip,
                className: `navigation-footer__button ${getNextButtonProps().text === t('course.navigation.completeCourse', 'COMPLETE COURSE') ? 'navigation-footer__button--complete' : ''}`,
              }}
            />
          </Box>
        </Box>

        <Box className="topic-view__content">
          <TopicContent
            key={videoPlayerKey}
            topic={currentTopic}
            courseId={courseId}
            chapterIndex={chapterId}
            topicIndex={topicId}
            currentTime={currentTime}
            onVideoProgress={null}
            onVideoEnded={handleVideoEnd}
            onQuizComplete={handleQuizComplete}
            onTimeUpdate={(time) => setCurrentTime(time)}
            onQuizScoreChange={(quizId, score, passingScore) => {
              setQuizScore(score);
              setQuizPassingScore(passingScore);
            }}
            isTopicCompleted={isTopicCompleted}
            onUsecaseGenerate={handleImageGenerate}
            onContentCompleted={(blockType, blockId) => {
              // Mark content as completed
              setIsContentCompleted(true);

              // If blockType and blockId parameters are provided, mark the relevant block as completed
              if (blockType && blockId) {
                // Update completedBlocks for specific block type
                setCompletedBlocks((prev) => {
                  // Update if there is a state for the specified block type
                  if (prev[blockType]) {
                    return {
                      ...prev,
                      [blockType]: {
                        ...prev[blockType],
                        [blockId]: true,
                      },
                    };
                  }

                  // Return default state if there is no state for the specified block type
                  return prev;
                });
              }
            }}
            onFormSubmit={handleFormSubmit}
            onInteractiveComplete={handleInteractiveContentComplete}
          />

          {/* Mobile Navigation Footer - Only visible on mobile */}
          <Box className="topic-view__mobile-navigation">
            <NavigationFooter
              hasPrevious={alternativeHasPrevious}
              hasNext={true}
              onPrevious={navigateToPreviousContent}
              onNext={handleNextClick}
              courseProgress={progress}
              isContentRequired={hasInteractivePlayground && !isTopicCompleted}
              isContentCompleted={isContentCompleted}
              quizScore={quizScore}
              quizPassingScore={quizPassingScore}
              nextButtonProps={{
                text: getNextButtonProps().text,
                variant: getNextButtonProps().variant,
                color: getNextButtonProps().color,
                disabled: getNextButtonProps().disabled,
                tooltip: getNextButtonProps().tooltip,
                className: `navigation-footer__button ${getNextButtonProps().text === t('course.navigation.completeCourse', 'COMPLETE COURSE') ? 'navigation-footer__button--complete' : ''}`,
              }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

TopicView.propTypes = {
  courseId: PropTypes.string.isRequired,
  chapterId: PropTypes.string.isRequired,
  topicId: PropTypes.string.isRequired,
  onCourseComplete: PropTypes.func,
};

export default TopicView;
