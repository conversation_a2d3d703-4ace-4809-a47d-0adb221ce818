@use '../../../styles/abstracts/variables' as *;

.content-block {
  &__form {
    display: flex;
    flex-direction: column;
    gap: $spacing-4;
    background-color: $bg-paper !important;
    border: 1px solid $border-color !important;
    border-radius: $border-radius-md !important;
    padding: $spacing-4 !important;
  }

  &__form-card {
    background-color: none !important;
    border:none !important;
    border-radius: none !important;
    box-shadow:none !important;
    padding: 0 !important;
    margin:0 !important;
  }

  &__form-card-title {
    font-size: calc($font-size-md + 1px) !important;
    font-weight: $font-weight-bold !important;
    border-bottom:none !important;
  }

  &__form-card-content {
    padding:0 !important;
  }
  &__form-button {
    width: fit-content !important;
    margin:0 0 0 auto !important;
    background-color: $primary-color !important;
    color: $text-light !important;
    border-radius: $border-radius-md !important;
    padding: $spacing-2 $spacing-4 !important;
    text-transform: none !important;
    font-weight: $font-weight-bold !important;
    font-size: calc($font-size-md - 1px) !important;
    &:hover {
      background-color: $primary-color-dark !important;
    }
  }
  &__form-feedback {
    width: 100% !important;
    margin-bottom: 0 !important;
    .content-block__form-feedback-title {
        font-size: calc($font-size-md + 1px) !important;
        font-weight: $font-weight-bold !important;
        border-bottom:none !important;
        margin-bottom:0 !important;
    }
    .MuiGrid-container {
      display: flex !important;
      flex-wrap: wrap !important;
      width: 100% !important;
      margin: 0 -8px !important; /* Negatif margin ile item'lardaki padding'i dengele */
    }
    
    .MuiGrid-item {
      padding: 8px !important;
      
      @media (max-width: 600px) {
        flex: 0 0 33.33% !important; /* 3 buttons per row on mobile */
        max-width: 33.33% !important;
      }
      
      @media (min-width: 601px) and (max-width: 960px) {
        flex: 0 0 20% !important; /* 5 buttons per row on tablets */
        max-width: 20% !important;
      }
      
      @media (min-width: 961px) {
        flex: 0 0 9.09% !important; /* 11 buttons per row on desktop */
        max-width: 9.09% !important;
      }
    }
    
    button {
      width: 100% !important;
      min-width: 40px !important;
      height: 40px !important;
    }
  }
  &__quiz-container{
    padding-top: 0 !important;
  }
}

// Text content içinde gömülü video için stiller
.text-content__embedded-video {
  .topic-content__video-container {
    position: relative;
    background: #000;
    border-radius: $border-radius-md;
    overflow: hidden;
    margin: $spacing-2 0;
    
    // Enhanced video player için aspect ratio
    &.enhanced-video-container::before {
      content: '';
      display: block;
      padding-top: 56.25%; // 16:9 aspect ratio
    }
    
    .enhanced-video-player {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    // Iframe container için özel stiller - aspect ratio zaten JSX'te ayarlı
    &.iframe-video-container {
      background: transparent; // iframe için background gerekmez
      
      iframe {
        border-radius: $border-radius-md;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        
        // Loading state için placeholder
        &:not([src]) {
          background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
          background-size: 20px 20px;
          background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
          animation: loading 1s linear infinite;
        }
        
        @keyframes loading {
          0% { background-position: 0 0, 0 10px, 10px -10px, -10px 0px; }
          100% { background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px; }
        }
      }
    }
  }
  
  // Video başlığı stili
  h6 {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    
    &::before {
      content: '▶';
      font-size: 0.9em;
    }
  }
}

/* Dışarda özel sınıf tanımlayarak spesifik ağırlığı artırıyoruz */
.content-block .MuiFormControlLabel-root.content-block__form-control-label {
  .MuiFormControlLabel-label {
    font-size: 16px !important;
  }
}

/* Daha spesifik seçici ile diğer element seçimlerini de ele alıyoruz */
.content-block .MuiFormGroup-root .content-block__form-control-label .MuiTypography-root {
  font-size: 16px !important;
  color: rgba(0, 0, 0, 0.87) !important;
}

.content-block .MuiRadioGroup-root .content-block__form-control-label .MuiTypography-root {
  font-size: 16px !important;
  color: rgba(0, 0, 0, 0.87) !important;
}

/* En spesifik seçici */
.content-block__form .MuiFormControlLabel-root.content-block__form-control-label span.MuiTypography-root {
  font-size: 16px !important;
  color: rgba(0, 0, 0, 0.87) !important;
}
.content-block__form-feedback-title{
  font-size: 17px !important;
    font-weight: 700 !important;
    border-bottom: none !important;
    margin-bottom: 0 !important;
}