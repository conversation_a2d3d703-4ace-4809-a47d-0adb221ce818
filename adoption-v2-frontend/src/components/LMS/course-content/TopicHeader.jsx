import { Box, Typography, IconButton } from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';
import PropTypes from 'prop-types';

const TopicHeader = ({ topic, onToggleSidebar }) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
      {/* Mobile Sidebar Toggle Button */}
      {onToggleSidebar && (
        <IconButton
          onClick={onToggleSidebar}
          size="small"
          sx={{
            display: 'flex',
            '@media (min-width: 768px)': {
              display: 'none',
            },
          }}
          aria-label="Toggle sidebar"
        >
          <MenuIcon />
        </IconButton>
      )}

      <Typography variant="h4" className="topic-view__title" component="h1" sx={{ flex: 1 }}>
        {topic?.title}
      </Typography>
    </Box>
  );
};

TopicHeader.propTypes = {
  topic: PropTypes.object,
  onToggleSidebar: PropTypes.func,
};

export default TopicHeader;
