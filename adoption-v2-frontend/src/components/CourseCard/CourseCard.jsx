import React from 'react';
import PropTypes from 'prop-types';
import Button from '@/components/Button/Button.jsx';
import './CourseCard.scss';
import { Box, Typography, Tooltip, Chip } from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ArticleIcon from '@mui/icons-material/Article';
import { useNavigate } from 'react-router-dom';

const CardContent = ({
  title,
  description,
  buttonText,
  buttonURL,
  newTab,
  className,
  buttonType,
  buttonVariant = 'contained',
  imageSrc,
  tooltipText,
  locked,
  cardNumber,
  courseNumber,
  duration,
  percent,
  variant,
  onCardClick,
  label,
}) => {
  // Only show metadata if not locked and either courseNumber > 0 or duration is valid
  const hasMetadata =
    !locked && ((courseNumber && courseNumber > 0) || (duration && duration !== 'N/A'));

  const getProgressStatus = () => {
    if (percent === 100) {
      return { label: 'Completed', color: 'success' };
    } else if (percent > 0) {
      return { label: `In Progress: ${percent}%`, color: 'primary' };
    }
    return null;
  };

  const progressStatus = getProgressStatus();

  const handleButtonClick = (e) => {
    e.stopPropagation(); // Buton tıklamasını kart tıklamasından ayırmak için
  };

  return (
    <Box className={`card-content ${className || ''}`}>
      {imageSrc && (
        <Box className={`card-image ${variant ? `variant-${variant}` : ''}`}>
          {progressStatus && (
            <Box className="progress-status">
              <Chip
                label={progressStatus.label}
                color={progressStatus.color}
                size="small"
                className="progress-chip"
              />
            </Box>
          )}
          <img src={imageSrc} alt={title} />
          {locked && (
            <Box className="card-lock">
              <LockIcon />
            </Box>
          )}
        </Box>
      )}

      {label && (
        <Chip
          label={label}
          size="small"
          color="primary"
          className="card-label"
          variant="outlined"
        />
      )}

      <Typography variant="h3" className="card-title">
        {title}
      </Typography>
      <Typography
        variant="body1"
        className={`card-description ${hasMetadata ? 'has-metadata' : ''} ${locked ? 'locked' : ''}`}
      >
        {description}
      </Typography>

      {!locked && hasMetadata && (
        <Box className="card-metadata">
          {courseNumber && courseNumber > 0 && (
            <Box className="metadata-item">
              <ArticleIcon />
              <Typography variant="caption">Course : {courseNumber}</Typography>
            </Box>
          )}
          {duration && duration !== 'N/A' && (
            <Box className="metadata-item">
              <AccessTimeIcon />
              <Typography variant="caption">Duration : {duration}</Typography>
            </Box>
          )}
        </Box>
      )}

      {!locked && (
        <Button
          variant={buttonVariant}
          color="primary"
          className="card-button"
          href={buttonURL}
          target={newTab ? '_blank' : '_self'}
          rel={newTab ? 'noopener noreferrer' : ''}
          onClick={handleButtonClick}
        >
          {buttonText}
        </Button>
      )}

      {cardNumber && <Typography className="card-number">{cardNumber}</Typography>}
    </Box>
  );
};

const CourseCard = ({
  children,
  buttonText,
  buttonType,
  buttonVariant = 'contained',
  imageSrc,
  tooltipText,
  locked,
  cardNumber,
  courseNumber,
  duration,
  percent,
  variant,
  onClick,
  label,
  className,
}) => {
  const navigate = useNavigate();

  // CardContent'ten buttonURL'yi almak için klon özelliklerini kontrol edelim
  let buttonURL = '';
  let newTab = false;

  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child) && child.type === CardContent) {
      buttonURL = child.props.buttonURL || '';
      newTab = child.props.newTab || false;
    }
  });

  const handleCardClick = () => {
    if (locked) return; // Kart kilitliyse tıklama işlemini engelle

    // Özel onClick fonksiyonu varsa onu çağır
    if (onClick) {
      onClick();
      return;
    }

    if (!buttonURL) return; // URL yoksa tıklama işlemini engelle

    if (newTab) {
      window.open(buttonURL, '_blank', 'noopener,noreferrer');
    } else {
      navigate(buttonURL);
    }
  };

  const cardContent = (
    <Box
      className={`carousel-card ${locked ? 'locked' : ''} ${!locked ? 'clickable' : ''}`}
      onClick={!locked ? handleCardClick : undefined}
      tabIndex={!locked ? 0 : undefined}
      role={!locked ? 'button' : undefined}
      aria-label={!locked ? 'Kursa git' : undefined}
      onKeyDown={(e) => {
        if (!locked && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault();
          handleCardClick();
        }
      }}
      style={{ cursor: !locked ? 'pointer' : 'default' }}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            buttonType,
            buttonText,
            buttonVariant,
            imageSrc,
            tooltipText,
            locked,
            cardNumber,
            courseNumber,
            duration,
            percent,
            variant,
            onCardClick: handleCardClick,
            label,
            className,
          });
        }
        return child;
      })}
    </Box>
  );

  return locked && tooltipText ? (
    <Tooltip
      title={tooltipText}
      arrow
      placement="top"
      componentsProps={{
        tooltip: {
          className: 'course-card-tooltip',
        },
      }}
    >
      {cardContent}
    </Tooltip>
  ) : (
    cardContent
  );
};

const propTypes = {
  buttonText: PropTypes.string.isRequired,
  buttonType: PropTypes.oneOf(['URL', 'File', 'Selected Content', 'MODAL']).isRequired,
  buttonVariant: PropTypes.oneOf(['contained', 'outlined', 'text']),
  imageSrc: PropTypes.string,
  tooltipText: PropTypes.string,
  locked: PropTypes.bool,
  cardNumber: PropTypes.number,
  courseNumber: PropTypes.number,
  duration: PropTypes.string,
  percent: PropTypes.number,
  variant: PropTypes.oneOf(['bigThumbnail']),
  onClick: PropTypes.func,
  label: PropTypes.string,
  className: PropTypes.string,
};

CourseCard.propTypes = {
  ...propTypes,
  children: PropTypes.node.isRequired,
};

CardContent.propTypes = {
  ...propTypes,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  buttonURL: PropTypes.string.isRequired,
  newTab: PropTypes.bool,
  onCardClick: PropTypes.func,
};

export { CardContent };
export default CourseCard;
