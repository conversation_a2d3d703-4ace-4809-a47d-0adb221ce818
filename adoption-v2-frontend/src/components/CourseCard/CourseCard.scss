@use '../../styles/abstracts/variables' as *;

.carousel-card {
  border-radius: $border-radius-md;
  height: 100%;
  display: flex;
  box-shadow: $shadow-sm;
  flex-direction: column;
  overflow: hidden;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF;
  border: 1px solid $divider-color;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-md;

    .card-number {
      color: rgba($primary-color, 0.2);
    }
  }

  &.clickable {
    position: relative;
    
    &:focus-visible {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
    }
    
    // Butonun üzerine gelince hover efektini engellemek için
    .card-button {
      z-index: 2;
      position: relative;
    }
  }

  &.locked {
    cursor: not-allowed;
    opacity: 0.7;

    .card-image {
      position: relative;

      .card-lock {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display:flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 42px;
        height: 42px;
        padding: $spacing-2;
        
        .MuiSvgIcon-root {
          color: white;
          font-size: 24px;
        }
      }
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: $spacing-4 $spacing-4 $spacing-4 $spacing-4;
    position: relative;

    .card-label {
      margin-bottom: $spacing-2;
      align-self: flex-start;
      font-size: 12px;
      height: 24px;
      border-radius: $border-radius-sm;
      border:none !important;
      padding:0 !important;
      margin:0 0 0 0 !important;
      letter-spacing: 0.5px;
      color: $text-secondary !important;
      font-weight: $font-weight-medium;
      span{
        padding:0 !important;
        margin:-12px 0 0 0 !important;
      }
      &.MuiChip-outlined {
        border-color: $primary-color;
        color: $primary-color;
        padding:0 !important;
        margin:0 !important;
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: $text-primary;
      margin: 0 0 $spacing-1 0;
      line-height: 1.4;
    }

    .card-description {
      font-size: 14px;
      color: $text-secondary;
      line-height: 1.6;
      margin: 0 0 $spacing-4 0;
      flex: 1;
      &.locked{
        margin-bottom:0;
        min-height: max-content;
      }
      &.has-metadata {
        margin-bottom: 0;
      }
    }

    .card-image {
      width: 100%;
      height: 124px;
      object-fit: cover;
      margin-top:0;
      margin-bottom: $spacing-3;
      border-radius: $border-radius-sm;
      overflow: hidden;
      position: relative;
      
      &.variant-bigThumbnail {
        height: 200px !important;
        
        img {
          height: 200px !important;
        }
      }
      
      img {
        width: 100%;
        height: 124px;
        object-fit: cover;
        display: block;
      }

      .progress-status {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        
        .progress-chip {
          font-size: 12px;
          border-radius:0px !important;
          border-bottom-right-radius: $border-radius-sm !important;
          height: 24px;
          
          &.MuiChip-colorSuccess {
            background-color: #4caf50;
            color: white;
          }
          
          &.MuiChip-colorPrimary {
            background-color: #1976d2;
            color: white;
          }
        }
      }
    }

    .card-metadata {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin: 16px 0;
      color: var(--text-secondary);

      .metadata-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        svg {
          font-size: 18px;
          opacity: 0.7;
        }

        .MuiTypography-root {
          font-size: 12px !important;
          color: $text-secondary;
          line-height: 1.2;
        }
      }
    }

    .card-number {
      position: absolute;
      bottom: 16px;
      right: 16px;
      font-size: 64px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.1);
      z-index: 0;
      transition: color 0.3s ease;
    }
  }

  @media (max-width: 768px) {

    .card-content {
      padding: $spacing-2;

      .card-title {
        font-size: 16px;
      }

      .card-description {
        font-size: 13px;
        margin-bottom: $spacing-4;
      }
    }
    .card-content.mscopilot-container {
        padding:$spacing-4 !important;
      }
  }
}

.MuiTooltip-popper {
  .course-card-tooltip {
    background-color: $primary-color;
    font-size: $font-size-xs;
    padding: $spacing-2 $spacing-3;
    border-radius: $border-radius-sm;
    font-weight: $font-weight-regular;
    
    &.MuiTooltip-tooltipPlacementTop {
      .MuiTooltip-arrow {
        color: $primary-color;
      }
    }
  }
} 